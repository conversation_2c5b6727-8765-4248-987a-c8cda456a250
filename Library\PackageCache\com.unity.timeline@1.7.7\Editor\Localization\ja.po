msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-06-19 12:56+0900\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: po-conv\n"

#: Editor/Animation/BindingTreeViewDataSource.cs:1
msgid " (Default Value)"
msgstr " (デフォルト値)"

#: Editor/Manipulators/Sequence/EaseClip.cs:1
msgid " Duration {0:0.00;-0.00} frames "
msgstr "　期間 {0:0.00;-0.00} フレーム "

#: Editor/Manipulators/Sequence/EaseClip.cs:1
msgid " Duration {0:0.00;-0.00} s "
msgstr " 期間 {0:0.00;-0.00} s "

#: Editor/Manipulators/Sequence/EaseClip.cs:1
msgid "({0:+0.00;-0.00} frames)"
msgstr "({0:+0.00;-0.00} フレーム)"

#: Editor/Manipulators/Sequence/EaseClip.cs:1
msgid "({0:+0.00;-0.00} s)"
msgstr "({0:+0.00;-0.00} s)"

#: Editor/Utilities/DisplayNameHelper.cs:1
msgid "<No Asset>"
msgstr "<アセットなし>"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "A prefab to instantiate as a child object of the source game object"
msgstr "ソースゲームオブジェクトの子オブジェクトとして生成されるプレハブ"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "A random seed to provide the particle systems for consistent previews. This will only be used on particle systems where AutoRandomSeed is on."
msgstr "再現性のあるプレビューのためのパーティクルシステム用のランダムシード。AutoRandomSeedがオンになっているパーティクルシステムにのみ使用されます。"

#: [DisplayName]./Runtime/Activation/ActivationPlayableAsset.cs:1
msgid "Activation Clip"
msgstr "アクティベーションクリップ"

#: Editor/Activation/ActivationTrackEditor.cs:1
msgid "Active"
msgstr "アクティブ"

#: Editor/treeview/TimelineDragging.cs:1
msgid "Add Clip With {0}"
msgstr "{0}でクリップを追加"

#: Editor/Actions/Menus/TimelineContextMenu.cs:1
msgid "Add From {1}"
msgstr "{1}から追加"

#: Editor/Recording/AnimationTrackRecorder.cs:1
msgid "Add Key"
msgstr "キー追加"

#: Editor/Actions/Menus/TimelineContextMenu.cs:1
msgid "Add Layer"
msgstr "レイヤーを追加"

#: [MenuEntry]./Editor/Animation/AnimationTrackActions.cs:1
msgid "Add Override Track"
msgstr "オーバーライドトラックを追加"

#: Editor/Signals/Styles.cs:1
msgid "Add Reaction"
msgstr "リアクションを追加"

#: Editor/Signals/Styles.cs:1
msgid "Add Signal Receiver"
msgstr "シグナルレシーバーを追加"

#: Editor/Signals/Styles.cs:1
msgid "Add Signal Receiver Reaction"
msgstr "シグナルレシーバー反応を追加"

#: Editor/DirectorStyles.cs:1
msgid "Add new tracks."
msgstr "新しいトラックの作成"

#: Editor/treeview/TimelineDragging.cs:1
msgid "Add {0}"
msgstr "{0}を追加"

#: Editor/Actions/Menus/TimelineContextMenu.cs:1
msgid "Add {0} From {1}"
msgstr "{1}から{0}を追加"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Advanced"
msgstr "上級者向け"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Allow Audio Scrubbing"
msgstr "オーディオスクラブ許可"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Allow the users to hear audio while scrubbing on audio clip."
msgstr "スクラブ中もオーディオクリップの音が聞こえるようにする。"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Anim Clip Property"
msgstr "アニメーションクリッププロパティ"

#: Editor/Animation/CurveDataSource.cs:1
msgid "Animated Values"
msgstr "アニメーションで更新された値"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Animation Clip"
msgstr "アニメーションクリップ"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Animation Clip Name"
msgstr "アニメーションクリップ名"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Animation Extrapolation"
msgstr "アニメーション外挿"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Apply Avatar Mask Property"
msgstr "アバターマスクプロパティに適用"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Apply Foot IK"
msgstr "脚部IKに適用"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Applying an Avatar Mask to the base track may not properly mask Root Motion or Humanoid bones from an Animator Controller or other Timeline track."
msgstr "アバターマスクをベーストラックに適用すると、アニメーターコントローラや他のタイムライントラックからルートモーションやヒューマノイドボーンを適切にマスクできない場合があります。"

#: [MenuItem]./Editor/Signals/SignalAssetInspector.cs:1
msgid "Assets"
msgstr "アセット"

#: [DisplayName]./Runtime/Audio/AudioPlayableAsset.cs:1
msgid "Audio Clip"
msgstr "オーデイオクリップ"

#: Editor/Audio/AudioClipPropertiesDrawer.cs:1
msgid "AudioSource: {0}"
msgstr "オーディオソース: {0}"

#: Editor/Audio/AudioTrackInspector.cs:1
msgid "AudioSource: {2}"
msgstr "オーディオソース: {2}"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Auto will apply scene offsets if there is a controller attached to the animator and transform offsets otherwise."
msgstr "自動はアニメーターにアタッチされたコントローラがある場合にはシーンオフセットを適用し、そうでなければトランスフォームのオフセットになります。"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Avatar Mask Property"
msgstr "アバターマスクプロパティ"

#: Editor/TimelineHelpers.cs:1
msgid "Bind Track"
msgstr "バインドトラック"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Blend Curves"
msgstr "ブレンド曲線"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Blend In Curve"
msgstr "イン曲線でブレンド"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Blend In Duration"
msgstr "ブレンドイン期間"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Blend Out Curve"
msgstr "アウト曲線でブレンド"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Blend Out Duration"
msgstr "ブレンドアウト期間"

#: Editor/Audio/AudioClipPropertiesDrawer.cs:1
msgid "Calculated from:\n"
msgstr "計算元:\n"

#: Editor/treeview/TimelineDragging.cs:1
msgid "Cancel"
msgstr "キャンセル"

#: Editor/Utilities/StyleManager.cs:1
msgid "Cannot find style {0} for {1}"
msgstr "{1}用のスタイル{0}が見つかりません"

#: Editor/Window/TimelineWindow_Duration.cs:1
msgid "Change Duration"
msgstr "期間変更"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Clip In"
msgstr "クリップイン"

#: Editor/Animation/CurveDataSource.cs:1
msgid "Clip Properties"
msgstr "クリップのプロパティ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Clip Timing"
msgstr "クリップタイミング"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Clip Transform Offsets"
msgstr "クリップトランスフォームオフセット"

#: Editor/Audio/AudioClipPropertiesDrawer.cs:1
msgid "Clip: {1}\n"
msgstr "クリップ: {1}\n"

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
#, fuzzy
msgid "Collapse Track Markers"
msgstr "トラックマーカーの展開/折りたたみ"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Control Activation"
msgstr "コントロールアクティベーション"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Control Children"
msgstr "子の制御"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Control ITimeControl"
msgstr "ITimeControlの制御"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Control Particle Systems"
msgstr "パーティクルシステム制御"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Control Playable Directors"
msgstr "プレイアブルディレクターの制御"

#: Editor/Extensions/AnimationTrackExtensions.cs:1
msgid "Convert From Clip"
msgstr "クリップから変換"

#: Editor/Extensions/AnimationTrackExtensions.cs:1
msgid "Convert To Clip"
msgstr "クリップに変換"

#: [MenuEntry]./Editor/Animation/AnimationTrackActions.cs:1
msgid "Convert To Clip Track"
msgstr "クリップトラックへ変換"

#: [MenuEntry]./Editor/Animation/AnimationTrackActions.cs:1
msgid "Convert To Infinite Clip"
msgstr "無限クリップへ変換"

#: [MenuEntry]./Editor/Actions/TimelineActions.cs:1
msgid "Copy"
msgstr "コピー"

#: Editor/CustomEditors/TrackEditor.cs:1
msgid "Could not find appropriate component on this gameObject"
msgstr "このゲームオブジェクトに適切なコンポーネントがありません"

#: [MenuItem]./Editor/Signals/SignalAssetInspector.cs:1
msgid "Create"
msgstr "作成"

#: Editor/TimelineHelpers.cs:1
msgid "Create Clip"
msgstr "クリップ作成"

#: Editor/Signals/Styles.cs:1
msgid "Create New Signal Asset"
msgstr "新しいシグナルアセットを作成する"

#: Editor/Signals/Styles.cs:1
msgid "Create Signal Key"
msgstr "シグナルキーを追加"

#: Editor/Signals/Styles.cs:1
msgid "Create Signal..."
msgstr "シグナルを作成..."

#: Editor/DirectorStyles.cs:1
msgid "Create a new Timeline and Director Component for Game Object"
msgstr "タイムラインとゲームオブジェクト用のディレクターコンポーネントを作成します"

#: Editor/TimelineHelpers.cs:1
msgid "Create clip"
msgstr "クリップ作成"

#: Editor/treeview/TimelineDragging.cs:1
msgid "Create {0} on {1}"
msgstr "{0}を{1}上に作成"

#: Editor/Signals/Styles.cs:1
msgid "Creates a Signal Receiver component on the track binding and the reaction for the current signal."
msgstr "現在のシグナルのためのリアクションとトラックバインドにシグナルレシーバーコンポーネントを作成する"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Curve Editor"
msgstr "カーブエディタ"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Custom"
msgstr "カスタム"

#: [MenuEntry]./Editor/Actions/ClipAction.cs:1
msgid "Custom Actions/Sample Clip Action"
msgstr "カスタムアクション/サンプルクリップアクション"

#: [MenuEntry]./Editor/Actions/TrackAction.cs:1
msgid "Custom Actions/Sample track Action"
msgstr "カスタムアクション/サンプルトラックアクション"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Custom Framerate"
msgstr "カスタムフレームレート"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Custom framerate value used for new Timeline Assets."
msgstr "新しいタイムラインアセットにカスタムフレームレートが使用されます。"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Debug TimeArea"
msgstr "デバッグタイムエリア"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Default Framerate"
msgstr "デフォルトフレームレート"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Default Offset Match Fields"
msgstr "デフォルトオフセットマッチフィールド"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Define scrolling behavior during playback."
msgstr "プレイバック中のスクロール挙動を定義"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Define the time unit for the timeline window (Frames or Seconds)."
msgstr "タイムラインウインドウの時間単位を定義（フレームまたは秒）"

#: Editor/treeview/TrackGui/TimelineTrackErrorGUI.cs:1
msgid "Delete"
msgstr "削除"

#: Editor/Actions/TimelineActions.cs:1
msgid "Delete Items"
msgstr "アイテムの削除"

#: Editor/Signals/Styles.cs:1
msgid "Delete Row"
msgstr "行の削除"

#: Editor/Utilities/TrackModifier.cs:1
msgid "Delete Track"
msgstr "トラック削除"

#: Editor/Window/TimelineWindow_HeaderGui.cs:1
msgid "Display time based on the current timeline."
msgstr "現在のタイムラインの時刻を表示"

#: Editor/Window/TimelineWindow_HeaderGui.cs:1
msgid "Display time based on the master timeline."
msgstr "マスタータイムラインの時刻を表示"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Double Clip Speed"
msgstr "クリップ倍速"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Draw the waveforms for all audio clips."
msgstr "全てのオーディオクリップの波形を表示"

#: Editor/Signals/Styles.cs:1
msgid "Duplicate"
msgstr "複製"

#: Editor/Utilities/MarkerModifier.cs:1
msgid "Duplicate Marker"
msgstr "マーカー複製"

#: Editor/Signals/Styles.cs:1
msgid "Duplicate Row"
msgstr "行の複製"

#: Editor/inspectors/GroupTrackInspector.cs:1
msgid "Duration"
msgstr "期間"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "Duration Mode"
msgstr "期間モード"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "Duration Mode Property"
msgstr "期間モードプロパティ"

#: Editor/Window/TimelineWindow_TimeArea.cs:1
msgid "Duration Mode/{0}"
msgstr "期間モード/{0}"

#: Editor/Manipulators/Sequence/EaseClip.cs:1
msgid "Ease In"
msgstr "イースイン"

#: Editor/Manipulators/Sequence/EaseClip.cs:1
msgid "Ease In Clip"
msgstr "イースインクリップ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Ease In Duration"
msgstr "イーズイン期間"

#: Editor/Manipulators/Sequence/EaseClip.cs:1
msgid "Ease Out"
msgstr "イースアウト"

#: Editor/Manipulators/Sequence/EaseClip.cs:1
msgid "Ease Out Clip"
msgstr "イースアウトクリップ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Ease Out Duration"
msgstr "イーズアウト期間"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Edge Snap"
msgstr "エッジにスナップ"

#: Editor/Animation/CurvesProxy.cs:1
msgid "Edit Clip Curve"
msgstr "クリップカーブ編集"

#: Editor/Recording/TimelineRecording_Monobehaviour.cs:1
msgid "Edit Curve"
msgstr "カーブ編集"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Edit Skin"
msgstr "スキン編集"

#: Editor/State/SequenceHierarchy.cs:1
msgid "Edit Sub-Timeline"
msgstr "サブタイムラインを編集"

#: [MenuEntry]./Editor/Actions/TrackActions.cs:1
msgid "Edit in Animation Window"
msgstr "アニメーションウインドウで編集"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Complete Last Loop"
msgstr "編集/最後のループを完了"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Double Speed"
msgstr "編集/２倍速"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Half Speed"
msgstr "編集/スロー"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Match Duration"
msgstr "編集/期間を合わせる"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Reset All"
msgstr "編集/すべてをリセット"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Reset Duration"
msgstr "編集/リセット期間"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Reset Speed"
msgstr "編集/リセット速度"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Split"
msgstr "編集/分割"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Trim End"
msgstr "編集/トリム終了"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Trim Last Loop"
msgstr "編集/最後のループをトリム"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Editing/Trim Start"
msgstr "編集/トリム開始"

#: Editor/Signals/Styles.cs:1
msgid "Emit Once"
msgstr "一度だけ発生"

#: Editor/Signals/SignalEmitterInspector.cs:1
msgid "Emit Once Property"
msgstr "一度だけ発生プロパティ"

#: Editor/Signals/Styles.cs:1
msgid "Emit Signal"
msgstr "シグナルを発生"

#: Editor/Signals/Styles.cs:1
msgid "Emit the signal once during loops."
msgstr "ループ中に一度だけ発生"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Enable Snap to Frame to manipulate clips and align them on frames."
msgstr "フレームにスナップを有効にし、クリップ操作においてフレームに整列させます。"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Enable the ability to snap clips on the edge of another clip."
msgstr "別のクリップのエッジにスナップする機能を有効にします。"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Enable to apply foot IK to the AnimationClip when the target is humanoid."
msgstr "ターゲットがヒューマノイドの場合にAnimationClipへの脚部IKを有効にします"

#: Editor/DirectorStyles.cs:1
msgid "Enable/disable scene preview mode"
msgstr "シーンプレビューモードの有効/無効"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "End"
msgstr "終了"

#: Editor/Window/TimelineWindow_Duration.cs:1
msgid "End of sequence marker"
msgstr "シーケンスメーカーの終了"

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
#, fuzzy
msgid "End recording"
msgstr "記録中..."

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
#, fuzzy
msgid "Expand Track Markers"
msgstr "トラックマーカーの展開/折りたたみ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Extrapolation used after a clip ends"
msgstr "最後のクリップ後の外挿"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Extrapolation used prior to the first clip"
msgstr "最初のクリップへの外挿"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Fields to apply when matching offsets on clips. The defaults can be set on the track."
msgstr "クリップのオフセットにマッチするときに適用するフィールド。デフォルトはトラックに設定できます。"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Fields to apply when matching offsets on clips. These are the defaults, and can be overridden for each clip."
msgstr "クリップのオフセットにマッチする場合に適用するフィールド。これらはデフォルトで、それぞれのクリップで上書き可能です。"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Film (24)"
msgstr "フィルム(24)"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Film (30)"
msgstr "フィルム(30)"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Film (50)"
msgstr "フィルム(50)"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Film (60)"
msgstr "フィルム(60)"

#: [MenuEntry]./Editor/Actions/ClipsActions.cs:1
msgid "Find Source Asset"
msgstr "ソースアセットを見つける"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Foot IK"
msgstr "脚部IK"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "Frame Rate"
msgstr "フレームレート"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "Frame Rate Property"
msgstr "フレームレートプロパティ"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Frame Rate/Custom"
msgstr "フレームレート/カスタム"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Framerate value used for new Timeline Assets."
msgstr "タイムラインアセットに使用されるフレームレート値"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Frames"
msgstr "フレーム"

#: Editor/Window/TimelineWindow_HeaderGui.cs:1
msgid "Global"
msgstr "グローバル"

#: Editor/State/SequenceHierarchy.cs:1
msgid "Go to Sub-Timeline"
msgstr "サブタイムラインへ移動"

#: Editor/DirectorStyles.cs:1
msgid "Go to the beginning of the timeline (Shift+<)"
msgstr "タイムラインの先頭に移動(Shift+<)"

#: Editor/DirectorStyles.cs:1
msgid "Go to the end of the timeline (Shift+>)"
msgstr "タイムラインの終端に移動(Shift+>)"

#: Editor/DirectorStyles.cs:1
msgid "Go to the next frame"
msgstr "次のフレームに移動"

#: Editor/DirectorStyles.cs:1
msgid "Go to the previous frame"
msgstr "前のフレームに移動"

#: Editor/treeview/Drawers/ClipDrawer.cs:1
msgid "HOLD"
msgstr "ホールド"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Half Clip Speed"
msgstr "クリップスピード半減"

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
msgid "Hide curves view"
msgstr "カーブビューを隠す"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "In"
msgstr "イン"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Inherited"
msgstr "継承されている"

#: Editor/Trackhead.cs:1
msgid "Insert Time"
msgstr "タイムの挿入"

#: Editor/Trackhead.cs:1
#, fuzzy
msgid "Insert/Frame/10 Frames"
msgstr "挿入/フレーム/"

#: Editor/Trackhead.cs:1
#, fuzzy
msgid "Insert/Frame/100 Frames"
msgstr "挿入/フレーム/"

#: Editor/Trackhead.cs:1
#, fuzzy
msgid "Insert/Frame/25 Frames"
msgstr "挿入/フレーム/"

#: Editor/Trackhead.cs:1
#, fuzzy
msgid "Insert/Frame/5 Frames"
msgstr "挿入/フレーム/"

#: Editor/Trackhead.cs:1
msgid "Insert/Frame/Single"
msgstr "挿入/フレーム/シングル"

#: Editor/Trackhead.cs:1
msgid "Insert/Selected Time"
msgstr "挿入/選択された時間"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Inspector"
msgstr "インスペクター"

#: Editor/inspectors/GroupTrackInspector.cs:1
msgid "Invalid Track"
msgstr "無効トラック"

#: [MenuEntry]./Editor/Actions/TimelineActions.cs:1
msgid "Key All Animated"
msgstr "すべてのアニメーションするキー"

#: Editor/Actions/Menus/TimelineContextMenu.cs:1
msgid "Layer {0}"
msgstr "レイヤー{0}"

#: Editor/Animation/AnimationPlayableAssetEditor.cs:1
msgid "Legacy animation clips are not supported"
msgstr "レガシーアニメーションクリップはサポートされていません"

#: Editor/Window/TimelineWindow_HeaderGui.cs:1
msgid "Local"
msgstr "ローカル"

#: Editor/Actions/TrackActions.cs:1
msgid "Lock"
msgstr "ロック"

#: Editor/Actions/TrackActions.cs:1
msgid "Lock Tracks"
msgstr "トラックのロック"

#: Editor/Actions/TrackActions.cs:1
msgid "Lock selected track only"
msgstr "選択されたトラックのみロック"

#: Editor/treeview/TrackGui/TimelineTrackBaseGUI.cs:1
msgid "Locked"
msgstr "ロック"

#: Editor/treeview/TrackGui/TimelineTrackBaseGUI.cs:1
msgid "Locked / Muted"
msgstr "ロック / ミュート"

#: Editor/treeview/TrackGui/TimelineTrackBaseGUI.cs:1
msgid "Locked / Partially Muted"
msgstr "ロック / 部分的にミュート"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Loop"
msgstr "ループ"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Makes playback of the clip play relative to first key of the root transform"
msgstr "ルートトランスフォームの最初のキーを基準にしてクリップを再生します"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Match Clip Content"
msgstr "クリップ内容のマッチ"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Match Clip Duration"
msgstr "クリップ複製マッチ"

#: [MenuEntry]./Editor/Actions/TimelineActions.cs:1
msgid "Match Content"
msgstr "コンテントを合わせる"

#: Editor/Animation/AnimationOffsetMenu.cs:1
msgid "Match Offsets Fields/"
msgstr "オフセットフィールドにマッチ/"

#: [MenuEntry]./Editor/Animation/AnimationClipActions.cs:1
msgid "Match Offsets To Next Clip"
msgstr "次のクリップへのオフセットに合わせる"

#: [MenuEntry]./Editor/Animation/AnimationClipActions.cs:1
msgid "Match Offsets To Previous Clip"
msgstr "前のクリップへのオフセットに合わせる"

#: [MenuEntry]./Editor/Attributes/MenuEntryAttribute.cs:1
msgid "Menu Action with priority"
msgstr "優先度付きメニューアクション"

#: Editor/DirectorStyles.cs:1
msgid "Mix Mode (1)"
msgstr "混合モード (1)"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Mode Property"
msgstr "モードプロパティ"

#: Editor/Manipulators/Move/MoveItemModeMix.cs:1
msgid "Move Items"
msgstr "アイテムの移動"

#: Editor/Signals/Styles.cs:1
msgid "Multi-edit not supported for SignalReceivers on tracks bound to different GameObjects."
msgstr "異なるゲームオブジェクトにバインドされたトラック上のシグナルレシーバーに複数編集はサポートしていません"

#: Editor/Signals/Styles.cs:1
msgid "Multi-edit not supported for SignalReceivers when SignalEmitters use different Signals."
msgstr "シグナルエミッターが異なるシグナルを扱う場合のシグナルレシーバーでは複数編集をサポートしていません"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Multiple Clip Timing"
msgstr "複数のクリップタイミング"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Multiple clips selected. Only common properties are shown."
msgstr "複数のクリップが選択されています。共通プロパティのみ表示されます。"

#: Editor/Actions/TrackActions.cs:1
msgid "Mute"
msgstr "ミュート"

#: [MenuEntry]./Editor/Actions/TimelineActions.cs:1
msgid "Mute Timeline Markers"
msgstr "タイムラインマーカーをミュート"

#: Editor/Actions/TrackActions.cs:1
msgid "Mute Tracks"
msgstr "トラックのミュート"

#: Editor/Actions/TrackActions.cs:1
msgid "Mute selected track only"
msgstr "選択されたトラックのみミュート"

#: Editor/treeview/TrackGui/TimelineTrackBaseGUI.cs:1
msgid "Muted"
msgstr "ミュート"

#: [MenuEntry]./Editor/Attributes/MenuEntryAttribute.cs:1
msgid "My Menu/Menu Action inside submenu"
msgstr "マイメニュー/サブメニュー内メニューアクション"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "NTSC (29.97)"
msgstr "NTSC (29.97)"

#: Editor/inspectors/GroupTrackInspector.cs:1
msgid "Name"
msgstr "名前"

#: Editor/Signals/Styles.cs:1
msgid "New Signal"
msgstr "新しいシグナル"

#: Editor/Animation/AnimationPlayableAssetEditor.cs:1
msgid "No animation clip assigned"
msgstr "アニメーションクリップが割り当てられていません"

#: Editor/Audio/AudioPlayableAssetEditor.cs:1
msgid "No audio clip assigned"
msgstr "オーディオクリップが割り当てられていません"

#: Editor/Signals/Styles.cs:1
msgid "No reaction for {0} has been defined in this receiver"
msgstr "このレシーバーには{0}のリアクションが定義されていません"

#: Editor/Signals/SignalEmitterEditor.cs:1
msgid "No signal assigned"
msgstr "シグナルが割り当てられていません"

#: Editor/DirectorStyles.cs:1
msgid "No timeline found in the scene"
msgstr "このシーンにはタイムラインがありません"

#: Editor/Signals/Styles.cs:1
msgid "None"
msgstr "なし"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Offsets Match Fields"
msgstr "マッチするフィールドのオフセット"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Offsets applied to recorded position and rotation keys"
msgstr "記録された位置および回転のキーに適用されるオフセット"

#: Editor/DirectorStyles.cs:1
msgid "Options"
msgstr "オプション"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Out"
msgstr "アウト"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "PAL (25)"
msgstr "PAL (25)"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Parent Object"
msgstr "親オブジェクト"

#: Editor/treeview/TrackGui/TimelineTrackBaseGUI.cs:1
msgid "Partially Muted"
msgstr "部分的にミュート"

#: [MenuEntry]./Editor/Actions/TimelineActions.cs:1
msgid "Paste"
msgstr "ペースト"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Play Range Mode"
msgstr "範囲再生モード"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Play Range Mode/Loop"
msgstr "範囲再生/ループ"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Play Range Mode/Once"
msgstr "範囲再生/一度"

#: Editor/DirectorStyles.cs:1
msgid "Play the timeline (Space)"
msgstr "タイムライン(スペース)の再生"

#: Editor/Utilities/BindingUtility.cs:1
msgid "PlayableDirector Binding"
msgstr "PlayableDirectorバインド"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Playback Scrolling Mode"
msgstr "プレイバックスクロールモード"

#: Editor/treeview/TrackGui/TimelineTrackErrorGUI.cs:1
msgid "Please fix any compile errors in the script for this track"
msgstr "このトラックに関連するスクリプトのコンパイルエラーをすべて修正してください"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Position"
msgstr "位置"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Position Property"
msgstr "位置プロパティ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Post Extrapolation Mode Property"
msgstr "ポスト外挿モードプロパティ"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Post Playback"
msgstr "ポストプレイバック"

#: Editor/Activation/ActivationTrackInspector.cs:1
msgid "Post Playback Property"
msgstr "ポストプレイバックプロパティ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Post-Extrapolate"
msgstr "後の外挿"

#: Editor/Activation/ActivationTrackInspector.cs:1
msgid "Post-playback state"
msgstr "ポストプレイバックステート"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Pre Extrapolation Mode Property"
msgstr "プレ外挿モードプロパティ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Pre-Extrapolate"
msgstr "前の外挿"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Prefab"
msgstr "プレハブ"

#: Editor/Utilities/BreadcrumbDrawer.cs:1
msgid "Prefab Isolation not enabled. Click to Enable."
msgstr "プレハブアイソレーションが有効ではありません。有効をクリックしてください。"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Prefab Object"
msgstr "プレハブオブジェクト"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Preferences Page..."
msgstr "環境設定ページ..."

#: Editor/Recording/AnimationTrackRecorder.cs:1
msgid "Prepend Key"
msgstr "キー追加"

#: Editor/DirectorStyles.cs:1
msgid "Preview"
msgstr "プレビュー"

#: Editor/Signals/SignalEmitterInspector.cs:1
msgid "Property"
msgstr "プロパティ"

#: Editor/DirectorStyles.cs:1
msgid "R"
msgstr "R"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Random Seed"
msgstr "ランダムシード"

#: Editor/Signals/TreeView/SignalReceiverTreeView.cs:1
msgid "Reaction"
msgstr "リアクション"

#: Editor/Signals/Styles.cs:1
msgid "Receiver Component on"
msgstr "レシーバーコンポーネントをオン"

#: Editor/Recording/AnimationTrackRecorder.cs:1
msgid "Record Key"
msgstr "レコードキー"

#: Editor/Recording/AnimationTrackRecorder.cs:1
msgid "Recorded"
msgstr "レコード済み"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Recorded Offset Euler Property"
msgstr "オイラープロパティの記録されたオフセット"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Recorded Offset Position Property"
msgstr "ポジションプロパティの記録されたオフセット"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Recorded Offsets"
msgstr "記録されたオフセット"

#: Editor/treeview/Drawers/ClipDrawer.cs:1
msgid "Recording in blends in prohibited"
msgstr "ブレンドの記録が許可されていません"

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
msgid "Recording is disabled: scene preview is ignored for this TimelineAsset"
msgstr "収録が禁止されています:このTimelineAssetではシーンプレビューが無視されます"

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
msgid "Recording is not permitted when Track Offsets are set to Auto. Track Offset settings can be changed in the track menu of the base track."
msgstr "トラックオフセットがオートに設定されている場合、レコーディングは許可されません。トラックオフセットの設定は、ベーストラックのトラックメニューで変更することができます。"

#: Editor/DirectorStyles.cs:1
msgid "Recording..."
msgstr "記録中..."

#: Editor/Recording/TimelineRecording.cs:1
msgid "Remove Curve"
msgstr "カーブ削除"

#: [MenuEntry]./Editor/Actions/TrackActions.cs:1
msgid "Remove Invalid Markers"
msgstr "無効なマーカーを削除"

#: Editor/Recording/TimelineRecording.cs:1
msgid "Remove Key"
msgstr "キー削除"

#: Editor/Animation/ClipCurveEditor.cs:1
msgid "Remove Properties"
msgstr "プロパティ削除"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Remove Start Offset"
msgstr "開始オフセットを削除"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Remove Start Offset Property"
msgstr "開始オフセットプロパティを削除"

#: Editor/Extensions/TrackExtensions.cs:1
msgid "Remove Track"
msgstr "トラック削除"

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
msgid "Rename Track"
msgstr "トラックリネーム"

#: Editor/inspectors/MarkerInspector.cs:1
msgid "Rename marker"
msgstr "マーカーのリネーム"

#: Editor/DirectorStyles.cs:1
msgid "Replace Mode (3)"
msgstr "置換モード (3)"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Reset Clip Editing"
msgstr "クリップ編集のリセット"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Reset Clip Speed"
msgstr "クリップスピードのリセット"

#: [MenuEntry]./Editor/Animation/AnimationClipActions.cs:1
msgid "Reset Offsets"
msgstr "オフセットのリセット"

#: Editor/Signals/SignalReceiverHeader.cs:1
msgid "Resize to Fit"
msgstr "合うようにリサイズ"

#: Editor/Signals/Styles.cs:1
msgid "Retroactive"
msgstr "遡及的"

#: Editor/Signals/SignalEmitterInspector.cs:1
msgid "Retroactive Property"
msgstr "遡及的プロパティ"

#: Editor/DirectorStyles.cs:1
msgid "Ripple Mode (2)"
msgstr "リップルモード (2)"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Rotation"
msgstr "回転"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Rotation Property"
msgstr "回転プロパティ"

#: Editor/inspectors/TimelineAssetInspector.cs:1
#, fuzzy
msgid "Scene Preview"
msgstr "プレビュー"

#: Editor/inspectors/TimelineAssetInspector.cs:1
#, fuzzy
msgid "Scene Preview Property"
msgstr "遡及的プロパティ"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Scene offsets will use the existing transform as initial offsets. Use this to play the track from the gameObjects current position and rotation."
msgstr "シーンオフセットは最初のオフセットとして既存のトランスフォームを使用します。ゲームオブジェクトの現在の位置および回転のトラックを再生する際に使用してください。"

#: Editor/DirectorStyles.cs:1
msgid "Scene preview is disabled for this TimelineAsset"
msgstr "このTimelineAssetのシーンプレビューは無効です"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Search Hierarchy"
msgstr "サーチヒストリー"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Search child game objects for particle systems and playable directors"
msgstr "パーティクルシステムおよびプレイアブルディレクターを持つ子ゲームオブジェクトを検索する"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Seconds"
msgstr "秒"

#: Editor/Signals/Styles.cs:1
msgid "Select which Signal Asset to emit."
msgstr "発生させるシグナルアセットを選択"

#: Editor/Actions/Menus/TimelineContextMenu.cs:1
msgid "Select {0}"
msgstr "{0}を追加"

#: Editor/Trackhead.cs:1
msgid "Select/Blends Intersecting"
msgstr "選択/交差をブレンド"

#: Editor/Trackhead.cs:1
msgid "Select/Clips Ending After"
msgstr "選択/終了後のクリップ"

#: Editor/Trackhead.cs:1
msgid "Select/Clips Ending Before"
msgstr "選択/終了前のクリップ"

#: Editor/Trackhead.cs:1
msgid "Select/Clips Intersecting"
msgstr "選択/交差しているクリップ"

#: Editor/Trackhead.cs:1
msgid "Select/Clips Starting After"
msgstr "選択/開始後のクリップ"

#: Editor/Trackhead.cs:1
msgid "Select/Clips Starting Before"
msgstr "選択/開始前のクリップ"

#: [MenuItem]./Editor/Window/TimelineWindow.cs:1
msgid "Sequencing"
msgstr "シーケンス"

#: Editor/DirectorStyles.cs:1
msgid "Show / Hide Timeline Markers"
msgstr "タイムラインマーカーの表示/非表示"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Show Audio Waveforms"
msgstr "オーディオウェーブフォーム表示"

#: [MenuEntry]./Editor/Actions/TrackActions.cs:1
msgid "Show Markers"
msgstr "マーカーの表示"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Show QuadTree Debugger"
msgstr "QuadTreeデバッガ表示"

#: Editor/Window/TimelineWindow_Gui.cs:1
msgid "Show Snapping Debug"
msgstr "スナップデバッグを表示"

#: [MenuEntry]./Editor/Actions/TimelineActions.cs:1
msgid "Show Timeline Markers"
msgstr "タイムラインマーカー表示"

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
msgid "Show curves view"
msgstr "カーブビュー表示"

#: Editor/DirectorStyles.cs:1
msgid "Show markers"
msgstr "マーカーの表示"

#: [MenuItem]./Editor/Signals/SignalAssetInspector.cs:1
msgid "Signal"
msgstr "シグナル"

#: [MenuEntry]./Editor/Attributes/MenuEntryAttribute.cs:1
msgid "Simple Menu Action"
msgstr "シンプルメニューアクション"

#: [MenuEntry]./Tests/Editor/Actions/ClipActionTests.cs:1
msgid "Simple With Menu Entry Custom Clip Test Action"
msgstr "Simple With Menu Entry Custom Clip Test Action"

#: [MenuEntry]./Tests/Editor/Actions/MarkerActionTests.cs:1
msgid "Simple With Menu Entry Custom Marker Test Action"
msgstr "Simple With Menu Entry Custom Marker Test Action"

#: [MenuEntry]./Tests/Editor/Actions/TimelineActionTests.cs:1
msgid "Simple With Menu Entry Custom Timeline Test Action"
msgstr "Simple With Menu Entry Custom Timeline Test Action"

#: [MenuEntry]./Tests/Editor/Actions/TrackActionTests.cs:1
msgid "Simple With Menu Entry Custom Track Test Action"
msgstr "Simple With Menu Entry Custom Track Test Action"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Snap To Frame"
msgstr "フレームにスナップ"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Source Object"
msgstr "ソースオブジェクト"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "Specified how the duration of the sequence is calculated"
msgstr "シーケンス期間の計算方法"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Speed Multiplier"
msgstr "速度の乗数"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Split Clip"
msgstr "クリップ分割"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Start"
msgstr "開始"

#: Editor/treeview/TrackGui/TimelineTrackGUI.cs:1
msgid "Start recording"
msgstr "収録開始"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Start the clip at this local time"
msgstr "このローカル時間でクリップを開始"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Synchronize the time between the clip and any Script that implements the ITimeControl interface on the game object"
msgstr "クリップとゲームオブジェクトの ITimeControl インタフェースを持つスクリプトを同期させる"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Synchronize the time between the clip and any particle systems on the game object"
msgstr "クリップとゲームオブジェクト上のパーティクルシステムを同期させる"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Synchronize the time between the clip and any playable directors on the game object"
msgstr "クリップとゲームオブジェクトのプレイアブルディレクターを同期させる"

#: [DisplayName]./Tests/Editor/Utilities/TypeLookup.cs:1
msgid "Testing Track"
msgstr "テストトラック"

#: Editor/Utilities/BreadcrumbDrawer.cs:1
msgid "The PlayableDirector is disabled"
msgstr "そのPlayableDirectorは無効です"

#: Editor/Signals/Styles.cs:1
msgid "The Signal Receiver Component on the bound GameObject."
msgstr "バインドされたゲームオブジェクトのシグナルレシーバー"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid ""
"The active state to the leave the game object when the timeline is finished. \n"
"\n"
"Revert will leave the game object in the state it was prior to the timeline being run"
msgstr ""
"タイムラインが終わったとき放置させるゲームオブジェクトへのステート。\n"
"\n"
"リバートさせることでゲームオブジェクトはタイムライン実行前のままになります。"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "The assigned GameObject contains a PlayableDirector component that results in a circular reference."
msgstr "割り当てられたゲームオブジェクトに循環参照するPlayableDirectorがあります。"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "The assigned GameObject references the same PlayableDirector component being controlled."
msgstr "割り当てられたゲームオブジェクトは、制御される同じPlayableDirectorコンポーネントを参照します。"

#: Editor/CustomEditors/ClipEditor.cs:1
msgid "The associated script can not be loaded"
msgstr "関連するスクリプトを読み込めません"

#: Editor/Activation/ActivationTrackEditor.cs:1
msgid "The bound GameObject contains the PlayableDirector."
msgstr "バインドされたゲームオブジェクトはPlayableDirectorを保持しています"

#: Editor/Activation/ActivationTrackEditor.cs:1
msgid "The bound GameObject is a parent of the PlayableDirector."
msgstr "バインドされたゲームオブジェクトはPlayableDirectorの親です"

#: Editor/CustomEditors/TrackEditor.cs:1
msgid "The bound GameObject is disabled."
msgstr "バインドされたゲームオブジェクトが無効です"

#: Editor/CustomEditors/TrackEditor.cs:1
msgid "The bound object is a Prefab"
msgstr "バインドされたオブジェクトがプレハブです"

#: Editor/CustomEditors/TrackEditor.cs:1
msgid "The bound object is not the correct type."
msgstr "バインドされたオブジェクトが正しい型ではありません"

#: Editor/CustomEditors/TrackEditor.cs:1
msgid "The component is disabled"
msgstr "コンポーネントが無効です"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The end time of the clip"
msgstr "クリップの終了時間"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The end time of the clip group"
msgstr "クリップグループの終了時間"

#: Editor/Audio/AudioClipPropertiesDrawer.cs:1
msgid "The final {3} is {0}\n"
msgstr "最終{3}は{0}\n"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "The frame rate at which this sequence updates"
msgstr "このシーケンスが更新する地点のフレームレート"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The length of the blend in"
msgstr "インで混合する長さ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The length of the blend out"
msgstr "アウトで混合する長さ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The length of the clip"
msgstr "クリップの長さ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The length of the ease in"
msgstr "イースインの長さ"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The length of the ease out"
msgstr "イースアウトの長さ"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "The length of the sequence"
msgstr "シーケンスの長さ"

#: [Tooltip]./Runtime/Animation/AnimationPlayableAsset.cs:1
msgid "The source AnimationClip does not loop during playback."
msgstr "ソースとなるアニメーションクリップがプレイバック中にループしません。"

#: [Tooltip]./Runtime/Animation/AnimationPlayableAsset.cs:1
msgid "The source AnimationClip loops during playback."
msgstr "ソースとなるアニメーションクリップはプレイバック中にループします。"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The start time of the clip"
msgstr "クリップの開始時間"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "The start time of the clip group"
msgstr "クリップグループの開始時間"

#: Editor/Signals/Styles.cs:1
msgid "There is no Signal Receiver component on {0}"
msgstr "{0}にはシグナルレシーバーコンポーネントがありません"

#: Editor/CustomEditors/ClipEditor.cs:1
msgid "This clip does not contain a valid playable asset"
msgstr "このクリップは有効なプレイアブルアセットを保持していません"

#: Editor/treeview/Drawers/ClipDrawer.cs:1
msgid "This clip is not recordable"
msgstr "このクリップは記録できません"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "This mode is deprecated may be removed in a future release."
msgstr "このモードは削除予定です"

#: Editor/DirectorStyles.cs:1
msgid "This track references an external asset"
msgstr "このトラックは外部アセットを参照しています"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "This value is inherited from it's parent track."
msgstr "これは親のトラックから継承された値です"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Tile"
msgstr "タイル"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Time Unit"
msgstr "時間の単位"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Time scale of the playback speed"
msgstr "プレイバック速度のタイムスケール"

#: [MenuItem]./Editor/Window/TimelineWindow.cs:1
msgid "Timeline"
msgstr "タイムライン"

#: Editor/inspectors/TimelineProjectSettings.cs:1
msgid "Timeline Asset"
msgstr "タイムラインアセット"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "Timeline Assets"
msgstr "アセットのタイムライン"

#: Editor/inspectors/ClipInspector/ClipInspector.cs:1
msgid "Timeline Clips"
msgstr "タイムラインクリップ"

#: Editor/inspectors/TimelinePreferences.cs:1
msgid "Timeline Editor Settings"
msgstr "タイムラインのエディタ設定"

#: Editor/DirectorStyles.cs:1
msgid "Timeline Selector"
msgstr "タイムラインセレクター"

#: Editor/DirectorStyles.cs:1
msgid "To begin a new timeline with {0}, create {1}"
msgstr "{0}でタイムラインを開始するために{1}を作成する必要があります"

#: Editor/DirectorStyles.cs:1
msgid "To start creating a timeline, select a GameObject"
msgstr "ゲームオブジェクトを選択することでタイムラインの作成を開始できます"

#: Editor/Actions/TimelineActions.cs:1
msgid "Toggle Mute"
msgstr "ミュート切り替え"

#: Editor/Extensions/TrackExtensions.cs:1
msgid "Toggle Show Markers"
msgstr "マーカー表示の切り替え"

#: Editor/DirectorStyles.cs:1
msgid "Toggle play range markers."
msgstr "プレイ範囲マーカーの切り替え"

#: Editor/Actions/Menus/TimelineContextMenu.cs:1
msgid "Track Group"
msgstr "トラックグループ"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Track Offsets"
msgstr "トラックオフセット"

#: [MenuEntry]./Editor/Animation/AnimationTrackActions.cs:1
msgid "Track Offsets/Apply Scene Offsets"
msgstr "トラックオフセット/シーンオフセットを適用"

#: [MenuEntry]./Editor/Animation/AnimationTrackActions.cs:1
msgid "Track Offsets/Apply Transform Offsets"
msgstr "トラックオフセット/トランスフォームオフセットを適用"

#: [MenuEntry]./Editor/Animation/AnimationTrackActions.cs:1
msgid "Track Offsets/Auto (Deprecated)"
msgstr "トラックオフセット/自動 (廃止)"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Track Position Property"
msgstr "トラックポジションのプロパティ"

#: Editor/Animation/CurveDataSource.cs:1
msgid "Track Properties"
msgstr "トラックのプロパティ"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Track Rotation Property"
msgstr "トラックローテーションのプロパティ"

#: Editor/Actions/Menus/TimelineContextMenu.cs:1
msgid "Track Sub-Group"
msgstr "トラックサブグループ"

#: Editor/treeview/TrackGui/TimelineTrackErrorGUI.cs:1
msgid "Track cannot be loaded."
msgstr "トラックを読み込めません"

#: Editor/Signals/Styles.cs:1
msgid "Track has no bound GameObject."
msgstr "トラックにバインドされたゲームオブジェクトがありません"

#: Editor/Audio/AudioTrackInspector.cs:1
msgid "Track: {1}\n"
msgstr "トラック: {1}\n"

#: Editor/Audio/AudioClipPropertiesDrawer.cs:1
msgid "Track: {2}"
msgstr "トラック: {2}"

#: Editor/inspectors/AnimationTrackInspector.cs:1
msgid "Transform offsets are applied to the entire track. Use this mode to play the animation track at a fixed position and rotation."
msgstr "トランスフォームオフセットは全体のトラックに適用されます。このモードは、固定の位置および回転におけるアニメーションを再生するときに使用してください。"

#: Editor/Manipulators/EditMode.cs:1
msgid "Trim Clip"
msgstr "クリップトリム"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Trim Clip End"
msgstr "クリップエンドトリム"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Trim Clip Last Loop"
msgstr "クリップ最終ループのトリム"

#: Editor/Utilities/ClipModifier.cs:1
msgid "Trim Clip Start"
msgstr "クリップスタートトリム"

#: Editor/inspectors/GroupTrackInspector.cs:1
msgid "Type"
msgstr "タイプ"

#: Editor/Actions/TrackActions.cs:1
msgid "Unlock"
msgstr "ロック解除"

#: Editor/Actions/TrackActions.cs:1
msgid "Unlock selected track only"
msgstr "選択されたトラックのみロック解除"

#: Editor/Actions/TrackActions.cs:1
msgid "Unmute"
msgstr "ミュート解除"

#: Editor/Actions/TrackActions.cs:1
msgid "Unmute selected track only"
msgstr "選択されたトラックのみミュート解除"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Update Director"
msgstr "ディレクターを更新"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Update I Time Control"
msgstr "ITimeControlの更新"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Update Particle"
msgstr "パーティクルの更新"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "Use Activation"
msgstr "アクティベーション使用"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Use defaults"
msgstr "デフォルトを使用"

#: Editor/Signals/Styles.cs:1
msgid "Use retroactive to emit this signal even if playback starts afterwards."
msgstr "あとからプレイバックが始まるとしてもこのシグナルを遡及的に発生させる"

#: [Tooltip]./Runtime/Animation/AnimationPlayableAsset.cs:1
msgid "Use the loop time setting from the source AnimationClip."
msgstr "ソースのAnimationClipのループ時間設定を使用する"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Use this to offset the root transform position and rotation relative to the track when playing this clip"
msgstr "クリップ再生時のトラックに相対的なルートのトランスフォームポジションおよびローテーションへのオフセットに使用されます"

#: Editor/Playables/ControlPlayableInspector.cs:1
msgid "When checked the clip will control the active state of the source game object"
msgstr "クリップがソースゲームオブジェクトのアクティブ状態を制御するようになります"

#: Editor/inspectors/TimelineAssetInspector.cs:1
msgid "When ignoring preview, the Timeline window will modify the scene when this timeline is opened."
msgstr "プレビューを無視すると、タイムラインウインドウはこのタイムラインが開いたときに変更されます。"

#: Editor/inspectors/AnimationPlayableAssetInspector.cs:1
msgid "Whether the source Animation Clip loops during playback."
msgstr "プレイバック中のアニメーションクリップループのソースかどうか"

#: [MenuItem]./Editor/Window/TimelineWindow.cs:1
msgid "Window"
msgstr "ウインドウ"

#: Editor/Animation/AnimationPlayableAssetEditor.cs:1
msgid "You are using motion curves without applyRootMotion enabled on the Animator. The root transform will not be animated"
msgstr "アニメーターで applyRootMotion を有効にしていないモーションカーブを使用しています。ルート変換はアニメーション化されません"

#: Editor/Animation/AnimationPlayableAssetEditor.cs:1
msgid "You are using root curves without applyRootMotion enabled on the Animator. The root transform will not be animated"
msgstr "アニメーターで applyRootMotion を有効にしていないルート曲線を使用しています。ルート変換はアニメーション化されません"

#: Editor/Signals/Styles.cs:1
msgid "Your project contains no Signal assets"
msgstr "このプロジェクトにシグナルアセットがありません"

#: Editor/Utilities/DisplayNameHelper.cs:1
msgid "[Read Only]"
msgstr "[書き換え禁止]"

#: Editor/Window/TimelineWindow_TrackGui.cs:1
msgid "a Director component and a Timeline asset"
msgstr "Directorコンポーネントとタイムラインアセット"

#: Editor/inspectors/TimelineInspectorUtility.cs:1
msgid "f"
msgstr "f"

#: Editor/inspectors/TimelineInspectorUtility.cs:1
msgid "s"
msgstr "s"

#: Editor/inspectors/MarkerInspector.cs:1
msgid "{0} Markers"
msgstr "{0} マーカー"

msgid " Frames"
msgstr "フレーム"

msgid "Activation Playable Asset"
msgstr "アクティベーションプレイアブルアセット"

msgid "Activation Track"
msgstr "アクティベーショントラック"

msgid "Animation Playable Asset"
msgstr "アニメーションプレイアブルアセット"

msgid "Animation Track"
msgstr "アニメーショントラック"

msgid "Apply Transform Offsets"
msgstr "トランスフォームオフセットを適用"

msgid "Audio Playable Asset"
msgstr "オーディオプレイアブルアセット"

msgid "Audio Track"
msgstr "オーディオトラック"

msgid ""
"Avatar Mask disabled\n"
"Click to enable"
msgstr ""
"アバターマスク無効\n"
"クリックで有効"

msgid ""
"Avatar Mask enabled\n"
"Click to disable"
msgstr ""
"アバターマスク有効\n"
"クリックで無効"

msgid "Based On Clips"
msgstr "クリップに基づく"

msgid "Blend In"
msgstr "ブレンドイン"

msgid "Blend Out"
msgstr "ブレンドアウト"

msgid "Control Activation|"
msgstr "コントロールアクティベーション|"

msgid "Control Playable Asset"
msgstr "コントロールプレイアブルアセット"

msgid "Control Playable Directors|"
msgstr "プレイアブルディレクターの制御|"

msgid "Control Track"
msgstr "コントロールトラック"

msgid "Delete Curve"
msgstr "カーブ削除"

msgid "Delete Parameter Curves"
msgstr "パラメーターカーブ削除"

msgid "Fixed Length"
msgstr "固定長"

msgid "Frame Rate/30"
msgstr "フレームレート/30"

msgid "Frame Rate/50"
msgstr "フレームレート/50"

msgid "Frame Rate/60"
msgstr "フレームレート/60"

msgid "Frame Rate/Custom ("
msgstr "フレームレート/カスタム ("

msgid "Frame Rate/Film (24)"
msgstr "フレームレート/フィルム (24)"

msgid "Frame Rate/NTSC (29.97)"
msgstr "フレームレート/NTSC (29.97)"

msgid "Frame Rate/PAL (25)"
msgstr "フレームレート/PAL (25)"

msgid "Hide Inline Curves"
msgstr "インラインカーブ非表示"

msgid "Inactive"
msgstr "非アクティブ"

msgid "Leave As Is"
msgstr "そのまま"

msgid "Markers"
msgstr "マーカー"

msgid "Match Clip"
msgstr "クリップマッチ"

msgid "Must be disabled when the Source Game Object references the same PlayableDirector component that is being controlled"
msgstr "ソースゲームオブジェクトが同じく制御されているPlayableDirectorを参照している場合は無効にしていなければなりません"

msgid "Pan"
msgstr "パン"

msgid "Playable Track"
msgstr "プレイアブルトラック"

msgid "Playback Scrolling mode/None"
msgstr "プレイバックスクロールモード/なし"

msgid "Playback Scrolling mode/Smooth"
msgstr "プレイバックスクロールモード/スムース"

msgid "Reparent"
msgstr "リペアレント"

msgid "Set Transform Offsets"
msgstr "トランスフォームオフセット設定"

msgid "Show Inline Curves"
msgstr "インラインカーブ表示"

msgid "Signal Asset"
msgstr "シグナルアセット"

msgid "Signal Emitter"
msgstr "シグナルエミッター"

msgid "Signal Track"
msgstr "シグナルトラック"

msgid "Smooth"
msgstr "スムース"

msgid "Source Game Object"
msgstr "ソースゲームオブジェクト"

msgid "Spatial Blend"
msgstr "空間的ブレンド"

msgid "Stereo Pan"
msgstr "ステレオパン"

msgid "Umute"
msgstr "ミュート解除"

msgid "Use Source Asset"
msgstr "ソースアセットを使用"

msgid "Volume"
msgstr "音量"

msgid "translation: {0}   scale: {1}   rect: {2}   shownRange: {3}"
msgstr "トランスレーション: {0}   スケール: {1}   矩形: {2}   矩形表示: {3}"
