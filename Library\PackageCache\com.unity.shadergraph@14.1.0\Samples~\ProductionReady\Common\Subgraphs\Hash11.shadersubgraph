{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "06f9e921e934457d847e89724f38b4aa",
    "m_Properties": [
        {
            "m_Id": "824885cbe741440ea17d2d1e241df9e9"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "0179a444c5304e668af24642a0ab024e"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "7cd81ecf6c3a4fc7af5d969b135c5117"
        },
        {
            "m_Id": "f329ddd3ccbd40c895e296e64420e5eb"
        },
        {
            "m_Id": "616aa43f2043474cbdc71d15edd7b99c"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "ca3839df5ebc49a79b043b35c282704a"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "616aa43f2043474cbdc71d15edd7b99c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f329ddd3ccbd40c895e296e64420e5eb"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f329ddd3ccbd40c895e296e64420e5eb"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7cd81ecf6c3a4fc7af5d969b135c5117"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "7cd81ecf6c3a4fc7af5d969b135c5117"
    },
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "0179a444c5304e668af24642a0ab024e",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "824885cbe741440ea17d2d1e241df9e9"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0dd5e701abc44740ae78f0350937fee2",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "616aa43f2043474cbdc71d15edd7b99c",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -755.0000610351563,
            "y": 443.5000305175781,
            "width": 84.5,
            "height": 34.000030517578128
        }
    },
    "m_Slots": [
        {
            "m_Id": "0dd5e701abc44740ae78f0350937fee2"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "824885cbe741440ea17d2d1e241df9e9"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "75e5f8553dd24cdc85961e8cdfec1910",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7bb05a9714e3414da68a94d1284a7686",
    "m_Id": 0,
    "m_DisplayName": "p",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "p",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "7cd81ecf6c3a4fc7af5d969b135c5117",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -428.0,
            "y": 400.66668701171877,
            "width": 85.33328247070313,
            "height": 76.66665649414063
        }
    },
    "m_Slots": [
        {
            "m_Id": "b30c1b64b56b4f97a05c85df5427ed6a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "824885cbe741440ea17d2d1e241df9e9",
    "m_Guid": {
        "m_GuidSerialized": "735bb76f-575f-46ab-86cb-2be8c828d70e"
    },
    "m_Name": "In",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "In",
    "m_DefaultReferenceName": "_In",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b30c1b64b56b4f97a05c85df5427ed6a",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ca3839df5ebc49a79b043b35c282704a",
    "m_Title": "",
    "m_Content": "Generates a random output value for every unique input value.\n\nThis one receives a float and outputs a Float.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -642.0000610351563,
        "y": 300.5000305175781,
        "width": 200.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "f329ddd3ccbd40c895e296e64420e5eb",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Hash11Tchou (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -660.0000610351563,
            "y": 400.5000305175781,
            "width": 230.50006103515626,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "7bb05a9714e3414da68a94d1284a7686"
        },
        {
            "m_Id": "75e5f8553dd24cdc85961e8cdfec1910"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "Hash11Tchou",
    "m_FunctionSource": "",
    "m_FunctionBody": "uint v = (uint) (int) round(p);\r\nv ^= 1103515245U;\r\nv += v;\r\nv *= v;                        \nv ^= v >> 5u;             \nv *= 0x27d4eb2du;     \r\nOut = v * (1.0 / float(0xffffffff));"
}

