# 🌱 绿色超平坦地面设置指南

## 🚀 一键创建超平坦绿色世界

### 方法一：使用SuperFlatGreen（推荐）
1. **层级视图**中右键 → **创建空对象**
2. 重命名为"SuperFlatWorld"
3. **检视面板**中点击**添加组件**
4. 搜索并添加"SuperFlatGreen"脚本
5. 调整参数：
   - **世界大小**（World Size）：500 (超大地面)
   - **草地颜色**（Grass Color）：选择你喜欢的绿色
   - **创建无限外观**（Create Infinite Appearance）：勾选
6. 点击**播放按钮**▶️ - 立即获得超平坦绿色世界！

### 方法二：使用GreenGridGenerator（网格效果）
1. **层级视图**中右键 → **创建空对象**
2. 重命名为"GridWorld"
3. **检视面板**中点击**添加组件**
4. 搜索并添加"GreenGridGenerator"脚本
5. 调整参数：
   - **网格大小**（Grid Size）：200
   - **网格分辨率**（Grid Resolution）：50
   - **显示网格线**（Show Grid Lines）：勾选/取消勾选
6. 点击**播放按钮**▶️

## 🎯 特色功能

### SuperFlatGreen特点：
- ✅ **超大地面**：500x500单位的巨大平坦地面
- ✅ **无限外观**：3x3地面块创造无边界感觉
- ✅ **雾效果**：远处雾气增强无限感
- ✅ **完美碰撞**：可以正常行走和跳跃
- ✅ **浅蓝天空**：舒适的视觉体验

### GreenGridGenerator特点：
- ✅ **网格纹理**：类似Minecraft的方块感觉
- ✅ **可调节网格**：自定义网格密度和颜色
- ✅ **实时预览**：在编辑器中实时调整效果

## 🎮 完整设置流程

### 第一步：创建地面
选择上面任一方法创建绿色地面

### 第二步：创建第一人称角色
1. **层级视图**中右键 → **创建空对象**
2. 重命名为"FirstPersonPlayer"
3. 设置位置：(0, 2, 0) - 确保在地面上方
4. **添加组件**：
   - **角色控制器**（Character Controller）
   - **FirstPersonController**脚本
   - **CursorManager**脚本

### 第三步：设置摄像机
1. 将**主摄像机**拖到FirstPersonPlayer下
2. 设置摄像机位置：(0, 1.8, 0)

### 第四步：配置角色控制器
- **中心**（Center）：(0, 1, 0)
- **半径**（Radius）：0.5
- **高度**（Height）：2

### 第五步：测试
点击**播放按钮**▶️，享受在超平坦绿色世界中的第一人称体验！

## 🎨 自定义选项

### 改变草地颜色：
- 浅绿色：RGB(0.5, 0.9, 0.5)
- 深绿色：RGB(0.2, 0.6, 0.2)
- 黄绿色：RGB(0.6, 0.8, 0.3)

### 调整世界大小：
- 小世界：100
- 中等世界：300
- 大世界：500
- 超大世界：1000

## ⚡ 性能提示
- 较大的世界大小可能影响性能
- 如果帧率较低，尝试减小世界大小
- 网格分辨率越高，纹理越精细但性能越低

## 🎯 完成！
现在你有了一个完美的绿色超平坦世界，可以自由探索！
