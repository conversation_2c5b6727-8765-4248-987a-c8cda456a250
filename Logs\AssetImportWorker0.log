Using pre-set license
Built from '1.6.1_update' branch; Version is '2022.3.61t2 (5a7d31f62760) revision 5930289'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 31968 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\UNRTIY\Tuan Hub\2022.3.61t2\Editor\Tuanjie.exe
-adb2
-batchMode
-noUpm
-disableFMOD
-name
AssetImportWorker0
-projectPath
D:/UNRTIY/One/A
-logFile
Logs/AssetImportWorker0.log
-srvPort
54063
Successfully changed project path to: D:/UNRTIY/One/A
D:/UNRTIY/One/A
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18820]  Target information:

Player connection [18820]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 214725282 [EditorId] 214725282 [Version] 1048832 [Id] WindowsEditor(7,lsy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18820] Host joined multi-casting on [***********:54997]...
Player connection [18820] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 7.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61t2 (5a7d31f62760)
[Subsystems] Discovering subsystems at path D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/UNRTIY/One/A/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) 610M (ID=0x164e)
    Vendor:   ATI
    VRAM:     15984 MB
    Driver:   32.0.13034.2002
Initialize mono
Mono path[0] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Managed'
Mono path[1] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56496
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004044 seconds.
- Loaded All Assemblies, in  0.327 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.226 seconds
Domain Reload Profiling: 553ms
	BeginReloadAssembly (84ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (163ms)
		LoadAssemblies (84ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (161ms)
			TypeCache.Refresh (159ms)
				TypeCache.ScanAssembly (140ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (226ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (186ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (130ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.652 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.522 seconds
Domain Reload Profiling: 1174ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (419ms)
		LoadAssemblies (307ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (194ms)
			TypeCache.Refresh (170ms)
				TypeCache.ScanAssembly (145ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (523ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (426ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (39ms)
			ProcessInitializeOnLoadAttributes (320ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5131 Unused Serialized files (Serialized files now loaded: 0)
Unloading 29 unused Assets / (346.8 KB). Loaded Objects now: 5609.
Memory consumption went from 217.4 MB to 217.1 MB.
Total: 8.032100 ms (FindLiveObjects: 1.133000 ms CreateObjectMapping: 0.140600 ms MarkObjects: 6.364800 ms  DeleteObjects: 0.392600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26311.462103 seconds.
  path: Assets/Readme.asset
  artifactKey: Guid(8105016687592461f977c054a80ce2f2) Importer(PreviewImporter)
Start importing Assets/Readme.asset using Guid(8105016687592461f977c054a80ce2f2) Importer(PreviewImporter)  -> (artifact id: 'de4cb2a79a177a239092cc3df5cd0b69') in 0.034386 seconds
Number of asset objects unloaded after import = 2
