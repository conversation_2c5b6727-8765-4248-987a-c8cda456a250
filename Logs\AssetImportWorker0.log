Using pre-set license
Built from '1.6.1_update' branch; Version is '2022.3.61t2 (5a7d31f62760) revision 5930289'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 31968 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\UNRTIY\Tuan Hub\2022.3.61t2\Editor\Tuanjie.exe
-adb2
-batchMode
-noUpm
-disableFMOD
-name
AssetImportWorker0
-projectPath
D:/UNRTIY/One/A
-logFile
Logs/AssetImportWorker0.log
-srvPort
62075
Successfully changed project path to: D:/UNRTIY/One/A
D:/UNRTIY/One/A
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [41920]  Target information:

Player connection [41920]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 530088251 [EditorId] 530088251 [Version] 1048832 [Id] WindowsEditor(7,lsy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [41920] Host joined multi-casting on [***********:54997]...
Player connection [41920] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 11.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61t2 (5a7d31f62760)
[Subsystems] Discovering subsystems at path D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/UNRTIY/One/A/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) 610M (ID=0x164e)
    Vendor:   ATI
    VRAM:     15984 MB
    Driver:   32.0.13034.2002
Initialize mono
Mono path[0] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Managed'
Mono path[1] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56904
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004580 seconds.
- Loaded All Assemblies, in  0.365 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.192 seconds
Domain Reload Profiling: 558ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (168ms)
		LoadAssemblies (105ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (162ms)
				TypeCache.ScanAssembly (141ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (193ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (153ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (103ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
