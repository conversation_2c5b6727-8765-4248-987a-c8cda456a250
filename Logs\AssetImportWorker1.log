Using pre-set license
Built from '1.6.1_update' branch; Version is '2022.3.61t2 (5a7d31f62760) revision 5930289'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 31968 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\UNRTIY\Tuan Hub\2022.3.61t2\Editor\Tuanjie.exe
-adb2
-batchMode
-noUpm
-disableFMOD
-name
AssetImportWorker1
-projectPath
D:/UNRTIY/One/A
-logFile
Logs/AssetImportWorker1.log
-srvPort
54063
Successfully changed project path to: D:/UNRTIY/One/A
D:/UNRTIY/One/A
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [32100]  Target information:

Player connection [32100]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1471842552 [EditorId] 1471842552 [Version] 1048832 [Id] WindowsEditor(7,lsy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [32100] Host joined multi-casting on [***********:54997]...
Player connection [32100] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 8.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61t2 (5a7d31f62760)
[Subsystems] Discovering subsystems at path D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/UNRTIY/One/A/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) 610M (ID=0x164e)
    Vendor:   ATI
    VRAM:     15984 MB
    Driver:   32.0.13034.2002
Initialize mono
Mono path[0] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Managed'
Mono path[1] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56852
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004845 seconds.
- Loaded All Assemblies, in  0.345 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.216 seconds
Domain Reload Profiling: 561ms
	BeginReloadAssembly (97ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (157ms)
		LoadAssemblies (96ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (154ms)
			TypeCache.Refresh (152ms)
				TypeCache.ScanAssembly (135ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (216ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (173ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (118ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.657 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.517 seconds
Domain Reload Profiling: 1174ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (425ms)
		LoadAssemblies (311ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (198ms)
			TypeCache.Refresh (176ms)
				TypeCache.ScanAssembly (152ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (518ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (422ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (39ms)
			ProcessInitializeOnLoadAttributes (318ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5131 Unused Serialized files (Serialized files now loaded: 0)
Unloading 29 unused Assets / (346.8 KB). Loaded Objects now: 5609.
Memory consumption went from 221.2 MB to 220.9 MB.
Total: 5.841900 ms (FindLiveObjects: 0.457400 ms CreateObjectMapping: 0.106600 ms MarkObjects: 4.859000 ms  DeleteObjects: 0.418300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26311.444656 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(PreviewImporter)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(PreviewImporter)  -> (artifact id: '969fc9552b66735c07cd266df58661e3') in 0.030747 seconds
Number of asset objects unloaded after import = 1
