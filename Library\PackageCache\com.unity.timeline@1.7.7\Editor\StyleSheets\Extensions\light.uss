.Icon-Activation {
    /* GUIStyle.fixedWidth */
    width: 10;

    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineActivation.png");
}

.trackRecordButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackRecordButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackRecordButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackRecordButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackAvatarMaskButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackAvatarButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/AvatarMaskDisabled.png");
}

.trackAvatarMaskButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackAvatarButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackAvatarButtonEnabled.png");
}

.Icon-ClipIn {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineIconClipIn.png");
}

.Icon-ClipOut {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineIconClipOut.png");
}

.Icon-ClipSelected {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineClipGradientSelected.png");
}

.trackCurvesButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackCurvesButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackCurvesButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackCurvesButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.Icon-Endmarker {
    /* GUIState.textColor */
    color: rgb(57, 122, 234);
}

.MarkerItem {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineMarkerItemCollapsed.png");
}

.MarkerItem:checked {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineMarkerItem.png");
}

.MarkerItem:hover:focus:checked {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineMarkerItemSelected.png");
}

.SignalEmitter {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineSignalCollapsed.png");
}

.SignalEmitter:checked {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TimelineSignal.png");
}

.SignalEmitter:hover:focus:checked {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineSignalSelected.png");
}

.trackCollapseMarkerButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackMarkerButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackCollapseMarkerButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackMarkerButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.Icon-ExtrapolationContinue {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineContinue.png");
}

.Icon-ExtrapolationHold {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineHold.png");
}

.Icon-ExtrapolationLoop {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineLoop.png");
}

.Icon-ExtrapolationPingPong {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelinePingPong.png");
}

.Icon-Foldout {
    /* GUIState.background */
    background-image: resource("Builtin Skins/LightSkin/Images/IN foldout.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/LightSkin/Images/IN <EMAIL>");
}

/* GUIStyle.active */
.Icon-Foldout:hover:active {
    /* GUIState.background */
    background-image: resource("Builtin Skins/LightSkin/Images/IN foldout on.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/LightSkin/Images/<NAME_EMAIL>");
}

/* GUIStyle.onActive */
.Icon-Foldout:hover:active:checked {
    /* GUIState.background */
    background-image: resource("Builtin Skins/LightSkin/Images/IN foldout on.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/LightSkin/Images/<NAME_EMAIL>");
}

/* GUIStyle.onNormal */
.Icon-Foldout:checked {
    /* GUIState.background */
    background-image: resource("Builtin Skins/LightSkin/Images/IN foldout on.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/LightSkin/Images/<NAME_EMAIL>");
}

.Icon-InfiniteTrack {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineInfiniteTrackNoShadow.png");
}

.Icon-Keyframe {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineKeyframe.png");
}

.trackLockOverlay {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineDisabledBackground.png");
}

.trackMuteButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackMuteButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackMuteButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackMuteButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackLockButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackLockButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackLockButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/TrackLockButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.Icon-Options {
    /* GUIState.background */
    background-image: resource("Icons/_Popup.png");
}

.Icon-PlayAreaEnd {
    /* GUIState.textColor */
    color: rgba(255, 255, 255, 0.2);

    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineEndPlayback.png");
}

.Icon-PlayAreaStart {
    /* GUIState.textColor */
    color: rgba(255, 255, 255, 0.21);

    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineStartPlayback.png");
}

.Icon-Playrange {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelinePlayRange.png");
}

.Icon-TimeCursor {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/LightSkin/TimelineTimeCursor.png");
}

.Icon-TrackOptions {
    /* GUIStyle.imagePosition */
    -unity-image-position: image-above;

    /* GUIState.background */
    background-image: resource("Builtin Skins/LightSkin/Images/pane options.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/LightSkin/Images/pane <EMAIL>");
}

.sequenceGroupFont {
    /* GUIState.textColor */
    color: rgb(255, 255, 255);
}

.Font-Clip {
    /* GUIStyleState.textColor */
    color: rgb(0, 0, 0);
}

.sequenceSwitcher{
    background-color: rgb(208, 208, 208);
}

.trackButtonSuite {
    background-color: rgba(241, 241, 241, 0.7);
}
