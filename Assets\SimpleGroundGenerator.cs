using UnityEngine;

public class SimpleGroundGenerator : MonoBehaviour
{
    [Header("Ground Settings")]
    public Vector3 groundSize = new Vector3(100f, 0.1f, 100f);
    public Material groundMaterial;
    public bool createWalls = false;
    public bool useGridTexture = true;

    void Start()
    {
        CreateGround();
    }

    void CreateGround()
    {
        // Create ground GameObject
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "SimpleGreenGround";
        ground.transform.position = new Vector3(0, 0, 0);
        ground.transform.localScale = new Vector3(groundSize.x / 10f, 1f, groundSize.z / 10f); // Plane默认是10x10单位

        // Apply material if provided
        if (groundMaterial != null)
        {
            ground.GetComponent<Renderer>().material = groundMaterial;
        }
        else
        {
            // Create a green grass-like material
            Material grassMaterial = CreateGrassMaterial();
            ground.GetComponent<Renderer>().material = grassMaterial;
        }

        // Add some walls for testing (optional)
        if (createWalls)
        {
            CreateWalls();
        }
    }

    Material CreateGrassMaterial()
    {
        Material grassMaterial = new Material(Shader.Find("Standard"));

        if (useGridTexture)
        {
            // Create a bright green color with grid-like appearance
            grassMaterial.color = new Color(0.2f, 0.8f, 0.2f); // Bright green
            grassMaterial.mainTextureScale = new Vector2(50f, 50f); // Tiling for grid effect
        }
        else
        {
            // Simple flat green color
            grassMaterial.color = new Color(0.3f, 0.7f, 0.3f); // Grass green
        }

        // Make it less shiny
        grassMaterial.SetFloat("_Metallic", 0f);
        grassMaterial.SetFloat("_Glossiness", 0.2f);

        return grassMaterial;
    }

    void CreateWalls()
    {
        // Create 4 walls around the ground
        Vector3 wallScale = new Vector3(1f, 5f, groundSize.z + 2f);

        // Left wall
        GameObject leftWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        leftWall.name = "LeftWall";
        leftWall.transform.position = new Vector3(-groundSize.x/2 - 0.5f, 2f, 0);
        leftWall.transform.localScale = wallScale;

        // Right wall
        GameObject rightWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        rightWall.name = "RightWall";
        rightWall.transform.position = new Vector3(groundSize.x/2 + 0.5f, 2f, 0);
        rightWall.transform.localScale = wallScale;

        // Front wall
        wallScale = new Vector3(groundSize.x + 2f, 5f, 1f);
        GameObject frontWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        frontWall.name = "FrontWall";
        frontWall.transform.position = new Vector3(0, 2f, groundSize.z/2 + 0.5f);
        frontWall.transform.localScale = wallScale;

        // Back wall
        GameObject backWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        backWall.name = "BackWall";
        backWall.transform.position = new Vector3(0, 2f, -groundSize.z/2 - 0.5f);
        backWall.transform.localScale = wallScale;

        // Apply material to walls
        Material wallMaterial = new Material(Shader.Find("Standard"));
        wallMaterial.color = new Color(0.8f, 0.6f, 0.4f); // Light brown

        leftWall.GetComponent<Renderer>().material = wallMaterial;
        rightWall.GetComponent<Renderer>().material = wallMaterial;
        frontWall.GetComponent<Renderer>().material = wallMaterial;
        backWall.GetComponent<Renderer>().material = wallMaterial;
    }
}
