using UnityEngine;

public class CheckerboardGround : MonoBehaviour
{
    [Header("棋盘格地面设置")]
    public float groundSize = 200f;
    public int checkerSize = 10; // 每个格子的大小
    public Color color1 = new Color(0.2f, 0.8f, 0.2f); // 绿色
    public Color color2 = new Color(0.7f, 0.7f, 0.7f); // 灰色
    
    void Start()
    {
        CreateCheckerboardGround();
    }
    
    void CreateCheckerboardGround()
    {
        // 创建地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "CheckerboardGround";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(groundSize / 10f, 1f, groundSize / 10f);
        
        // 创建棋盘格材质
        Material checkerMaterial = CreateCheckerboardMaterial();
        ground.GetComponent<Renderer>().material = checkerMaterial;
        
        // 添加碰撞体
        if (ground.GetComponent<MeshCollider>() == null)
        {
            ground.AddComponent<MeshCollider>();
        }
        
        // 设置环境
        SetupEnvironment();
        
        Debug.Log("绿灰棋盘格地面创建完成！");
    }
    
    Material CreateCheckerboardMaterial()
    {
        // 尝试不同的着色器
        Material material = null;
        string[] shaderNames = {
            "Universal Render Pipeline/Lit",
            "Standard",
            "Mobile/Diffuse",
            "Unlit/Texture"
        };
        
        foreach (string shaderName in shaderNames)
        {
            Shader shader = Shader.Find(shaderName);
            if (shader != null)
            {
                material = new Material(shader);
                break;
            }
        }
        
        if (material == null)
        {
            material = new Material(Shader.Find("Sprites/Default"));
        }
        
        // 创建棋盘格纹理
        Texture2D checkerTexture = CreateCheckerboardTexture();
        material.mainTexture = checkerTexture;
        
        // 设置材质属性
        if (material.HasProperty("_Metallic"))
            material.SetFloat("_Metallic", 0f);
        if (material.HasProperty("_Glossiness"))
            material.SetFloat("_Glossiness", 0.1f);
        if (material.HasProperty("_Smoothness"))
            material.SetFloat("_Smoothness", 0.1f);
        
        // 设置纹理平铺 - 根据地面大小和格子大小计算
        float tiling = groundSize / checkerSize;
        material.mainTextureScale = new Vector2(tiling, tiling);
        
        return material;
    }
    
    Texture2D CreateCheckerboardTexture()
    {
        int textureSize = 64; // 纹理大小
        Texture2D texture = new Texture2D(textureSize, textureSize);
        
        int halfSize = textureSize / 2;
        
        for (int x = 0; x < textureSize; x++)
        {
            for (int y = 0; y < textureSize; y++)
            {
                // 创建2x2的棋盘格模式
                bool isColor1 = ((x < halfSize) && (y < halfSize)) || 
                               ((x >= halfSize) && (y >= halfSize));
                
                Color pixelColor = isColor1 ? color1 : color2;
                texture.SetPixel(x, y, pixelColor);
            }
        }
        
        texture.Apply();
        texture.filterMode = FilterMode.Point; // 保持清晰的边缘
        texture.wrapMode = TextureWrapMode.Repeat;
        
        return texture;
    }
    
    void SetupEnvironment()
    {
        // 设置天空颜色
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            mainCamera.backgroundColor = new Color(0.5f, 0.8f, 1f);
            mainCamera.clearFlags = CameraClearFlags.SolidColor;
        }
    }
    
    // 实时更新功能
    void OnValidate()
    {
        if (Application.isPlaying)
        {
            GameObject existingGround = GameObject.Find("CheckerboardGround");
            if (existingGround != null)
            {
                Material newMaterial = CreateCheckerboardMaterial();
                existingGround.GetComponent<Renderer>().material = newMaterial;
            }
        }
    }
    
    // 手动重新创建
    [ContextMenu("重新创建棋盘格地面")]
    void RecreateCheckerboard()
    {
        GameObject existingGround = GameObject.Find("CheckerboardGround");
        if (existingGround != null)
        {
            DestroyImmediate(existingGround);
        }
        CreateCheckerboardGround();
    }
}
