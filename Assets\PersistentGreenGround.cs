using UnityEngine;

public class PersistentGreenGround : MonoBehaviour
{
    [Header("持久绿色地面设置")]
    public float groundSize = 200f;
    public Color groundColor = new Color(0.3f, 0.8f, 0.3f);
    
    private static bool groundCreated = false;
    private GameObject groundParent;
    
    void Start()
    {
        // 只创建一次地面
        if (!groundCreated)
        {
            CreatePersistentGround();
            groundCreated = true;
        }
        else
        {
            // 如果地面已存在，销毁这个多余的生成器
            Destroy(gameObject);
        }
    }
    
    void CreatePersistentGround()
    {
        // 创建一个父对象来管理所有地面
        groundParent = new GameObject("PersistentGroundSystem");
        
        // 创建地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "PersistentGreenGround";
        ground.transform.SetParent(groundParent.transform);
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(groundSize / 10f, 1f, groundSize / 10f);
        
        // 设置材质
        SetupGroundMaterial(ground);
        
        // 确保有碰撞体
        if (ground.GetComponent<MeshCollider>() == null)
        {
            ground.AddComponent<MeshCollider>();
        }
        
        // 设置环境
        SetupEnvironment();
        
        Debug.Log("持久绿色地面创建完成！");
    }
    
    void SetupGroundMaterial(GameObject ground)
    {
        Renderer renderer = ground.GetComponent<Renderer>();
        
        // 创建新材质避免修改默认材质
        Material groundMaterial = new Material(renderer.material);
        groundMaterial.name = "PersistentGreenMaterial";
        groundMaterial.color = groundColor;
        
        // 设置材质属性
        if (groundMaterial.HasProperty("_Metallic"))
            groundMaterial.SetFloat("_Metallic", 0f);
        if (groundMaterial.HasProperty("_Glossiness"))
            groundMaterial.SetFloat("_Glossiness", 0.1f);
        if (groundMaterial.HasProperty("_Smoothness"))
            groundMaterial.SetFloat("_Smoothness", 0.1f);
            
        renderer.material = groundMaterial;
    }
    
    void SetupEnvironment()
    {
        // 设置摄像机背景
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            mainCamera.backgroundColor = new Color(0.5f, 0.8f, 1f);
            mainCamera.clearFlags = CameraClearFlags.SolidColor;
        }
    }
    
    void OnDestroy()
    {
        // 当脚本被销毁时，重置标志
        groundCreated = false;
    }
    
    // 手动重置方法
    [ContextMenu("重置地面系统")]
    public void ResetGroundSystem()
    {
        // 删除现有地面
        if (groundParent != null)
        {
            DestroyImmediate(groundParent);
        }
        
        // 查找并删除其他可能的地面对象
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.name.Contains("Ground") && obj != gameObject)
            {
                DestroyImmediate(obj);
            }
        }
        
        // 重置标志并重新创建
        groundCreated = false;
        CreatePersistentGround();
        groundCreated = true;
    }
}
