using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class PRSSamples
{
    public string introduction;
    public PRSSample[] samples;
    private Dictionary<GameObject, int> prefabToSample;

    public static PRSSamples CreateFromJSON(string jsonString, GameObject[] prefabs = null)
    {
        var newSamples = JsonUtility.FromJson<PRSSamples>(jsonString);

        if (prefabs != null)
        {
            newSamples.prefabToSample = new Dictionary<GameObject, int>();

            foreach (var go in prefabs)
            {
                int index = System.Array.FindIndex(newSamples.samples, s => s.prefabName == jsonString);
                if (index >= 0)
                    newSamples.prefabToSample.Add(go, index);
            }
        }

        return newSamples;
    }
    
    public PRSSample FindSampleWithPrefab(GameObject prefab)
    {
        if ( prefabToSample.ContainsKey(prefab) )
            return samples[prefabToSample[prefab]];

        foreach(PRSSample sample in samples)
            if (sample.prefabName == prefab.name)
                return sample;
        
        Debug.LogWarning($"Sample not found with prefabName: {prefab.name}");
        return null;
    }
}

[System.Serializable]
public class PRSSample
{
    public string title;
    public string prefabName;
    public string description;
}