using UnityEngine;

public class SimpleGreenGround : MonoBehaviour
{
    [Header("简单绿色地面")]
    public float groundSize = 200f;
    public Color groundColor = Color.green;
    
    void Start()
    {
        CreateSimpleGround();
    }
    
    void CreateSimpleGround()
    {
        // 创建地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "SimpleGreenGround";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(groundSize / 10f, 1f, groundSize / 10f);
        
        // 获取渲染器
        Renderer renderer = ground.GetComponent<Renderer>();
        
        // 直接设置颜色，不依赖特定着色器
        renderer.material.color = groundColor;
        
        // 确保有碰撞体
        if (ground.GetComponent<MeshCollider>() == null)
        {
            ground.AddComponent<MeshCollider>();
        }
        
        // 设置简单的天空
        if (Camera.main != null)
        {
            Camera.main.backgroundColor = new Color(0.5f, 0.8f, 1f);
            Camera.main.clearFlags = CameraClearFlags.SolidColor;
        }
        
        Debug.Log("简单绿色地面创建完成！");
    }
}
