using UnityEngine;

public class FinalGreenGround : MonoBehaviour
{
    [Header("最终绿色地面")]
    public float groundSize = 200f;
    public Color groundColor = new Color(0.3f, 0.8f, 0.3f);
    
    void Start()
    {
        // 彻底清理所有可能的地面对象
        CleanAllGrounds();
        
        // 创建唯一的绿色地面
        CreateFinalGround();
        
        Debug.Log("最终绿色地面创建完成！");
    }
    
    void CleanAllGrounds()
    {
        // 删除所有可能的地面对象名称
        string[] possibleNames = {
            "Ground", "Plane", "Floor", "Quad",
            "SuperFlatGround", "SimpleGreenGround", "CleanGreenGround",
            "GreenGrid", "PersistentGreenGround", "SimpleGround"
        };
        
        foreach (string name in possibleNames)
        {
            GameObject obj = GameObject.Find(name);
            if (obj != null && obj != gameObject)
            {
                Debug.Log($"删除对象: {name}");
                DestroyImmediate(obj);
            }
        }
        
        // 删除所有包含"Ground"、"Tile"关键词的对象
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj == gameObject) continue; // 跳过自己
            
            string objName = obj.name.ToLower();
            if (objName.Contains("ground") || objName.Contains("tile") || 
                objName.Contains("plane") || objName.Contains("floor"))
            {
                Debug.Log($"删除地面相关对象: {obj.name}");
                DestroyImmediate(obj);
            }
        }
    }
    
    void CreateFinalGround()
    {
        // 创建最终的绿色地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "FinalGreenGround_" + System.DateTime.Now.Ticks; // 唯一名称
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(groundSize / 10f, 1f, groundSize / 10f);
        
        // 设置材质
        Renderer renderer = ground.GetComponent<Renderer>();
        Material groundMaterial = new Material(renderer.material);
        groundMaterial.name = "FinalGreenMaterial";
        groundMaterial.color = groundColor;
        renderer.material = groundMaterial;
        
        // 添加碰撞体
        if (ground.GetComponent<MeshCollider>() == null)
        {
            ground.AddComponent<MeshCollider>();
        }
        
        // 设置环境
        SetupEnvironment();
    }
    
    void SetupEnvironment()
    {
        // 设置天空颜色
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            mainCamera.backgroundColor = new Color(0.5f, 0.8f, 1f);
            mainCamera.clearFlags = CameraClearFlags.SolidColor;
        }
        
        // 设置光照
        Light directionalLight = FindObjectOfType<Light>();
        if (directionalLight != null)
        {
            directionalLight.color = Color.white;
            directionalLight.intensity = 1f;
        }
    }
    
    // 在Inspector中提供手动清理功能
    [ContextMenu("彻底清理并重新创建")]
    void ForceRecreate()
    {
        CleanAllGrounds();
        CreateFinalGround();
    }
}
