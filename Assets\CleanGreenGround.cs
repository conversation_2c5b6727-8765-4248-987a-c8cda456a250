using UnityEngine;

public class CleanGreenGround : MonoBehaviour
{
    [Header("清洁绿色地面设置")]
    public float groundSize = 200f;
    public Color groundColor = new Color(0.3f, 0.8f, 0.3f);
    public bool removeExistingGround = true;
    
    void Start()
    {
        if (removeExistingGround)
        {
            CleanExistingGround();
        }
        CreateCleanGreenGround();
    }
    
    void CleanExistingGround()
    {
        // 查找并删除可能存在的地面对象
        string[] groundNames = { "Plane", "Ground", "Floor", "Quad" };
        
        foreach (string groundName in groundNames)
        {
            GameObject existingGround = GameObject.Find(groundName);
            if (existingGround != null)
            {
                Debug.Log($"删除现有地面对象: {groundName}");
                DestroyImmediate(existingGround);
            }
        }
        
        // 查找所有带有"ground"、"plane"、"floor"关键词的对象
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            string objName = obj.name.ToLower();
            if ((objName.Contains("ground") || objName.Contains("plane") || objName.Contains("floor")) 
                && obj != this.gameObject)
            {
                Debug.Log($"删除地面相关对象: {obj.name}");
                DestroyImmediate(obj);
            }
        }
    }
    
    void CreateCleanGreenGround()
    {
        // 创建新的干净地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "CleanGreenGround";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(groundSize / 10f, 1f, groundSize / 10f);
        
        // 设置材质
        Renderer renderer = ground.GetComponent<Renderer>();
        Material groundMaterial = new Material(renderer.material);
        groundMaterial.color = groundColor;
        renderer.material = groundMaterial;
        
        // 确保有碰撞体
        if (ground.GetComponent<MeshCollider>() == null)
        {
            ground.AddComponent<MeshCollider>();
        }
        
        // 设置天空
        SetupEnvironment();
        
        Debug.Log("干净的绿色地面创建完成！");
    }
    
    void SetupEnvironment()
    {
        // 设置摄像机背景
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            mainCamera.backgroundColor = new Color(0.5f, 0.8f, 1f); // 浅蓝色
            mainCamera.clearFlags = CameraClearFlags.SolidColor;
        }
        
        // 设置光照
        Light directionalLight = FindObjectOfType<Light>();
        if (directionalLight != null)
        {
            directionalLight.color = Color.white;
            directionalLight.intensity = 1f;
        }
    }
    
    // 手动清理按钮（在Inspector中显示）
    [ContextMenu("清理并重新创建地面")]
    void RecreateGround()
    {
        CleanExistingGround();
        CreateCleanGreenGround();
    }
}
