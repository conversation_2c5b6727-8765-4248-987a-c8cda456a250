using UnityEngine;

public class CursorManager : MonoBehaviour
{
    [Header("Cursor Settings")]
    public KeyCode toggleCursorKey = KeyCode.Escape;

    private bool isCursorLocked = true;

    void Start()
    {
        // Lock cursor at start
        SetCursorState(true);
    }

    void Update()
    {
        // Toggle cursor lock state
        if (Input.GetKeyDown(toggleCursorKey))
        {
            ToggleCursorLock();
        }
    }

    public void ToggleCursorLock()
    {
        isCursorLocked = !isCursorLocked;
        SetCursorState(isCursorLocked);
    }

    public void SetCursorState(bool locked)
    {
        isCursorLocked = locked;

        if (locked)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
        else
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
    }

    public bool IsCursorLocked()
    {
        return isCursorLocked;
    }
}
