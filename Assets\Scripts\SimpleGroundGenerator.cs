using UnityEngine;

public class SimpleGroundGenerator : MonoBehaviour
{
    [Header("Ground Settings")]
    public Vector3 groundSize = new Vector3(20f, 1f, 20f);
    public Material groundMaterial;
    
    void Start()
    {
        CreateGround();
    }
    
    void CreateGround()
    {
        // Create ground GameObject
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Cube);
        ground.name = "Ground";
        ground.transform.position = new Vector3(0, -0.5f, 0);
        ground.transform.localScale = groundSize;
        
        // Apply material if provided
        if (groundMaterial != null)
        {
            ground.GetComponent<Renderer>().material = groundMaterial;
        }
        else
        {
            // Create a simple gray material
            Material defaultMaterial = new Material(Shader.Find("Standard"));
            defaultMaterial.color = Color.gray;
            ground.GetComponent<Renderer>().material = defaultMaterial;
        }
        
        // Add some walls for testing
        CreateWalls();
    }
    
    void CreateWalls()
    {
        // Create 4 walls around the ground
        Vector3 wallScale = new Vector3(1f, 5f, groundSize.z + 2f);
        
        // Left wall
        GameObject leftWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        leftWall.name = "LeftWall";
        leftWall.transform.position = new Vector3(-groundSize.x/2 - 0.5f, 2f, 0);
        leftWall.transform.localScale = wallScale;
        
        // Right wall
        GameObject rightWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        rightWall.name = "RightWall";
        rightWall.transform.position = new Vector3(groundSize.x/2 + 0.5f, 2f, 0);
        rightWall.transform.localScale = wallScale;
        
        // Front wall
        wallScale = new Vector3(groundSize.x + 2f, 5f, 1f);
        GameObject frontWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        frontWall.name = "FrontWall";
        frontWall.transform.position = new Vector3(0, 2f, groundSize.z/2 + 0.5f);
        frontWall.transform.localScale = wallScale;
        
        // Back wall
        GameObject backWall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        backWall.name = "BackWall";
        backWall.transform.position = new Vector3(0, 2f, -groundSize.z/2 - 0.5f);
        backWall.transform.localScale = wallScale;
        
        // Apply material to walls
        Material wallMaterial = new Material(Shader.Find("Standard"));
        wallMaterial.color = new Color(0.8f, 0.6f, 0.4f); // Light brown
        
        leftWall.GetComponent<Renderer>().material = wallMaterial;
        rightWall.GetComponent<Renderer>().material = wallMaterial;
        frontWall.GetComponent<Renderer>().material = wallMaterial;
        backWall.GetComponent<Renderer>().material = wallMaterial;
    }
}
