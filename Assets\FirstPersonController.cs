using UnityEngine;

public class FirstPersonController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float walkSpeed = 5f;
    public float runSpeed = 10f;
    public float jumpHeight = 2f;
    public float gravity = -9.81f;

    [Header("Mouse Look Settings")]
    public float mouseSensitivity = 2f;
    public float maxLookAngle = 80f;

    [Header("Camera Settings")]
    public bool isThirdPerson = false;
    public Vector3 thirdPersonOffset = new Vector3(0, 2, -3);
    public KeyCode toggleViewKey = KeyCode.V;

    [Header("Character Model")]
    public GameObject characterModel; // 引用PMX角色模型
    public bool hideModelInFirstPerson = true;

    [Header("Ground Check")]
    public Transform groundCheck;
    public float groundDistance = 0.4f;
    public LayerMask groundMask = 1;

    // Private variables
    private CharacterController controller;
    private Camera playerCamera;
    private CursorManager cursorManager;
    private Vector3 velocity;
    private bool isGrounded;
    private float xRotation = 0f;

    void Start()
    {
        // Get components
        controller = GetComponent<CharacterController>();
        playerCamera = GetComponentInChildren<Camera>();
        cursorManager = GetComponent<CursorManager>();

        // Setup character model
        SetupCharacterModel();

        // Create ground check if it doesn't exist
        if (groundCheck == null)
        {
            GameObject groundCheckObj = new GameObject("GroundCheck");
            groundCheckObj.transform.SetParent(transform);
            groundCheckObj.transform.localPosition = new Vector3(0, -1f, 0);
            groundCheck = groundCheckObj.transform;
        }
    }

    void SetupCharacterModel()
    {
        if (characterModel != null)
        {
            // 确保角色模型是当前对象的子对象
            if (characterModel.transform.parent != transform)
            {
                characterModel.transform.SetParent(transform);
            }

            // 重置角色模型的本地位置和旋转
            characterModel.transform.localPosition = Vector3.zero;
            characterModel.transform.localRotation = Quaternion.identity;

            // 根据当前视角模式设置角色可见性
            SetCharacterVisibility(isThirdPerson);

            // 设置初始摄像机位置
            UpdateCameraPosition();

            Debug.Log($"角色模型 {characterModel.name} 已设置完成");
        }
    }

    void SetCharacterVisibility(bool visible)
    {
        if (characterModel != null)
        {
            // 获取所有渲染器组件
            Renderer[] renderers = characterModel.GetComponentsInChildren<Renderer>();
            foreach (Renderer renderer in renderers)
            {
                renderer.enabled = visible;
            }
        }
    }

    void Update()
    {
        HandleViewToggle();
        HandleMouseLook();
        HandleMovement();
        HandleJump();
    }

    void HandleViewToggle()
    {
        // 切换第一人称/第三人称视角
        if (Input.GetKeyDown(toggleViewKey))
        {
            isThirdPerson = !isThirdPerson;
            UpdateCameraPosition();
            SetCharacterVisibility(isThirdPerson);
        }
    }

    void UpdateCameraPosition()
    {
        if (playerCamera == null)
        {
            Debug.LogWarning("PlayerCamera is null! Cannot update camera position.");
            return;
        }

        if (isThirdPerson)
        {
            // 第三人称视角
            playerCamera.transform.localPosition = thirdPersonOffset;
        }
        else
        {
            // 第一人称视角
            playerCamera.transform.localPosition = new Vector3(0, 1.8f, 0);
        }
    }

    void HandleMouseLook()
    {
        // Only handle mouse look if cursor is locked
        if (cursorManager != null && !cursorManager.IsCursorLocked())
            return;

        // Check if camera exists
        if (playerCamera == null)
        {
            Debug.LogWarning("PlayerCamera is null! Please assign a camera as child object.");
            return;
        }

        // Get mouse input
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;

        // Rotate the player body left and right
        transform.Rotate(Vector3.up * mouseX);

        // Rotate the camera up and down
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -maxLookAngle, maxLookAngle);
        playerCamera.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
    }

    void HandleMovement()
    {
        // Check if grounded
        isGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);

        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f; // Small negative value to keep grounded
        }

        // Get input
        float horizontal = Input.GetAxis("Horizontal"); // A/D keys
        float vertical = Input.GetAxis("Vertical");     // W/S keys

        // Calculate movement direction
        Vector3 direction = transform.right * horizontal + transform.forward * vertical;

        // Determine speed (running or walking)
        float currentSpeed = Input.GetKey(KeyCode.LeftShift) ? runSpeed : walkSpeed;

        // Move the character
        controller.Move(direction * currentSpeed * Time.deltaTime);
    }

    void HandleJump()
    {
        // Jump
        if (Input.GetButtonDown("Jump") && isGrounded)
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
        }

        // Apply gravity
        velocity.y += gravity * Time.deltaTime;
        controller.Move(velocity * Time.deltaTime);
    }

    void OnDrawGizmosSelected()
    {
        // Draw ground check sphere in scene view
        if (groundCheck != null)
        {
            Gizmos.color = isGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundDistance);
        }
    }
}
