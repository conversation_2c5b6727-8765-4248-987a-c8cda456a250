{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "a9222f2f14844a8ba8b63420c5e3cc98",
    "m_Properties": [
        {
            "m_Id": "c1caf9bb6d6148ad9023d0ffc25210e8"
        },
        {
            "m_Id": "c82f9722adfb48518ebf4c9fc721e554"
        },
        {
            "m_Id": "a1473c8a53df417bb0c0e74bac0ba06c"
        },
        {
            "m_Id": "53689255819a45759e7f2c64d06715c3"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "d3bc17828bd1496a89efedfe263041d6"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "316082aecac94b328d2b33c99aa24fc0"
        },
        {
            "m_Id": "97ce2477b6e7423aadbb2dc797594fc3"
        },
        {
            "m_Id": "09920086ea2c4fcb84ce6abaac755478"
        },
        {
            "m_Id": "74dd0d7ea1df4e8b989860b9a0356edf"
        },
        {
            "m_Id": "cd48247096c846a2b681a9270d5e3970"
        },
        {
            "m_Id": "09f2640553d84f309d116ef497098226"
        },
        {
            "m_Id": "6378538fc72143a7aa5c86a4ce39a2b0"
        },
        {
            "m_Id": "46e6c758b4fe4c0d9e817c04851e9021"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "40bf01c61961495485e6eb2cbc50d494"
        },
        {
            "m_Id": "3676a5de6e794d738b31df3a41c906b3"
        },
        {
            "m_Id": "42cb89b7267d4fcdbb7fd910c926e803"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "09920086ea2c4fcb84ce6abaac755478"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "74dd0d7ea1df4e8b989860b9a0356edf"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "09f2640553d84f309d116ef497098226"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "cd48247096c846a2b681a9270d5e3970"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "46e6c758b4fe4c0d9e817c04851e9021"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "09f2640553d84f309d116ef497098226"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6378538fc72143a7aa5c86a4ce39a2b0"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "09f2640553d84f309d116ef497098226"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "74dd0d7ea1df4e8b989860b9a0356edf"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "09f2640553d84f309d116ef497098226"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "97ce2477b6e7423aadbb2dc797594fc3"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "74dd0d7ea1df4e8b989860b9a0356edf"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "cd48247096c846a2b681a9270d5e3970"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "316082aecac94b328d2b33c99aa24fc0"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "316082aecac94b328d2b33c99aa24fc0"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "09428ede758545b086b6651c64c38433",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "09920086ea2c4fcb84ce6abaac755478",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -615.0000610351563,
            "y": 172.5,
            "width": 111.75006103515625,
            "height": 33.000030517578128
        }
    },
    "m_Slots": [
        {
            "m_Id": "e3761f18877c42d6b5d6b6e719331cc0"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "c82f9722adfb48518ebf4c9fc721e554"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.InverseLerpNode",
    "m_ObjectId": "09f2640553d84f309d116ef497098226",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Inverse Lerp",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -269.25006103515627,
            "y": -0.7499959468841553,
            "width": 122.25006103515625,
            "height": 140.25001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "86c226e85c1147e0bb166bb1b6ee08d1"
        },
        {
            "m_Id": "34e46302749e43b3a45cd0e89735e41f"
        },
        {
            "m_Id": "185f5de5581f40c3801033e88b2b9c80"
        },
        {
            "m_Id": "95a0ca0b193343f0b6976ff587477094"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "185f5de5581f40c3801033e88b2b9c80",
    "m_Id": 2,
    "m_DisplayName": "T",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "T",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "316082aecac94b328d2b33c99aa24fc0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": -1.0,
            "width": 119.0,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "7ec012e5977548ffa563158135fee5d3"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "34e46302749e43b3a45cd0e89735e41f",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.30000001192092898,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "3676a5de6e794d738b31df3a41c906b3",
    "m_Title": "Dot Product",
    "m_Content": "The MaskVector input is set to (0,1,0) - which is a vector pointing in the up direction (positive Y). When the object’s surface (Normal) is pointing in that direction, the mask is white.  When the surface is pointing away from that direction, the mask is black.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -539.0,
        "y": 216.0,
        "width": 256.0,
        "height": 135.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "40bf01c61961495485e6eb2cbc50d494",
    "m_Title": "Min & Max",
    "m_Content": "The closer together the Min and Max values the sharper the falloff and vice versa. Min and Max should be greater than 0 and less than 1.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -320.0,
        "y": -135.0,
        "width": 179.61700439453126,
        "height": 132.0870361328125
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "42cb89b7267d4fcdbb7fd910c926e803",
    "m_Title": "Angle Mask",
    "m_Content": "The Angle Mask subgraph uses the direction that a surface is facing to determine if the mask should be black or white. If the surface is pointing in the direction of the given input vector, the mask is white.If it’s pointing away from the given vector, the mask is black.\r\n",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -735.0,
        "y": -146.0,
        "width": 221.17205810546876,
        "height": 206.10208129882813
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "45e3dfcdb6c14d57bb3e7e49566e1cf1",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "46e6c758b4fe4c0d9e817c04851e9021",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -432.0,
            "y": 8.25001049041748,
            "width": 91.49996948242188,
            "height": 32.99998092651367
        }
    },
    "m_Slots": [
        {
            "m_Id": "e24dc28a68094fe5a1d72ecc7a9fbf61"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "53689255819a45759e7f2c64d06715c3"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4f1714a624034c81808894ec59fc1fd4",
    "m_Id": 0,
    "m_DisplayName": "Max",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "53689255819a45759e7f2c64d06715c3",
    "m_Guid": {
        "m_GuidSerialized": "cc535979-ad62-4bae-8c48-308289d74fcc"
    },
    "m_Name": "Min",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Min",
    "m_DefaultReferenceName": "_Min",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.20000000298023225,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "6378538fc72143a7aa5c86a4ce39a2b0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -432.0,
            "y": 41.24999237060547,
            "width": 94.5,
            "height": 33.000022888183597
        }
    },
    "m_Slots": [
        {
            "m_Id": "4f1714a624034c81808894ec59fc1fd4"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "a1473c8a53df417bb0c0e74bac0ba06c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "63ae8c6b7c3c4166b4327c39d08edb13",
    "m_Id": 0,
    "m_DisplayName": "MaskVector",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DotProductNode",
    "m_ObjectId": "74dd0d7ea1df4e8b989860b9a0356edf",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Dot Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -476.2500305175781,
            "y": 93.75003051757813,
            "width": 123.75,
            "height": 116.25
        }
    },
    "m_Slots": [
        {
            "m_Id": "09428ede758545b086b6651c64c38433"
        },
        {
            "m_Id": "8934deb70bcc49879e6c1c6923b47300"
        },
        {
            "m_Id": "45e3dfcdb6c14d57bb3e7e49566e1cf1"
        }
    ],
    "synonyms": [
        "scalar product"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "7c302ca1a96b467eac8abb7d46fe8199",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7ec012e5977548ffa563158135fee5d3",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "86c226e85c1147e0bb166bb1b6ee08d1",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.20000000298023225,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "88d599de12d84bcb8a2c37f946ea6dc5",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "8934deb70bcc49879e6c1c6923b47300",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "95a0ca0b193343f0b6976ff587477094",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "97ce2477b6e7423aadbb2dc797594fc3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -639.7500610351563,
            "y": 127.50001525878906,
            "width": 136.50006103515626,
            "height": 33.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "63ae8c6b7c3c4166b4327c39d08edb13"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "c1caf9bb6d6148ad9023d0ffc25210e8"
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "a1473c8a53df417bb0c0e74bac0ba06c",
    "m_Guid": {
        "m_GuidSerialized": "bced1302-c859-4427-aabb-4b5645693e47"
    },
    "m_Name": "Max",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Max",
    "m_DefaultReferenceName": "_Max",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.30000001192092898,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty",
    "m_ObjectId": "c1caf9bb6d6148ad9023d0ffc25210e8",
    "m_Guid": {
        "m_GuidSerialized": "a26965d9-e1df-4f8e-b75f-ca984258937f"
    },
    "m_Name": "MaskVector",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "MaskVector",
    "m_DefaultReferenceName": "_MaskVector",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty",
    "m_ObjectId": "c82f9722adfb48518ebf4c9fc721e554",
    "m_Guid": {
        "m_GuidSerialized": "00dc5a2f-a34f-4edf-b593-e94864a97389"
    },
    "m_Name": "Normal",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Normal",
    "m_DefaultReferenceName": "_Normal",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "cd48247096c846a2b681a9270d5e3970",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -143.2499542236328,
            "y": -0.7499959468841553,
            "width": 123.74996185302735,
            "height": 92.2500228881836
        }
    },
    "m_Slots": [
        {
            "m_Id": "88d599de12d84bcb8a2c37f946ea6dc5"
        },
        {
            "m_Id": "7c302ca1a96b467eac8abb7d46fe8199"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "d3bc17828bd1496a89efedfe263041d6",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "c1caf9bb6d6148ad9023d0ffc25210e8"
        },
        {
            "m_Id": "c82f9722adfb48518ebf4c9fc721e554"
        },
        {
            "m_Id": "a1473c8a53df417bb0c0e74bac0ba06c"
        },
        {
            "m_Id": "53689255819a45759e7f2c64d06715c3"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e24dc28a68094fe5a1d72ecc7a9fbf61",
    "m_Id": 0,
    "m_DisplayName": "Min",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e3761f18877c42d6b5d6b6e719331cc0",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

