{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 32248, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 32248, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 32248, "tid": 10, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 32248, "tid": 10, "ts": 1753950974821857, "dur": 608, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 32248, "tid": 10, "ts": 1753950974825913, "dur": 884, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 32248, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 32248, "tid": 1, "ts": 1753950974669612, "dur": 3566, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 32248, "tid": 1, "ts": 1753950974673181, "dur": 29420, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 32248, "tid": 1, "ts": 1753950974702611, "dur": 29004, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 32248, "tid": 10, "ts": 1753950974826802, "dur": 623, "ph": "X", "name": "", "args": {}}, {"pid": 32248, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974668309, "dur": 5298, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974673609, "dur": 139446, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974674366, "dur": 2489, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974676861, "dur": 1012, "ph": "X", "name": "ProcessMessages 20506", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974677875, "dur": 150, "ph": "X", "name": "ReadAsync 20506", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678029, "dur": 20, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678051, "dur": 67, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678122, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678125, "dur": 65, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678193, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678244, "dur": 41, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678287, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678292, "dur": 50, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678345, "dur": 38, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678385, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678419, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678472, "dur": 31, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678507, "dur": 40, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678550, "dur": 39, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678590, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678593, "dur": 42, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678638, "dur": 42, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678683, "dur": 43, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678730, "dur": 42, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678775, "dur": 45, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678821, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678823, "dur": 40, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678866, "dur": 40, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678908, "dur": 35, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678945, "dur": 29, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974678975, "dur": 36, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679012, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679013, "dur": 45, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679060, "dur": 30, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679092, "dur": 44, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679139, "dur": 29, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679169, "dur": 29, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679199, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679241, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679243, "dur": 77, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679324, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679326, "dur": 17, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679344, "dur": 18, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679364, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679392, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679433, "dur": 41, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679476, "dur": 36, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679513, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679537, "dur": 21, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679560, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679582, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679606, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679641, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679643, "dur": 27, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679673, "dur": 36, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679712, "dur": 26, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679740, "dur": 27, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679770, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679795, "dur": 24, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679821, "dur": 29, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679852, "dur": 27, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679881, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679901, "dur": 18, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679920, "dur": 34, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679957, "dur": 36, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974679995, "dur": 43, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680039, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680040, "dur": 40, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680082, "dur": 49, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680133, "dur": 41, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680175, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680176, "dur": 40, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680218, "dur": 14, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680234, "dur": 25, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680261, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680294, "dur": 28, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680325, "dur": 30, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680356, "dur": 25, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680383, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680414, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680416, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680468, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680470, "dur": 22, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680495, "dur": 28, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680524, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680526, "dur": 34, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680562, "dur": 35, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680599, "dur": 27, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680627, "dur": 30, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680659, "dur": 48, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680708, "dur": 52, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680763, "dur": 34, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680798, "dur": 59, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680859, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680860, "dur": 57, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680919, "dur": 35, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680956, "dur": 41, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974680999, "dur": 31, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681031, "dur": 28, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681061, "dur": 28, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681091, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681092, "dur": 41, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681134, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681135, "dur": 33, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681170, "dur": 33, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681205, "dur": 33, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681240, "dur": 23, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681266, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681306, "dur": 38, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681345, "dur": 32, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681379, "dur": 36, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681416, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681418, "dur": 37, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681459, "dur": 35, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681495, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681496, "dur": 40, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681538, "dur": 34, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681574, "dur": 75, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681652, "dur": 41, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681695, "dur": 33, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681730, "dur": 36, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681768, "dur": 32, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681801, "dur": 25, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681829, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681859, "dur": 124, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974681986, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682036, "dur": 24, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682063, "dur": 44, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682110, "dur": 51, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682164, "dur": 62, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682227, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682230, "dur": 25, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682258, "dur": 35, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682299, "dur": 36, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682337, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974682340, "dur": 3934, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686277, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686279, "dur": 508, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686792, "dur": 14, "ph": "X", "name": "ProcessMessages 20497", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686806, "dur": 56, "ph": "X", "name": "ReadAsync 20497", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686867, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686869, "dur": 46, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686916, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686917, "dur": 32, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686951, "dur": 33, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974686986, "dur": 52, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687041, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687046, "dur": 42, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687090, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687092, "dur": 38, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687133, "dur": 47, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687182, "dur": 31, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687215, "dur": 26, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687244, "dur": 31, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687276, "dur": 43, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687321, "dur": 29, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687352, "dur": 30, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687384, "dur": 70, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687456, "dur": 29, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687487, "dur": 46, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687536, "dur": 72, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687612, "dur": 60, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687674, "dur": 4, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687680, "dur": 73, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687760, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687764, "dur": 41, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687807, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687809, "dur": 64, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687877, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687880, "dur": 53, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687935, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687938, "dur": 50, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687990, "dur": 1, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974687992, "dur": 44, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688038, "dur": 37, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688078, "dur": 25, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688104, "dur": 34, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688142, "dur": 37, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688181, "dur": 31, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688215, "dur": 34, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688252, "dur": 34, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688288, "dur": 26, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688316, "dur": 32, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688349, "dur": 36, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688387, "dur": 36, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688425, "dur": 71, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688498, "dur": 27, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688528, "dur": 31, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688560, "dur": 39, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688603, "dur": 31, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688635, "dur": 39, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688676, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688679, "dur": 37, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688719, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688722, "dur": 49, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688773, "dur": 53, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974688829, "dur": 327, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689159, "dur": 147, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689307, "dur": 2, "ph": "X", "name": "ProcessMessages 5165", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689310, "dur": 23, "ph": "X", "name": "ReadAsync 5165", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689335, "dur": 25, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689362, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689402, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689404, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689429, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689433, "dur": 22, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689455, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689457, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689518, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689521, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689550, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689552, "dur": 36, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689591, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689594, "dur": 40, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689635, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689637, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689674, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689679, "dur": 44, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689724, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689726, "dur": 40, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689771, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689775, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689818, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974689861, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692058, "dur": 170, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692230, "dur": 16, "ph": "X", "name": "ProcessMessages 20518", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692248, "dur": 58, "ph": "X", "name": "ReadAsync 20518", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692310, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692314, "dur": 35, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692354, "dur": 50, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692408, "dur": 33, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692443, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692447, "dur": 41, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692490, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692494, "dur": 28, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692524, "dur": 3, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692532, "dur": 88, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692623, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692677, "dur": 40, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692719, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692722, "dur": 42, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692770, "dur": 85, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692858, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692861, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692929, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692932, "dur": 36, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692970, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974692973, "dur": 47, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693025, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693074, "dur": 31, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693107, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693156, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693203, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693206, "dur": 33, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693241, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693243, "dur": 43, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693288, "dur": 53, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693342, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693344, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693403, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693407, "dur": 29, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693436, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693438, "dur": 45, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693484, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693535, "dur": 44, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693583, "dur": 11, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693596, "dur": 19, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693620, "dur": 21, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693641, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693654, "dur": 30, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693685, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693687, "dur": 52, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693745, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693775, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693777, "dur": 31, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693810, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693815, "dur": 33, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693851, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693889, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693894, "dur": 25, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693921, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693923, "dur": 55, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693981, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974693983, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694008, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694010, "dur": 32, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694045, "dur": 3, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694050, "dur": 35, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694088, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694128, "dur": 31, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694161, "dur": 29, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694193, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694269, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694305, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694307, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694332, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694335, "dur": 37, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694374, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694376, "dur": 30, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694407, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694408, "dur": 66, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694476, "dur": 28, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694506, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694561, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694601, "dur": 28, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694631, "dur": 35, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694668, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694716, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694752, "dur": 35, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694791, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694794, "dur": 26, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694824, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694827, "dur": 29, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694857, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694859, "dur": 35, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694896, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694899, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694923, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694926, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974694948, "dur": 52, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695005, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695033, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695035, "dur": 32, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695068, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695070, "dur": 24, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695096, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695097, "dur": 46, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695146, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695148, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695172, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695174, "dur": 30, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695206, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695208, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695247, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695274, "dur": 100, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695381, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974695444, "dur": 294, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974697706, "dur": 580, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974698290, "dur": 16, "ph": "X", "name": "ProcessMessages 5200", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974698307, "dur": 3816, "ph": "X", "name": "ReadAsync 5200", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974702128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974702130, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974702179, "dur": 1814, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974703996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974703999, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974704035, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974704037, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974704157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974704159, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974704212, "dur": 1070, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705286, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705316, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705319, "dur": 378, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705700, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705736, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705738, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705774, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705804, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705847, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974705875, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706059, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706060, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706087, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706089, "dur": 218, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706310, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706312, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706347, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706349, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706376, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706378, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706410, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706414, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706458, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974706486, "dur": 1726, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708215, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708266, "dur": 2, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708269, "dur": 16, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708287, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708300, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708346, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708349, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708386, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708411, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708487, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708507, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708523, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708584, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708612, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708637, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708693, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708720, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708743, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974708766, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709026, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709074, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709076, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709104, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709129, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709133, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709522, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709548, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709624, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709652, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709677, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709762, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709784, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709965, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709985, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974709987, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710063, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710093, "dur": 286, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710381, "dur": 30, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710413, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710414, "dur": 247, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710663, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710689, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710755, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710790, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710816, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710818, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974710841, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974711015, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974711038, "dur": 105, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974711146, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974711183, "dur": 172, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974711359, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974711395, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974711556, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974711588, "dur": 83366, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974794967, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974794972, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974795006, "dur": 1235, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974796243, "dur": 3582, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974799829, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974799831, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974799869, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974799871, "dur": 669, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974800543, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974800574, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974800687, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974800721, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974800874, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974800907, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974800910, "dur": 125, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801037, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801039, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801077, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801079, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801110, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801112, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801181, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801225, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801227, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801328, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801358, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801359, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801411, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801415, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801444, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801460, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801586, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801588, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801627, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801658, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801660, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801703, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801740, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801773, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801798, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801821, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801852, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801899, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801933, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801968, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974801992, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802018, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802055, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802092, "dur": 206, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802301, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802323, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802431, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802450, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802494, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974802508, "dur": 526, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803036, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803088, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803127, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803157, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803182, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803212, "dur": 345, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803562, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803600, "dur": 237, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803841, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803872, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974803898, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804112, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804199, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804229, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804258, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804341, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804374, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804376, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804407, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804431, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804458, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804459, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804513, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804537, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804538, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804560, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804615, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804643, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804663, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804689, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804721, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804746, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804810, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804811, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804835, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804837, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804870, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974804872, "dur": 676, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974805552, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974805583, "dur": 249, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 32248, "tid": 12884901888, "ts": 1753950974805833, "dur": 7082, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 32248, "tid": 10, "ts": 1753950974827427, "dur": 588, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 32248, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 32248, "tid": 8589934592, "ts": 1753950974666465, "dur": 65189, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 32248, "tid": 8589934592, "ts": 1753950974731656, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 32248, "tid": 8589934592, "ts": 1753950974731658, "dur": 1164, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 32248, "tid": 10, "ts": 1753950974828016, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 32248, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 32248, "tid": 4294967296, "ts": 1753950974643229, "dur": 170557, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 32248, "tid": 4294967296, "ts": 1753950974648165, "dur": 13291, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 32248, "tid": 4294967296, "ts": 1753950974813947, "dur": 6109, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 32248, "tid": 4294967296, "ts": 1753950974816067, "dur": 2968, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 32248, "tid": 4294967296, "ts": 1753950974820098, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 32248, "tid": 10, "ts": 1753950974828022, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753950974672320, "dur": 1263, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753950974673595, "dur": 657, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753950974674400, "dur": 678, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753950974675975, "dur": 882, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753950974677223, "dur": 165, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753950974677902, "dur": 1018, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753950974679214, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753950974681344, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753950974681754, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753950974684505, "dur": 3199, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753950974688627, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753950974690083, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753950974692822, "dur": 320, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753950974693792, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753950974675100, "dur": 21069, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753950974696178, "dur": 109639, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753950974805817, "dur": 278, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753950974806095, "dur": 150, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753950974806457, "dur": 1032, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753950974674905, "dur": 21282, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974696425, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753950974696558, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753950974696781, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753950974697059, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753950974697317, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753950974697501, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753950974697571, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753950974697691, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753950974697846, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753950974697969, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974698767, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974699355, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974700043, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Generation\\Descriptors\\BlockFieldDescriptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753950974699957, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974701558, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974702074, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974702960, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974703575, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974704134, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974704691, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974706039, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974706663, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974707040, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753950974707426, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753950974708193, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974708263, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974708321, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974708522, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974708727, "dur": 1247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974709974, "dur": 23745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974733720, "dur": 1287, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974735008, "dur": 62809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753950974797819, "dur": 3925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753950974801799, "dur": 3466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753950974805301, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974674905, "dur": 21299, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974696405, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753950974696510, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974696742, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_38E656AD7AB33EA4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753950974696811, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974697100, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753950974697211, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753950974697337, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753950974697473, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753950974697556, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753950974697661, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753950974697716, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974697958, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974698589, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974699070, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974700024, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974700864, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974701867, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974702803, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974703811, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974704310, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974705001, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974705711, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974705940, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974706104, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974706790, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974707079, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974707961, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974708311, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974708509, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974708717, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974709282, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974710010, "dur": 87815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974797837, "dur": 4013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753950974801851, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753950974801968, "dur": 3728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753950974805740, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974675016, "dur": 21247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974696382, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753950974696552, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753950974696691, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753950974696797, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753950974697142, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753950974697441, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753950974697611, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753950974697723, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753950974697889, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974698768, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974699397, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974699958, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974700880, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974701576, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974702320, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974703114, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974703679, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974704867, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974705679, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974706221, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974706669, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974707037, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753950974707314, "dur": 1930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753950974709298, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753950974709410, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753950974710444, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753950974710536, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753950974710888, "dur": 86915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974797825, "dur": 4881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753950974802872, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753950974802949, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974803976, "dur": 1313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753950974805308, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974674962, "dur": 21271, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974696246, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753950974696551, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753950974696841, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974697213, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753950974697320, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753950974697475, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753950974697625, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753950974697726, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753950974697863, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753950974697989, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974698633, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974699483, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974699919, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974700394, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974700895, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974701901, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974702369, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974703222, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974703746, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974704478, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974705040, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974705697, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974705859, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974706162, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974706707, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974707056, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753950974707429, "dur": 1306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753950974708786, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753950974708964, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753950974709436, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974710000, "dur": 87834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974797837, "dur": 5337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753950974803222, "dur": 1811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753950974805097, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974674959, "dur": 21256, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974696222, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753950974696391, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753950974696538, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753950974696788, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753950974696964, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753950974697089, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753950974697364, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753950974697542, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753950974697774, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753950974697948, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974698789, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974699571, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974700071, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974700711, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974701576, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974702144, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974702647, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974703325, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974704241, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974704738, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974705582, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974706128, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974706747, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974707071, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753950974707289, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753950974707960, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974708313, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974708509, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974708735, "dur": 1248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974709983, "dur": 87756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974797745, "dur": 4901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753950974802647, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974802844, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753950974802934, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974803220, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974803346, "dur": 1781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974805132, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753950974805236, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974674998, "dur": 21250, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974696407, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753950974696593, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E69732DB14D8F64.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753950974696857, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753950974697206, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753950974697275, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753950974697513, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753950974697641, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753950974697735, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753950974697925, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974698550, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974699365, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974700039, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974700852, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Artistic\\Adjustment\\ChannelMixerNode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753950974700620, "dur": 1744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974702365, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974703473, "dur": 665, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.1.0\\Editor\\ShaderStripping\\ShaderStrippingWatcher.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753950974702902, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974704357, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974704887, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974705494, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974705971, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974706131, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974706739, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974707067, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753950974707441, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753950974708182, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974708322, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974708525, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974708732, "dur": 1238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974709991, "dur": 87806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974797799, "dur": 2817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753950974800618, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974800726, "dur": 3686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753950974804485, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753950974805544, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974675041, "dur": 21237, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974696292, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_A8FE29C950FB2CD8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753950974696398, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974696505, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753950974696615, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753950974696782, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753950974697180, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753950974697235, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753950974697481, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753950974697539, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753950974697647, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753950974697868, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753950974697974, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974698924, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974699763, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974700962, "dur": 893, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Texture\\CalculateLevelOfDetailTexture2DNode.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753950974700503, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974701923, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974702344, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974703123, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974703666, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974704622, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974705818, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974706131, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974706753, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974707081, "dur": 901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974707982, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974708320, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974708506, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753950974708746, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753950974709270, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974709991, "dur": 87830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974797834, "dur": 4239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753950974802075, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753950974802263, "dur": 3129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753950974805446, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974675592, "dur": 20945, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974696553, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753950974696648, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753950974696783, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753950974696956, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974697165, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753950974697280, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753950974697501, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753950974697615, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753950974697853, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753950974697993, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974698511, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974699159, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974699759, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974700917, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Math\\Interpolation\\InverseLerpNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753950974700405, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974701553, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974702305, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974703298, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974704640, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974705374, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974706113, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974706775, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974707078, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974707978, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974708339, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974708526, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974708736, "dur": 1245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974709981, "dur": 87769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974797754, "dur": 3713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753950974801468, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753950974801805, "dur": 3408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753950974805257, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974675112, "dur": 21202, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974696403, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753950974696594, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753950974696774, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974696922, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_974E85225CDD9232.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753950974697056, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_974E85225CDD9232.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753950974697178, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753950974697386, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974697588, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753950974697686, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753950974697826, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753950974697976, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974698982, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974699485, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974699898, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974700444, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974701070, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974701761, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974702539, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974703150, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974703522, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974703953, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974704628, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974705391, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974706117, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974706767, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974707073, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974707959, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974708232, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974708327, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974708520, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974708725, "dur": 1252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974709977, "dur": 25034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974735012, "dur": 62734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974797824, "dur": 4635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753950974802460, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974802564, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974802886, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Internal.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1753950974802961, "dur": 1511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974804478, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974805008, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753950974805753, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974675696, "dur": 20881, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974696712, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4641433ED6B9F892.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753950974696780, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974697114, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753950974697276, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753950974697573, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753950974697635, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753950974697812, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753950974697896, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974698681, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974699280, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974699833, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974700360, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974701010, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974702015, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974702452, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\BoltCoreConfiguration.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753950974702441, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974703631, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974704275, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974704925, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974705749, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974706077, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974706663, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974707036, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753950974707281, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753950974708036, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974708108, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753950974708313, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753950974708885, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974709986, "dur": 87787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974797774, "dur": 4689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753950974802838, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753950974802953, "dur": 1105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974804078, "dur": 1259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753950974805353, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974675176, "dur": 21158, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974696350, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_67A51544EA16867F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753950974696497, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753950974696603, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753950974696833, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753950974697050, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753950974697223, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753950974697456, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753950974697631, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753950974697748, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753950974697954, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974698876, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974699607, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974700133, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974700980, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\ColorRGBMaterialSlot.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753950974700963, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974702105, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974702757, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974703525, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974704019, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974704560, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974705211, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974705841, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974705994, "dur": 127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974706121, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974706760, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974707071, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974707961, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974708311, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974708507, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753950974708717, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753950974709527, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753950974709654, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753950974710970, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753950974711038, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753950974711585, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753950974711672, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753950974711927, "dur": 85828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974797759, "dur": 5596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753950974803356, "dur": 690, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974804098, "dur": 1332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753950974805448, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974675216, "dur": 21138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974696369, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_78AB31D22221CD7D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753950974696570, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_78AB31D22221CD7D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753950974696822, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753950974696919, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974697084, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753950974697239, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753950974697332, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974697490, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753950974697630, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753950974697971, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974699209, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974699851, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974700826, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974701712, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974702403, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974702924, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974703534, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974704361, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974704994, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974705586, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974706185, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974706689, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974707069, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753950974707323, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974707706, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753950974708374, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974708512, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974708738, "dur": 1256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974709995, "dur": 88425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974798423, "dur": 5096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753950974803519, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974803941, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974803995, "dur": 1296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753950974805328, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974675252, "dur": 21123, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974696392, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753950974696613, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753950974696790, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753950974696908, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974696996, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753950974697198, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753950974697472, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1753950974697569, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753950974697648, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753950974697739, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753950974697810, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753950974697978, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974698788, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974699366, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974699909, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974700519, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974700988, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974701842, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974702429, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974702844, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974703535, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974704062, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974704567, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974705264, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974706100, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974706804, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974707043, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753950974707218, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753950974708231, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753950974708405, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753950974709053, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974709993, "dur": 87800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974797795, "dur": 4393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753950974802189, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974802342, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974802563, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974802681, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974802829, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974803410, "dur": 1838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753950974805267, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974675295, "dur": 21102, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974696407, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753950974696571, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753950974696823, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974697295, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753950974697350, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753950974697491, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753950974697617, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753950974697733, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753950974697910, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974698944, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974699435, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974699816, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974700708, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974701650, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974702538, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974702988, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974703666, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974704307, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974705044, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974705230, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974705931, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974706017, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974706135, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974706733, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974707066, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753950974707462, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753950974708346, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974708529, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974708737, "dur": 1266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974710003, "dur": 87764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974797787, "dur": 3383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753950974801171, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974801309, "dur": 3900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753950974805210, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753950974805347, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753950974675348, "dur": 21063, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753950974696461, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753950974696568, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974696799, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974696968, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974697077, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974697264, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974697401, "dur": 7354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753950974704755, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753950974704924, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974706192, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753950974706688, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974706756, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753950974707076, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974707234, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753950974708525, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974708773, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753950974709419, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753950974709541, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753950974709998, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1753950974710501, "dur": 123, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753950974711000, "dur": 84834, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1753950974797738, "dur": 3776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753950974801516, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753950974801620, "dur": 3932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753950974805589, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974675375, "dur": 21051, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974696436, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753950974696513, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974696610, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753950974696786, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974697081, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753950974697194, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753950974697339, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753950974697544, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753950974697789, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753950974697987, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974699181, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974700042, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974700574, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974701040, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974701952, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974702548, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974703039, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974703959, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974704810, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974705553, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974706154, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974706714, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974707064, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753950974707487, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753950974708237, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974708734, "dur": 1239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974709974, "dur": 1979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974711954, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753950974712080, "dur": 88567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974800649, "dur": 4064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753950974804766, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753950974805599, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974675445, "dur": 21019, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974696513, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974696770, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753950974696847, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753950974697184, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753950974697457, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753950974697642, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753950974697752, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753950974697884, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974698521, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974698913, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974699474, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974700046, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974700568, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974701749, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974702284, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974702726, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974703528, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974704435, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974705124, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974705181, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974705238, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974705832, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974706063, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974706662, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974707043, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753950974707214, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753950974707721, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974707963, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974708336, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974708525, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974708730, "dur": 1243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974709973, "dur": 1188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974711163, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753950974711281, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753950974711696, "dur": 86118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974797816, "dur": 4345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753950974802163, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753950974802268, "dur": 3435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753950974805750, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974675415, "dur": 21023, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974696557, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753950974696774, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974696950, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753950974697255, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753950974697465, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753950974697585, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753950974697649, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753950974697937, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974698004, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974698615, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974699010, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974700448, "dur": 1172, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Util\\KeywordUtil.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753950974700181, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974701863, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974702424, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974702839, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974703453, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974704104, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974704795, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974705684, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974705981, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974706165, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974706699, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974707058, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753950974707269, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753950974708384, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753950974708511, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753950974709640, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974710002, "dur": 87759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974797764, "dur": 4226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753950974801991, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753950974802093, "dur": 3605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753950974805748, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974675478, "dur": 20999, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974696570, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974696676, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_43A3C34EEA8A405D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753950974696779, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753950974697114, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753950974697460, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753950974697612, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753950974697668, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753950974697916, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974698986, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974699882, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974700603, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974701584, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974702231, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974702642, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974703388, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974704080, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974704905, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753950974704980, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974705993, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974706187, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974706679, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974707038, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753950974707222, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753950974708228, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753950974708402, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753950974708961, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974709992, "dur": 87792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753950974797786, "dur": 4117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753950974801945, "dur": 3622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753950974805620, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974675513, "dur": 20985, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974696509, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753950974696568, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974696779, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974696954, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974697033, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753950974697200, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753950974697388, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974697526, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753950974697611, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1753950974697746, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753950974697843, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753950974697956, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974698965, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974699533, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974700175, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974700983, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974701767, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974702662, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974703188, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974703616, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974704185, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974704839, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974705491, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974706136, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974706724, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974707059, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753950974707387, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753950974707981, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974708320, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974708518, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974708733, "dur": 1238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974709994, "dur": 87782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974797777, "dur": 3762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753950974801592, "dur": 3324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753950974804917, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974805090, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753950974805755, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974675552, "dur": 20960, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974696527, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753950974696613, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753950974696778, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753950974696999, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753950974697180, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753950974697270, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753950974697497, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753950974697555, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753950974697622, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753950974697720, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753950974697912, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974698737, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974699286, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974700332, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974701399, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974701825, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974702750, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974703480, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974704160, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974704756, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974705680, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974706091, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974706798, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974707079, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974707997, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974708316, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974708508, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753950974708740, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753950974709291, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974709999, "dur": 87828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974797829, "dur": 4173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753950974802003, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753950974802106, "dur": 2968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753950974805146, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974675073, "dur": 21221, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974696310, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_099DA8B5C5E5CF06.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753950974696388, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_27474F036FBEA425.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753950974696483, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974696561, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_27474F036FBEA425.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753950974696866, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753950974697098, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753950974697311, "dur": 5541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753950974702853, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974703050, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974703714, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974704214, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974704675, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974705091, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974705709, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974705908, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974706112, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974706782, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974707084, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974707972, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974708317, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974708513, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974708724, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974709425, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753950974709546, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974709606, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753950974710024, "dur": 87991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974798019, "dur": 3865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753950974801885, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974801998, "dur": 3110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753950974805109, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753950974805299, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974675627, "dur": 20926, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974696622, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CC2D34224542963B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753950974696843, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753950974697027, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753950974697160, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753950974697237, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753950974697506, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753950974697612, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753950974697716, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753950974697874, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753950974697999, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974698504, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974698961, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974699802, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974700779, "dur": 697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Math\\Wave\\SquareWaveNode.cs"}}, {"pid": 12345, "tid": 23, "ts": 1753950974700378, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974701585, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974702338, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974703347, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974704012, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974704503, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974705010, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974705454, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974706042, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974706681, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974707053, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753950974707367, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753950974707999, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974708229, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753950974708438, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753950974708942, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974709096, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753950974709206, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753950974709999, "dur": 87781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974797783, "dur": 4544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753950974802372, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974802491, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974802753, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974802837, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1753950974802914, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974802981, "dur": 1775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974804785, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753950974805632, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974675662, "dur": 20903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974696575, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753950974696716, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753950974696841, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753950974696983, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974697091, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753950974697268, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753950974697480, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753950974697657, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753950974697927, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974698680, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974699577, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974700350, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974701053, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974702027, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974702650, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974703245, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974703882, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974704981, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974705547, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974706045, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974706692, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974707073, "dur": 884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974707963, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974708206, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974708319, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974708508, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974708719, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974709306, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753950974709433, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753950974710570, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753950974710671, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753950974711158, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753950974711250, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753950974711671, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753950974711742, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753950974711950, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753950974712051, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753950974712266, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753950974712461, "dur": 85328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974797808, "dur": 4363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753950974802172, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753950974802275, "dur": 3191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753950974805537, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753950974811966, "dur": 964, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 32248, "tid": 10, "ts": 1753950974828446, "dur": 1864, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 32248, "tid": 10, "ts": 1753950974830341, "dur": 1333, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 32248, "tid": 10, "ts": 1753950974823857, "dur": 8453, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}