{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26516, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26516, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26516, "tid": 1563, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26516, "tid": 1563, "ts": 1753948062227759, "dur": 392, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26516, "tid": 1563, "ts": 1753948062230170, "dur": 432, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26516, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26516, "tid": 1, "ts": 1753948060707327, "dur": 3500, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26516, "tid": 1, "ts": 1753948060710829, "dur": 28183, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26516, "tid": 1, "ts": 1753948060739020, "dur": 22433, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26516, "tid": 1563, "ts": 1753948062230605, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 26516, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060706105, "dur": 8657, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060714763, "dur": 1496209, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060715474, "dur": 2030, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060717509, "dur": 7134, "ph": "X", "name": "ProcessMessages 15792", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060724645, "dur": 563, "ph": "X", "name": "ReadAsync 15792", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725218, "dur": 8, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725227, "dur": 88, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725320, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725322, "dur": 84, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725411, "dur": 7, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725420, "dur": 70, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725493, "dur": 5, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725499, "dur": 41, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725542, "dur": 38, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725585, "dur": 2, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725589, "dur": 60, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725653, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725655, "dur": 50, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725708, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725710, "dur": 65, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725777, "dur": 60, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725839, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725841, "dur": 43, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725885, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725888, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060725923, "dur": 155, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060726080, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060726127, "dur": 32, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060726161, "dur": 612, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060726775, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060726855, "dur": 4, "ph": "X", "name": "ProcessMessages 6786", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060726859, "dur": 24, "ph": "X", "name": "ReadAsync 6786", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060726885, "dur": 92, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060726979, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727019, "dur": 40, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727061, "dur": 70, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727133, "dur": 47, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727182, "dur": 51, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727236, "dur": 41, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727278, "dur": 52, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727336, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727339, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727428, "dur": 3, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727435, "dur": 77, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727517, "dur": 3, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727525, "dur": 51, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727578, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727581, "dur": 51, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727635, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727638, "dur": 91, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727731, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727734, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727796, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727798, "dur": 137, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727946, "dur": 4, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060727952, "dur": 69, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728025, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728029, "dur": 124, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728165, "dur": 2, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728173, "dur": 108, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728285, "dur": 3, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728291, "dur": 127, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728420, "dur": 3, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728424, "dur": 278, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728709, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728713, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728795, "dur": 3, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728799, "dur": 123, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728923, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728925, "dur": 52, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060728985, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729029, "dur": 23, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729054, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729118, "dur": 4, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729123, "dur": 57, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729182, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729185, "dur": 39, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729226, "dur": 58, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729287, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729310, "dur": 60, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729372, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729388, "dur": 51, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729440, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729441, "dur": 28, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729473, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729496, "dur": 51, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729549, "dur": 47, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729598, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729640, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729642, "dur": 14, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729657, "dur": 28, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729688, "dur": 2, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729692, "dur": 46, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729740, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729742, "dur": 26, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729769, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729771, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729793, "dur": 20, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729815, "dur": 85, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729901, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729935, "dur": 17, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729954, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729956, "dur": 19, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729976, "dur": 16, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060729994, "dur": 17, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730013, "dur": 20, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730037, "dur": 15, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730053, "dur": 15, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730070, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730087, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730107, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730109, "dur": 16, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730126, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730145, "dur": 2, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730148, "dur": 17, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730167, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730184, "dur": 14, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730199, "dur": 18, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730219, "dur": 12, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730232, "dur": 25, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730259, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730275, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730291, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730306, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730308, "dur": 18, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730328, "dur": 13, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730342, "dur": 13, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730357, "dur": 2, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730359, "dur": 13, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730376, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730394, "dur": 13, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730409, "dur": 12, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730423, "dur": 20, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730444, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730459, "dur": 11, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730472, "dur": 15, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730489, "dur": 13, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730505, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730521, "dur": 14, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730537, "dur": 13, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730552, "dur": 12, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730565, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730589, "dur": 14, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730604, "dur": 13, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730619, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730635, "dur": 13, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730649, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730675, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730701, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730718, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730733, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730750, "dur": 16, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730768, "dur": 14, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730784, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730805, "dur": 35, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730842, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060730859, "dur": 149, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731009, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731029, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731048, "dur": 15, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731064, "dur": 32, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731097, "dur": 28, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731127, "dur": 28, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731158, "dur": 25, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731186, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731187, "dur": 18, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731208, "dur": 19, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731230, "dur": 80, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731313, "dur": 32, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731348, "dur": 32, "ph": "X", "name": "ReadAsync 1207", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731382, "dur": 18, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731402, "dur": 71, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731474, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731500, "dur": 15, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731516, "dur": 16, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731533, "dur": 14, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731549, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731592, "dur": 69, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731662, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731664, "dur": 46, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731711, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731713, "dur": 50, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731765, "dur": 40, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731807, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731882, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731888, "dur": 40, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731931, "dur": 6, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731942, "dur": 53, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060731997, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732001, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732054, "dur": 78, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732133, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732137, "dur": 63, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732202, "dur": 47, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732251, "dur": 41, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732296, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732342, "dur": 77, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732422, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732474, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732476, "dur": 35, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732513, "dur": 44, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732558, "dur": 32, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732593, "dur": 31, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732625, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732651, "dur": 31, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732683, "dur": 4, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732688, "dur": 27, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732717, "dur": 29, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732749, "dur": 70, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732821, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732872, "dur": 36, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732909, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732911, "dur": 32, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732945, "dur": 39, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060732986, "dur": 32, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733020, "dur": 32, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733057, "dur": 43, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733101, "dur": 4, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733106, "dur": 24, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733132, "dur": 45, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733182, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733219, "dur": 55, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733276, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733299, "dur": 13, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733314, "dur": 18, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733334, "dur": 16, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733352, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733373, "dur": 49, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733423, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733444, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733464, "dur": 31, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733498, "dur": 20, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733520, "dur": 17, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733540, "dur": 13, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733555, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733576, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733594, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733613, "dur": 20, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733635, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733653, "dur": 14, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733669, "dur": 17, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733689, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733705, "dur": 18, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733729, "dur": 24, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733755, "dur": 17, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733774, "dur": 17, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733793, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733813, "dur": 28, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733847, "dur": 28, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733878, "dur": 13, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733893, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733947, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060733975, "dur": 59, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734035, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734036, "dur": 46, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734083, "dur": 4, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734088, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734109, "dur": 31, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734141, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734142, "dur": 21, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734165, "dur": 18, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734185, "dur": 27, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734213, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734241, "dur": 24, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734267, "dur": 12, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734281, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734314, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734420, "dur": 31, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734457, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734472, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734507, "dur": 43, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734553, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734572, "dur": 17, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734594, "dur": 21, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734616, "dur": 15, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734632, "dur": 23, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734657, "dur": 14, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734672, "dur": 43, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734716, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734740, "dur": 13, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734755, "dur": 13, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734769, "dur": 22, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734793, "dur": 13, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734811, "dur": 25, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734837, "dur": 14, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734854, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734883, "dur": 19, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734906, "dur": 42, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734950, "dur": 30, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060734982, "dur": 30, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735013, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735036, "dur": 16, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735054, "dur": 16, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735071, "dur": 15, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735088, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735107, "dur": 16, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735125, "dur": 33, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735166, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735185, "dur": 12, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735199, "dur": 18, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735218, "dur": 15, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735235, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735257, "dur": 12, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735271, "dur": 14, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735286, "dur": 42, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735329, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735343, "dur": 13, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735357, "dur": 16, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735375, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735392, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735408, "dur": 34, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735443, "dur": 21, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735466, "dur": 42, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735516, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735519, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735591, "dur": 2, "ph": "X", "name": "ProcessMessages 1897", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735594, "dur": 23, "ph": "X", "name": "ReadAsync 1897", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735618, "dur": 34, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735656, "dur": 29, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735687, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735722, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735743, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735768, "dur": 17, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735787, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735844, "dur": 37, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735885, "dur": 28, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735916, "dur": 45, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060735964, "dur": 57, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736023, "dur": 65, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736092, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736097, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736141, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736145, "dur": 31, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736178, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736180, "dur": 34, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736216, "dur": 68, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736288, "dur": 4, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736294, "dur": 32, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736329, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736331, "dur": 42, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736375, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736376, "dur": 60, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736439, "dur": 69, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736511, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736514, "dur": 56, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736573, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736575, "dur": 82, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736660, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736663, "dur": 51, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736717, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736718, "dur": 30, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736750, "dur": 27, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736779, "dur": 37, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736819, "dur": 32, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736854, "dur": 19, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736875, "dur": 40, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736918, "dur": 39, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736959, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060736988, "dur": 23, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737012, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737034, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737035, "dur": 23, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737060, "dur": 26, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737088, "dur": 33, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737122, "dur": 25, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737149, "dur": 30, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737181, "dur": 30, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737213, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737238, "dur": 23, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737263, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737321, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737347, "dur": 49, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737398, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737427, "dur": 23, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737453, "dur": 28, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737482, "dur": 25, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737508, "dur": 15, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737525, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737622, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737647, "dur": 24, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737676, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737701, "dur": 18, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737720, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737741, "dur": 140, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737883, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737905, "dur": 16, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737924, "dur": 15, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737940, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737942, "dur": 15, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060737958, "dur": 112, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738072, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738089, "dur": 15, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738106, "dur": 14, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738136, "dur": 16, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738155, "dur": 15, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738172, "dur": 124, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738297, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738315, "dur": 13, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738329, "dur": 14, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738345, "dur": 14, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738361, "dur": 62, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738424, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738437, "dur": 15, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738456, "dur": 18, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738475, "dur": 60, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738537, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738554, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738569, "dur": 13, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738584, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738600, "dur": 55, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738657, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738674, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738694, "dur": 44, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738740, "dur": 32, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738775, "dur": 36, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738814, "dur": 28, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738843, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738844, "dur": 14, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738860, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738921, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738957, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060738979, "dur": 49, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739029, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739059, "dur": 20, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739081, "dur": 47, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739130, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739160, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739184, "dur": 14, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739199, "dur": 46, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739246, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739275, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739277, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739300, "dur": 50, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739352, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739379, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739403, "dur": 51, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739455, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739486, "dur": 23, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739511, "dur": 19, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739531, "dur": 21, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739553, "dur": 91, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739647, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739686, "dur": 48, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739736, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739763, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739787, "dur": 42, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739832, "dur": 29, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739863, "dur": 63, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739928, "dur": 45, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739975, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060739977, "dur": 38, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740016, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740086, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740129, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740131, "dur": 129, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740262, "dur": 40, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740304, "dur": 32, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740338, "dur": 78, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740419, "dur": 30, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740451, "dur": 34, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740488, "dur": 21, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740511, "dur": 175, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740689, "dur": 135, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740826, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740828, "dur": 79, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740909, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740944, "dur": 44, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740990, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060740992, "dur": 14, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741007, "dur": 17, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741026, "dur": 16, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741044, "dur": 171, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741219, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741233, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741254, "dur": 12, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741267, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741286, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741304, "dur": 104, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741409, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741424, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741440, "dur": 17, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741458, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741473, "dur": 13, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741487, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741561, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741576, "dur": 13, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741591, "dur": 14, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741607, "dur": 13, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741621, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741688, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741701, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741704, "dur": 13, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741718, "dur": 14, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741733, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741750, "dur": 72, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741823, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741845, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741868, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741869, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741888, "dur": 75, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741964, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060741982, "dur": 34, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742018, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742037, "dur": 14, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742053, "dur": 14, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742069, "dur": 70, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742140, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742159, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742161, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742185, "dur": 14, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742202, "dur": 72, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742274, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742292, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742313, "dur": 15, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742330, "dur": 14, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742345, "dur": 76, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742422, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742460, "dur": 9, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742470, "dur": 14, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742485, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742505, "dur": 77, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742583, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742601, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742604, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742626, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742645, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742647, "dur": 15, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742663, "dur": 144, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742809, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742829, "dur": 17, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742848, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742866, "dur": 83, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742951, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742977, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060742978, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743000, "dur": 15, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743017, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743037, "dur": 156, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743195, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743216, "dur": 19, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743236, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743256, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743278, "dur": 110, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743390, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743406, "dur": 14, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743422, "dur": 16, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743441, "dur": 12, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743453, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743455, "dur": 30, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743487, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743504, "dur": 14, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743520, "dur": 14, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743535, "dur": 13, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743550, "dur": 17, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743568, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743583, "dur": 15, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743599, "dur": 71, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743672, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743687, "dur": 13, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743703, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743719, "dur": 11, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743733, "dur": 92, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743830, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743833, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743935, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743938, "dur": 58, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743997, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060743999, "dur": 58, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744060, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744062, "dur": 62, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744127, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744129, "dur": 32, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744164, "dur": 23, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744188, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744189, "dur": 124, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744316, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744388, "dur": 30, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744420, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744445, "dur": 13, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744459, "dur": 43, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744505, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744507, "dur": 29, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744537, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744557, "dur": 131, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744690, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744711, "dur": 99, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744813, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060744880, "dur": 272, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745155, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745229, "dur": 3, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745233, "dur": 30, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745265, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745313, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745315, "dur": 51, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745369, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745371, "dur": 25, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745398, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745444, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745501, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745503, "dur": 50, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745555, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745557, "dur": 46, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745606, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745633, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745658, "dur": 5, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745664, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745707, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745709, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745764, "dur": 26, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745793, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745828, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745853, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745880, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745905, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745933, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060745958, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746008, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746011, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746068, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746071, "dur": 55, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746129, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746135, "dur": 61, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746197, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746200, "dur": 58, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746262, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746310, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746313, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746364, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746366, "dur": 48, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746416, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746417, "dur": 33, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746454, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746493, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746530, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746532, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746574, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746614, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746616, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746658, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746691, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746726, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746730, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746776, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746779, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746829, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746874, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746876, "dur": 34, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746913, "dur": 52, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746966, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060746968, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060747009, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060747010, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060747053, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060747086, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060747111, "dur": 7170, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060754284, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060754288, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060754339, "dur": 488, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060754830, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060754832, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060754872, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060754924, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060754956, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060755010, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060755033, "dur": 1849, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060756886, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060756890, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060756936, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060756938, "dur": 1297, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758239, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758281, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758285, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758326, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758348, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758350, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758467, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758488, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758731, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758734, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758779, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758780, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758875, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758876, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758892, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060758982, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759004, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759025, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759069, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759097, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759123, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759170, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759196, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759217, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759316, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759392, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759434, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759436, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759475, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759477, "dur": 443, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759925, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759976, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060759978, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760028, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760033, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760085, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760109, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760112, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760153, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760169, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760300, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760328, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760355, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760394, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760396, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760430, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760431, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760466, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760468, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760543, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760591, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760698, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760738, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760767, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760847, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760879, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760912, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060760939, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761013, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761021, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761072, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761075, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761117, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761138, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761179, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761202, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761326, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761356, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761398, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761400, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761439, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761441, "dur": 197, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761645, "dur": 181, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761833, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761869, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761954, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060761999, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762035, "dur": 220, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762259, "dur": 289, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762551, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762553, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762592, "dur": 113, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762708, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762735, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762766, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762788, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762805, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762881, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762902, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762918, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762938, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060762952, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763148, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763164, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763194, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763221, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763272, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763309, "dur": 353, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763665, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763685, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763707, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763723, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763810, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763827, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763843, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763943, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060763959, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060764213, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060764236, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060764350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060764353, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060764394, "dur": 349, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060764745, "dur": 61, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060764810, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060764813, "dur": 510, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765333, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765386, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765437, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765439, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765487, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765489, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765575, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765580, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765635, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765743, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765782, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765788, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765942, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765946, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060765977, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766111, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766148, "dur": 179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766329, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766334, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766363, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766409, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766430, "dur": 484, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766917, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766940, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060766942, "dur": 69608, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060836560, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060836563, "dur": 790, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060837357, "dur": 1884, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060839244, "dur": 5981, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060845229, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060845231, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060845258, "dur": 545, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060845808, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060845848, "dur": 373, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846226, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846287, "dur": 142, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846433, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846476, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846481, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846513, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846517, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846542, "dur": 347, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846892, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060846919, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847022, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847031, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847116, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847118, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847179, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847182, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847219, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847221, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847315, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847344, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847428, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847457, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847485, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847502, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847715, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847742, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847765, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847791, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847793, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847821, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847859, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847861, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060847897, "dur": 299, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060848198, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060848221, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060848287, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060848310, "dur": 800, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849116, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849173, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849175, "dur": 172, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849352, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849433, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849509, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849559, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849562, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849743, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849776, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849822, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060849825, "dur": 203, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060850031, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060850033, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060850085, "dur": 747, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060850836, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060850873, "dur": 827, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060851707, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060851719, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060851781, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060851783, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060851838, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060851841, "dur": 418, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852265, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852268, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852322, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852327, "dur": 260, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852599, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852610, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852716, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852769, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852772, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852849, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852851, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852894, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060852896, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853011, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853076, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853079, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853151, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853153, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853233, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853238, "dur": 51, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853292, "dur": 62, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853358, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853361, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853431, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853436, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853497, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853500, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853564, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853620, "dur": 53, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853678, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853723, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853770, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948060853775, "dur": 1356140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948062209932, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948062209937, "dur": 117, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948062210057, "dur": 764, "ph": "X", "name": "ProcessMessages 1606", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753948062210824, "dur": 115, "ph": "X", "name": "ReadAsync 1606", "args": {}}, {"pid": 26516, "tid": 1563, "ts": 1753948062230612, "dur": 935, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26516, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26516, "tid": 8589934592, "ts": 1753948060704194, "dur": 56537, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26516, "tid": 8589934592, "ts": 1753948060760732, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26516, "tid": 8589934592, "ts": 1753948060760734, "dur": 1553, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26516, "tid": 1563, "ts": 1753948062231548, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26516, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26516, "tid": 4294967296, "ts": 1753948060691560, "dur": 1520069, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26516, "tid": 4294967296, "ts": 1753948060694680, "dur": 5625, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26516, "tid": 4294967296, "ts": 1753948062223768, "dur": 2583, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26516, "tid": 4294967296, "ts": 1753948062225482, "dur": 21, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26516, "tid": 4294967296, "ts": 1753948062226391, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26516, "tid": 1563, "ts": 1753948062231553, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753948060713308, "dur": 1561, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948060714880, "dur": 734, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948060715730, "dur": 55, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753948060715785, "dur": 679, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948060717332, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753948060718287, "dur": 7311, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_92F25047DCB9DD87.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753948060725634, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753948060725746, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753948060726115, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753948060726493, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060727194, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753948060727434, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753948060727766, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753948060727857, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753948060728139, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060728197, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753948060728382, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753948060728459, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753948060729135, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753948060729268, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753948060729491, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753948060731638, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060731868, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_5400A10AF7CC6BEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753948060731941, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060732045, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753948060732189, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753948060732419, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060732482, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753948060732536, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753948060732605, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753948060732843, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753948060732976, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753948060733370, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753948060733600, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060734231, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753948060734425, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753948060734707, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753948060734811, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060736117, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060736230, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753948060736857, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753948060740726, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060744253, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060744736, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753948060716486, "dur": 28619, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948060745117, "dur": 1348712, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948062093830, "dur": 294, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948062094124, "dur": 187, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948062094529, "dur": 58, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948062094607, "dur": 778, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753948060716394, "dur": 28734, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060745360, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060745497, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CC2D34224542963B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753948060745575, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CC2D34224542963B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753948060745697, "dur": 350, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753948060746088, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060746165, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060746346, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060746516, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060746645, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060746718, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1753948060746904, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060746992, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060747088, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060747221, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753948060747381, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060748272, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060749040, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060749667, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060751340, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Legacy\\PBRMasterNode1.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753948060750812, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060752177, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060752956, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060753860, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060754987, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060755907, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060756643, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060757096, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060758106, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060758738, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060759163, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753948060759409, "dur": 1289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753948060760764, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753948060761090, "dur": 1887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753948060762977, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060763309, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060763666, "dur": 76243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060839913, "dur": 9712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753948060849627, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060849764, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060849861, "dur": 2775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060852694, "dur": 1324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753948060854057, "dur": 1239761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060716730, "dur": 28513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060745252, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_9D28C4643EF997CA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753948060745358, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753948060745459, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753948060745596, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753948060745726, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753948060745846, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753948060745951, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753948060746218, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753948060746440, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753948060746503, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753948060746680, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753948060746857, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753948060747194, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753948060747329, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060748339, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060748903, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060749850, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060750755, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060751334, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060753024, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060753705, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060754979, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060755792, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060756614, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060757281, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060758028, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060758712, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060759161, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753948060759567, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753948060760725, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060760813, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060761193, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060761368, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060761487, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060762280, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060762340, "dur": 1369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060763709, "dur": 76263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060839979, "dur": 11163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753948060851229, "dur": 2359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753948060853625, "dur": 1240154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060717292, "dur": 28205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060745736, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753948060745864, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753948060745951, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753948060746265, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753948060746515, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753948060746720, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753948060746881, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753948060747154, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753948060747260, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753948060747422, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060748437, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060748985, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060749686, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060751056, "dur": 830, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Artistic\\Normal\\NormalBlendNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753948060750774, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060752385, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060753360, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060754309, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060755336, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060756167, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060756747, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060757921, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060758694, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060759194, "dur": 1662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060760857, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060761220, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060761400, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060761495, "dur": 1199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060762694, "dur": 1762, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060764457, "dur": 75539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060839998, "dur": 7654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753948060847654, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753948060847811, "dur": 6208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753948060854096, "dur": 1239727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060716548, "dur": 28626, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060745181, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753948060745372, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060745489, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753948060745678, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753948060745986, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753948060746278, "dur": 403, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753948060746793, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753948060747010, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753948060747127, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753948060747203, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753948060747337, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060748309, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060748966, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060751110, "dur": 899, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Controls\\TextControl.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753948060749967, "dur": 2387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060752355, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060753499, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060754624, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060755729, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060756459, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060757489, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060757683, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060758083, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060758722, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060759175, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753948060759653, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060759717, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753948060760687, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060760839, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060761201, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060761515, "dur": 2135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060763651, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060764461, "dur": 75457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060839922, "dur": 6661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753948060846584, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060846825, "dur": 6683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753948060853510, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753948060853697, "dur": 1240130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060716434, "dur": 28710, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060745342, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753948060745458, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060745518, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753948060745618, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753948060745811, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060745936, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753948060746315, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753948060746688, "dur": 630, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753948060747318, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060748006, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060748888, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060749889, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060751109, "dur": 852, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Math\\Advanced\\NormalizeNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753948060750644, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060752259, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060752885, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060754112, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060754782, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060755691, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060756299, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060756803, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060757025, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060757812, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060757986, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060758708, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060759184, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753948060759465, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060759824, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753948060760673, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060761429, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060761500, "dur": 1438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060762939, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753948060763104, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753948060763565, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060763655, "dur": 76259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060839950, "dur": 7229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753948060847181, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753948060847289, "dur": 6616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753948060853971, "dur": 1239871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060716497, "dur": 28663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060745173, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060745319, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060745455, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060745563, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060745734, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060745886, "dur": 351, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753948060746381, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753948060746491, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753948060746575, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753948060746728, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753948060746876, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753948060746951, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753948060747030, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753948060747212, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753948060747388, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060749064, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060751172, "dur": 688, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Texture\\GatherTexture2DNode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753948060750698, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060752331, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060753063, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060753908, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060754719, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060756047, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060756616, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060757136, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060757771, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060757864, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060758712, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060759155, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060759412, "dur": 1900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753948060761313, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060761490, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060761793, "dur": 1353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753948060763206, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060763340, "dur": 1528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753948060764913, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060764998, "dur": 1053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753948060766051, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948060766166, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753948060766330, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753948060766797, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753948060767337, "dur": 1188273, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753948061957388, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753948061956991, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753948061957564, "dur": 19087, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753948061957561, "dur": 20679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753948061979485, "dur": 383, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753948061979883, "dur": 92478, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753948062077571, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1753948062077565, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1753948062077683, "dur": 16172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060716597, "dur": 28603, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060745210, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_704C40EB5E4EE2B7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753948060745289, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060745461, "dur": 443, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A5A0099F491A8369.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753948060746012, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753948060746123, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753948060746209, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753948060746366, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753948060746471, "dur": 485, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753948060746982, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753948060747207, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753948060747365, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060748531, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060749404, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060751344, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\RedirectNode.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753948060750228, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060751930, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060753354, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060754398, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060754941, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060756176, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060757274, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060758167, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060758729, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060759193, "dur": 1607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060760827, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060761191, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060761352, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060761441, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060761503, "dur": 2148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060763652, "dur": 1344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060764997, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753948060765108, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753948060765600, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060765706, "dur": 74249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060839969, "dur": 9257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753948060849381, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060849834, "dur": 1379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060851220, "dur": 843, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060852078, "dur": 1579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753948060853658, "dur": 1240185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060716587, "dur": 28598, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060745265, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_BB555152ED99EC31.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753948060745326, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060745421, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B4D1B567B4047FDB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753948060745583, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753948060745917, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753948060746208, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753948060746354, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753948060746492, "dur": 474, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753948060747123, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753948060747191, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753948060747311, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060747855, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060748467, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060749610, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060751242, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Math\\Matrix\\MatrixConstructionNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753948060750577, "dur": 1966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060752544, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060753840, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060754770, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060755343, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060756503, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060757190, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060757830, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060757985, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060758696, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060759153, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753948060759501, "dur": 3083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753948060762663, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753948060762851, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753948060764113, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753948060764216, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753948060764630, "dur": 75310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060839944, "dur": 6712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753948060846662, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060846918, "dur": 6589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753948060853509, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753948060853589, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753948060853694, "dur": 1240105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060716642, "dur": 28572, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060745223, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753948060745345, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060745573, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753948060745727, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753948060745931, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_AF431D755E5610CB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753948060746151, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753948060746434, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753948060746611, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753948060746682, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753948060746780, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753948060747033, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753948060747228, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753948060747409, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060748372, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060749091, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060749911, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060750727, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060751991, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060752866, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060753773, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060754710, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060755493, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060756678, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060757940, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060758702, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060759166, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753948060759611, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753948060762059, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753948060762408, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753948060763077, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060763676, "dur": 76281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060839959, "dur": 7756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753948060847718, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060847898, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060848278, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060848777, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060848883, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060849101, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060849280, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060849346, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060849425, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060850456, "dur": 3066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753948060853523, "dur": 1240259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060716709, "dur": 28518, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060745345, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060745602, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753948060745792, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753948060745918, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753948060746209, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753948060746343, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753948060746501, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753948060746664, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753948060746788, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753948060746995, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753948060747366, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060748318, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060749251, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060749739, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060750816, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060752167, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060752908, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060754256, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060755416, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060756182, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060757061, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060758068, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060758715, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060759182, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753948060759427, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060759494, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753948060760380, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060760501, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753948060760698, "dur": 1225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753948060761924, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060762079, "dur": 1628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060763708, "dur": 76281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060839991, "dur": 6758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753948060846750, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060846832, "dur": 5887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753948060852720, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753948060853529, "dur": 1240248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060716762, "dur": 28495, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060745363, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060745465, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060745579, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_43A3C34EEA8A405D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753948060745754, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753948060745856, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060745908, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753948060746093, "dur": 505, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753948060746689, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753948060746883, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753948060747090, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753948060747206, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753948060747402, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060748408, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060749031, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060749769, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060751245, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\GraphValidation.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753948060751131, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060752421, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060753470, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060754606, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060755337, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060756280, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060756897, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060757384, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060758000, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060758714, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060759186, "dur": 1613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060760800, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753948060761099, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060761243, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753948060762271, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060762399, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060763725, "dur": 76222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060839949, "dur": 5518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753948060845469, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060845606, "dur": 6864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753948060852475, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060852658, "dur": 1301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753948060853983, "dur": 1239868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060716798, "dur": 28476, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060745354, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753948060745576, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753948060745727, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060745854, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753948060745970, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753948060746150, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753948060746208, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753948060746573, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753948060746898, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753948060747003, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753948060747233, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753948060747378, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060748560, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060749346, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060750038, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060751246, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\StencilUsage.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753948060751245, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060752305, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060752993, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060754046, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060754819, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060755695, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060756500, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060757059, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060758174, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060758732, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060759159, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753948060759762, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060760340, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753948060761216, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060761441, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753948060761720, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753948060762797, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060763691, "dur": 76273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060839983, "dur": 7450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753948060847434, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060847546, "dur": 5769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753948060853317, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753948060853538, "dur": 1240248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060716944, "dur": 28387, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060745343, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060745485, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060745725, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060745835, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060745929, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060746105, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060746302, "dur": 8835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753948060755138, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060755241, "dur": 1986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060757252, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753948060758722, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060758805, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753948060759149, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060759269, "dur": 1112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753948060760382, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060760799, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753948060761097, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753948060762070, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060762273, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060762370, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060763713, "dur": 76253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060839968, "dur": 9357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753948060849327, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060849741, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060849847, "dur": 2290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060852174, "dur": 1572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753948060853783, "dur": 1240023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060716868, "dur": 28421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060745305, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_18D02D41D4689F77.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753948060745460, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_18D02D41D4689F77.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753948060745671, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1983ABD9EB6C4B5C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753948060745732, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753948060745851, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753948060745936, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1753948060746052, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753948060746143, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753948060746601, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1753948060746901, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753948060747072, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753948060747154, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753948060747381, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060748346, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060749110, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060749879, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060751211, "dur": 647, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\SamplerStateShaderProperty.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753948060751130, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060752979, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060753837, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060754528, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060755311, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060756003, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060756475, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060756965, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060757707, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060757872, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060758707, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060759151, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753948060759578, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060759807, "dur": 1466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753948060761274, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060761437, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753948060761798, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753948060762935, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753948060763111, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753948060763651, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1753948060764272, "dur": 136, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060764749, "dur": 72195, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1753948060839924, "dur": 6083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753948060846012, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060846185, "dur": 5842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753948060852028, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060852153, "dur": 1589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753948060853825, "dur": 1239962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060716907, "dur": 28407, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060745365, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060745444, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753948060745514, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753948060745843, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753948060745925, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753948060746153, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753948060746432, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753948060746518, "dur": 315, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753948060746963, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753948060747112, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753948060747185, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753948060747362, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060748442, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060749001, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060749637, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060750476, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060751199, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060752228, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060753210, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060754410, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060755076, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060756142, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060756918, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060757924, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060758711, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060759174, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753948060759498, "dur": 1476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753948060760974, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060761231, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060761378, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060761524, "dur": 2124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060763680, "dur": 81850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060845536, "dur": 7423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753948060852960, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948060853120, "dur": 1201296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753948062054454, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753948062054420, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753948062054604, "dur": 1512, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753948062056119, "dur": 37743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060716994, "dur": 28353, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060745357, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753948060745456, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753948060745739, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060745893, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753948060746257, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753948060746396, "dur": 8200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753948060754597, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060754718, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060755725, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060756411, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060756846, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060756980, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060758201, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060758750, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060759200, "dur": 1644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060760844, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060761199, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060761395, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060761512, "dur": 2141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060763653, "dur": 2871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060766526, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753948060766703, "dur": 74310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060841019, "dur": 8958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753948060849980, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060850170, "dur": 2909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060853080, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753948060853135, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948060853230, "dur": 1224344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753948062077601, "dur": 16088, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753948062077576, "dur": 16114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753948060717038, "dur": 28324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060745375, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753948060745464, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060745575, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753948060745775, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753948060745924, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753948060746004, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753948060746112, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753948060746316, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753948060746597, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753948060746733, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753948060746952, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753948060747289, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753948060747346, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060747418, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060748327, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060749080, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060750528, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060751816, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060752241, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060752949, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060753720, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060754653, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060755961, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060756725, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060757451, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060757817, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060757907, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060758708, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060759181, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753948060759671, "dur": 679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060760355, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753948060761406, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060761525, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060761581, "dur": 2124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060763705, "dur": 76227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060839966, "dur": 7356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753948060847324, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753948060847398, "dur": 6558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753948060854038, "dur": 1239775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060717069, "dur": 28311, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060745397, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753948060745585, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753948060745954, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753948060746014, "dur": 584, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753948060746639, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753948060746877, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753948060746951, "dur": 326, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753948060747279, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753948060747368, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060747486, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060748591, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060749414, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060750225, "dur": 1737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060751963, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060752936, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060754274, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060754888, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060755591, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060756924, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060757553, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060757849, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060758690, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060759156, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753948060759471, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753948060760420, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060760554, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060760817, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060761221, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060761404, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060761489, "dur": 1176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060762667, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753948060762877, "dur": 1317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753948060764244, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753948060764348, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753948060764994, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753948060765096, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753948060765820, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753948060765952, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753948060766520, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753948060766707, "dur": 73216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060839926, "dur": 9279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753948060849206, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060849336, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060849414, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060850190, "dur": 3211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753948060853467, "dur": 1240317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060717101, "dur": 28297, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060745411, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753948060745511, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753948060745984, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753948060746340, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753948060746526, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753948060746677, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753948060746870, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753948060746967, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753948060747073, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753948060747169, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753948060747302, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753948060747465, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060748023, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060748838, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060749820, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060751062, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060752171, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060753056, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060754380, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060755267, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1753948060755405, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060756257, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060756782, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060756894, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060757425, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\Filter.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753948060757080, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060758152, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060758723, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060759179, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753948060759571, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753948060760805, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060761192, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060761422, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060761532, "dur": 2169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060763701, "dur": 76250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060839964, "dur": 6718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753948060846684, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060846840, "dur": 6565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753948060853408, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753948060853525, "dur": 1240277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060717134, "dur": 28281, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060745589, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753948060745784, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753948060745905, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060745979, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_974E85225CDD9232.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753948060746176, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753948060746277, "dur": 355, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1753948060746717, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1753948060746909, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753948060747002, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753948060747277, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753948060747407, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060748370, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060748998, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060751114, "dur": 935, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Procedural\\Shape\\BacteriaNode.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753948060750228, "dur": 1906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060752135, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060753191, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060754251, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060755086, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060756089, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060756703, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060757610, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060758034, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060758716, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060759182, "dur": 1620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060760806, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060760928, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060761194, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060761356, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060761487, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753948060761784, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753948060762588, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060762714, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060763687, "dur": 76291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060839983, "dur": 6528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753948060846574, "dur": 7084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753948060853659, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753948060853816, "dur": 1240042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060717169, "dur": 28256, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060745459, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17CAD97A34817613.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753948060745542, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17CAD97A34817613.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753948060745983, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1753948060746131, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753948060746283, "dur": 468, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753948060746853, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753948060747035, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753948060747150, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753948060747230, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753948060747405, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060748269, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060749008, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060749864, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060751243, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Basic\\BooleanNode.cs"}}, {"pid": 12345, "tid": 21, "ts": 1753948060750755, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060752182, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060753552, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060754304, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060755823, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060756493, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060757613, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060757875, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060758689, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060759196, "dur": 1654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060760851, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060761232, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060761386, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060761485, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753948060761805, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753948060762685, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060763163, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753948060763311, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753948060764087, "dur": 75840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060839929, "dur": 10440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753948060850444, "dur": 3055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060853508, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753948060853576, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1753948060853634, "dur": 1240159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060717194, "dur": 28247, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060745589, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_075C24B5F02EF89C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753948060745933, "dur": 444, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753948060746445, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753948060746534, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753948060746664, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753948060746796, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1753948060747146, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753948060747335, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060748095, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060748708, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060749323, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060750090, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060751200, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060752419, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060753403, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060754271, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060754892, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060755735, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060756716, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060757562, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060757876, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060758726, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060759183, "dur": 1620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060760803, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060761202, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060761389, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060761538, "dur": 2168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060763706, "dur": 76230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060839938, "dur": 7633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753948060847574, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060847693, "dur": 5966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753948060853660, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060853744, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753948060853860, "dur": 1239978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060717227, "dur": 28243, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060745482, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E69732DB14D8F64.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753948060745578, "dur": 307, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E69732DB14D8F64.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753948060745944, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753948060746093, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1753948060746193, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_BE343FF0452B4331.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753948060746477, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1753948060746576, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060746636, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753948060747033, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753948060747301, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753948060747450, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060748495, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060749362, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060749982, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060751241, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\Vector3MaterialSlot.cs"}}, {"pid": 12345, "tid": 23, "ts": 1753948060750945, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060752370, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060753543, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060754627, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060755412, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060756006, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060756566, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060757101, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060758014, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060758709, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060759177, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753948060759373, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753948060760306, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060760366, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060760826, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060761199, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060761363, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060761540, "dur": 2176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060763716, "dur": 76252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060839970, "dur": 8081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753948060848182, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060848603, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060848769, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060848857, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060849121, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060849292, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060849375, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060849756, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060849879, "dur": 3098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060852987, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948060853086, "dur": 1103918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948061957053, "dur": 19600, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 23, "ts": 1753948061957010, "dur": 21202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753948061979068, "dur": 143, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753948061979871, "dur": 69272, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753948062054411, "dur": 23283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 23, "ts": 1753948062054404, "dur": 23293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 23, "ts": 1753948062077717, "dur": 711, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 23, "ts": 1753948062078431, "dur": 15410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060717258, "dur": 28226, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060745492, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753948060745582, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753948060745730, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753948060745833, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060745936, "dur": 471, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753948060746485, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1753948060746550, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060746627, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1753948060746736, "dur": 566, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1753948060747351, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753948060747478, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060748279, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060748727, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060749616, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060750825, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060752124, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060752831, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060753538, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060755044, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060755850, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060756660, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060757444, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060757566, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060757734, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060757879, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060758692, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060759202, "dur": 1634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060760836, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060761197, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060761371, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060761518, "dur": 2129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060763674, "dur": 76231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060839908, "dur": 6584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753948060846494, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060846601, "dur": 6293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753948060852895, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060852999, "dur": 1076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753948060854119, "dur": 1239702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753948062099349, "dur": 1087, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26516, "tid": 1563, "ts": 1753948062231832, "dur": 1497, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26516, "tid": 1563, "ts": 1753948062233354, "dur": 1319, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26516, "tid": 1563, "ts": 1753948062229383, "dur": 5767, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}