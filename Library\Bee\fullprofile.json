{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26516, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26516, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26516, "tid": 1337, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26516, "tid": 1337, "ts": 1753947318381790, "dur": 10, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26516, "tid": 1337, "ts": 1753947318381810, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26516, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26516, "tid": 1, "ts": 1753947317191714, "dur": 1209, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26516, "tid": 1, "ts": 1753947317192928, "dur": 13252, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26516, "tid": 1, "ts": 1753947317206181, "dur": 16067, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26516, "tid": 1337, "ts": 1753947318381815, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 26516, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317191683, "dur": 12218, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317203902, "dur": 1177478, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317203912, "dur": 52, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317203967, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317204179, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317204227, "dur": 11, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317204240, "dur": 3025, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207271, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207334, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207336, "dur": 64, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207407, "dur": 2, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207411, "dur": 83, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207498, "dur": 2, "ph": "X", "name": "ProcessMessages 1733", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207501, "dur": 43, "ph": "X", "name": "ReadAsync 1733", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207546, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207547, "dur": 32, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207581, "dur": 43, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207627, "dur": 32, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207662, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207685, "dur": 88, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207776, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207806, "dur": 55, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207864, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207866, "dur": 55, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207925, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207927, "dur": 68, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207997, "dur": 1, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317207999, "dur": 49, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208050, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208051, "dur": 45, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208099, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208102, "dur": 53, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208159, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208161, "dur": 81, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208244, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208246, "dur": 33, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208282, "dur": 49, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208333, "dur": 45, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208379, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208380, "dur": 42, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208424, "dur": 35, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208461, "dur": 82, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208545, "dur": 43, "ph": "X", "name": "ReadAsync 1162", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208590, "dur": 44, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208636, "dur": 41, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208678, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208679, "dur": 42, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208723, "dur": 33, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208757, "dur": 20, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208779, "dur": 24, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208804, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208834, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208861, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208887, "dur": 28, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208916, "dur": 26, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208944, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208970, "dur": 20, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317208992, "dur": 17, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209011, "dur": 36, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209049, "dur": 24, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209076, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209097, "dur": 19, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209117, "dur": 1, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209119, "dur": 21, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209142, "dur": 18, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209163, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209185, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209205, "dur": 13, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209219, "dur": 218, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209444, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209522, "dur": 1, "ph": "X", "name": "ProcessMessages 1347", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209524, "dur": 34, "ph": "X", "name": "ReadAsync 1347", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209561, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209563, "dur": 32, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209596, "dur": 28, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209627, "dur": 42, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209671, "dur": 38, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209711, "dur": 52, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209765, "dur": 47, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209814, "dur": 35, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209850, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209878, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209901, "dur": 32, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209935, "dur": 51, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317209988, "dur": 86, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210076, "dur": 43, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210122, "dur": 47, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210172, "dur": 69, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210243, "dur": 59, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210304, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210307, "dur": 50, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210359, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210361, "dur": 45, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210409, "dur": 40, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210453, "dur": 35, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210490, "dur": 37, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210529, "dur": 60, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210591, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210593, "dur": 38, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210634, "dur": 39, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210676, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210678, "dur": 32, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210713, "dur": 34, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210749, "dur": 81, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210830, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210832, "dur": 17, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210851, "dur": 65, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210918, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210940, "dur": 15, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210956, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210980, "dur": 15, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317210996, "dur": 19, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211016, "dur": 26, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211045, "dur": 27, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211075, "dur": 58, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211140, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211142, "dur": 53, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211198, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211200, "dur": 27, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211229, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211231, "dur": 32, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211264, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211266, "dur": 22, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211292, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211317, "dur": 32, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211351, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211353, "dur": 56, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211411, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211413, "dur": 43, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211457, "dur": 28, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211487, "dur": 114, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211603, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211630, "dur": 24, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211655, "dur": 31, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211688, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211711, "dur": 31, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211744, "dur": 31, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211776, "dur": 51, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211829, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211870, "dur": 35, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211906, "dur": 23, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211931, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211961, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317211986, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212009, "dur": 24, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212034, "dur": 34, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212071, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212073, "dur": 44, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212120, "dur": 27, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212150, "dur": 29, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212181, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212207, "dur": 1, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212208, "dur": 20, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212230, "dur": 24, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212255, "dur": 14, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212271, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212301, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212326, "dur": 36, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212364, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212382, "dur": 23, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212407, "dur": 24, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212434, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212436, "dur": 22, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212461, "dur": 25, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212489, "dur": 25, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212516, "dur": 16, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212534, "dur": 26, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212561, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212564, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212585, "dur": 52, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212638, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212661, "dur": 20, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212683, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212706, "dur": 16, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212724, "dur": 17, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212742, "dur": 18, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212763, "dur": 16, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212781, "dur": 19, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212802, "dur": 25, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212830, "dur": 20, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212852, "dur": 26, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212880, "dur": 14, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212895, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212913, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212936, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212961, "dur": 26, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317212989, "dur": 18, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213008, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213032, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213055, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213077, "dur": 22, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213101, "dur": 23, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213127, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213149, "dur": 23, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213174, "dur": 21, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213198, "dur": 24, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213224, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213225, "dur": 27, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213254, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213256, "dur": 13, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213270, "dur": 13, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213285, "dur": 17, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213304, "dur": 16, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213322, "dur": 54, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213378, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213401, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213419, "dur": 15, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213438, "dur": 27, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213467, "dur": 25, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213494, "dur": 21, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213518, "dur": 15, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213534, "dur": 21, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213557, "dur": 25, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213583, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213585, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213611, "dur": 19, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213631, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213633, "dur": 14, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213649, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213670, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213672, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213689, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213711, "dur": 16, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213729, "dur": 14, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213745, "dur": 18, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213766, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213786, "dur": 13, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213802, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213822, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213845, "dur": 16, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213863, "dur": 16, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213881, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213897, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213899, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213917, "dur": 32, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213951, "dur": 18, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213972, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317213993, "dur": 13, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214008, "dur": 13, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214022, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214042, "dur": 17, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214060, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214062, "dur": 34, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214099, "dur": 22, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214122, "dur": 17, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214142, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214162, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214181, "dur": 17, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214201, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214225, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214245, "dur": 14, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214261, "dur": 17, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214281, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214300, "dur": 16, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214319, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214338, "dur": 32, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214372, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214387, "dur": 163, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214552, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214574, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214596, "dur": 12, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214609, "dur": 12, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214623, "dur": 15, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214639, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214657, "dur": 13, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214672, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214691, "dur": 14, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214705, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214707, "dur": 34, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214742, "dur": 15, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214759, "dur": 14, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214775, "dur": 14, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214790, "dur": 23, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214814, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214831, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214851, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214869, "dur": 14, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214885, "dur": 14, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214900, "dur": 13, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214915, "dur": 14, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214931, "dur": 14, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214946, "dur": 14, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214962, "dur": 10, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214973, "dur": 13, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317214988, "dur": 17, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215006, "dur": 14, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215022, "dur": 15, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215039, "dur": 13, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215054, "dur": 31, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215087, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215114, "dur": 23, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215139, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215156, "dur": 17, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215175, "dur": 12, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215189, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215223, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215238, "dur": 14, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215254, "dur": 14, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215269, "dur": 25, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215296, "dur": 10, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215307, "dur": 44, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215353, "dur": 21, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215375, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215394, "dur": 17, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215413, "dur": 14, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215429, "dur": 14, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215444, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215462, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215478, "dur": 18, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215497, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215515, "dur": 16, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215532, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215554, "dur": 14, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215569, "dur": 15, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215586, "dur": 14, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215602, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215621, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215623, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215641, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215645, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215667, "dur": 15, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215683, "dur": 22, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215708, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215734, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215755, "dur": 12, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215769, "dur": 26, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215797, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215825, "dur": 16, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215843, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215865, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215887, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215908, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215926, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215942, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215959, "dur": 16, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215977, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317215996, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216018, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216048, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216050, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216072, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216097, "dur": 18, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216117, "dur": 28, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216146, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216148, "dur": 17, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216166, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216188, "dur": 14, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216203, "dur": 18, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216222, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216228, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216248, "dur": 17, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216267, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216284, "dur": 17, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216304, "dur": 14, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216320, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216344, "dur": 14, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216360, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216384, "dur": 24, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216409, "dur": 14, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216425, "dur": 13, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216439, "dur": 14, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216455, "dur": 22, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216479, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216495, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216512, "dur": 14, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216527, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216542, "dur": 59, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216603, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216620, "dur": 20, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216643, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216666, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216689, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216706, "dur": 19, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216727, "dur": 15, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216744, "dur": 34, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216779, "dur": 28, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216809, "dur": 18, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216829, "dur": 26, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216857, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216872, "dur": 22, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216896, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216919, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216920, "dur": 56, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216979, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317216982, "dur": 72, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217057, "dur": 2, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217060, "dur": 40, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217101, "dur": 56, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217162, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217164, "dur": 47, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217214, "dur": 64, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217282, "dur": 14, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217298, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217326, "dur": 16, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217344, "dur": 19, "ph": "X", "name": "ReadAsync 15", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217364, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217392, "dur": 20, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217413, "dur": 12, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217427, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217474, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217505, "dur": 30, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217536, "dur": 66, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217604, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217624, "dur": 35, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217661, "dur": 43, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217705, "dur": 63, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217769, "dur": 15, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217786, "dur": 16, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217804, "dur": 38, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217843, "dur": 22, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217867, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217889, "dur": 41, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217933, "dur": 35, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217969, "dur": 12, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317217983, "dur": 34, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218018, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218037, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218053, "dur": 14, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218069, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218091, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218106, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218123, "dur": 15, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218140, "dur": 14, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218155, "dur": 38, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218195, "dur": 13, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218209, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218225, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218243, "dur": 14, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218259, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218276, "dur": 14, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218292, "dur": 14, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218309, "dur": 14, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218324, "dur": 13, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218339, "dur": 17, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218358, "dur": 16, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218376, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218396, "dur": 14, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218412, "dur": 42, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218455, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218457, "dur": 29, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218489, "dur": 37, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218527, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218555, "dur": 33, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218590, "dur": 29, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218622, "dur": 199, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218823, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218861, "dur": 23, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218885, "dur": 23, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218909, "dur": 20, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218931, "dur": 26, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317218959, "dur": 119, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219083, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219129, "dur": 30, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219161, "dur": 37, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219201, "dur": 22, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219225, "dur": 179, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219409, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219455, "dur": 2, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219466, "dur": 32, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219500, "dur": 62, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219565, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219608, "dur": 25, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219636, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219654, "dur": 11, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219667, "dur": 109, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219778, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219798, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219819, "dur": 19, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219840, "dur": 16, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219858, "dur": 97, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219957, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317219982, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220004, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220008, "dur": 16, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220026, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220046, "dur": 87, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220135, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220156, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220178, "dur": 15, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220195, "dur": 20, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220218, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220238, "dur": 87, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220328, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220345, "dur": 19, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220367, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220390, "dur": 14, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220405, "dur": 60, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220467, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220483, "dur": 12, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220497, "dur": 16, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220515, "dur": 12, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220528, "dur": 56, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220586, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220603, "dur": 14, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220619, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220635, "dur": 59, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220696, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220714, "dur": 13, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220729, "dur": 15, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220746, "dur": 15, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220762, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220812, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220831, "dur": 13, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220846, "dur": 13, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220861, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220917, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220936, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220956, "dur": 14, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317220971, "dur": 52, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221024, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221039, "dur": 14, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221055, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221079, "dur": 14, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221095, "dur": 34, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221131, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221134, "dur": 40, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221176, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221198, "dur": 25, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221225, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221243, "dur": 15, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221260, "dur": 17, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221281, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221307, "dur": 14, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221322, "dur": 17, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221341, "dur": 28, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221370, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221392, "dur": 13, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221407, "dur": 78, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221486, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221510, "dur": 28, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221540, "dur": 27, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221568, "dur": 31, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221602, "dur": 58, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221661, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221685, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221708, "dur": 24, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221733, "dur": 16, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221751, "dur": 47, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221800, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221819, "dur": 64, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221884, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221923, "dur": 66, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317221991, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222012, "dur": 17, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222030, "dur": 60, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222092, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222115, "dur": 113, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222231, "dur": 41, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222274, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222306, "dur": 12, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222320, "dur": 14, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222336, "dur": 14, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222351, "dur": 126, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222479, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222517, "dur": 18, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222537, "dur": 8, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222546, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222568, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222586, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222605, "dur": 100, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222706, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222724, "dur": 17, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222743, "dur": 17, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222762, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222779, "dur": 95, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222877, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222908, "dur": 36, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222947, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317222972, "dur": 96, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223070, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223097, "dur": 23, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223123, "dur": 19, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223144, "dur": 15, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223161, "dur": 84, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223246, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223267, "dur": 16, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223285, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223302, "dur": 17, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223322, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223351, "dur": 19, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223373, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223392, "dur": 57, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223450, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223467, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223485, "dur": 9, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223495, "dur": 57, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223554, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223578, "dur": 14, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223594, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223617, "dur": 14, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223633, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223674, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223689, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223709, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223728, "dur": 56, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223785, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223800, "dur": 16, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223819, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223838, "dur": 53, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223891, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223910, "dur": 18, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223930, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317223950, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224002, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224016, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224037, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224056, "dur": 55, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224113, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224133, "dur": 15, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224150, "dur": 16, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224170, "dur": 58, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224230, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224252, "dur": 19, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224273, "dur": 15, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224291, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224312, "dur": 20, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224334, "dur": 57, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224396, "dur": 4, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224403, "dur": 46, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224454, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224541, "dur": 2, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224544, "dur": 41, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224588, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224590, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224614, "dur": 66, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224684, "dur": 2, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224687, "dur": 75, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224764, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224766, "dur": 49, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224816, "dur": 1, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224817, "dur": 26, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224845, "dur": 16, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224863, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224921, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317224949, "dur": 122, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225074, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225124, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225126, "dur": 40, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225169, "dur": 35, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225206, "dur": 46, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225254, "dur": 40, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225296, "dur": 20, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225318, "dur": 33, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225353, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225387, "dur": 15, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225403, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225426, "dur": 18, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225445, "dur": 18, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225465, "dur": 19, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225485, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225507, "dur": 71, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225579, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225580, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225606, "dur": 116, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225725, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225776, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225778, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225870, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225874, "dur": 58, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225934, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225975, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317225977, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226025, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226054, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226097, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226099, "dur": 58, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226160, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226162, "dur": 51, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226216, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226218, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226244, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226274, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226309, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226350, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226391, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226393, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226464, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226466, "dur": 39, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226508, "dur": 43, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226553, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226605, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226607, "dur": 39, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226648, "dur": 26, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226679, "dur": 30, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226713, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226764, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226766, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226810, "dur": 32, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226846, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226849, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226884, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226886, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226935, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317226970, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227000, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227028, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227066, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227070, "dur": 51, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227123, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227125, "dur": 36, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227164, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227193, "dur": 66, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227261, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227283, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227305, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227307, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227338, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227380, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227382, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227409, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227446, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227483, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227511, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227541, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227563, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227590, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227613, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227654, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227693, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227726, "dur": 24, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227752, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227779, "dur": 55, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227836, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227889, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227894, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227939, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317227941, "dur": 5509, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317233453, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317233488, "dur": 1756, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317235247, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317235288, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317235314, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317235357, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317235386, "dur": 822, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236211, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236250, "dur": 547, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236800, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236835, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236838, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236884, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236912, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236931, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236971, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317236990, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237206, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237245, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237247, "dur": 256, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237507, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237546, "dur": 39, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237587, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237612, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237647, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237684, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237735, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317237764, "dur": 571, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238338, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238367, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238401, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238418, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238468, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238490, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238519, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238536, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238554, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238574, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238610, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238611, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238647, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238667, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238702, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238720, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238757, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238759, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238792, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238816, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238839, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238855, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238890, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238911, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238933, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317238954, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239077, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239093, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239119, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239135, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239159, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239160, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239175, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239314, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239346, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239348, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239413, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239442, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239494, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239520, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239542, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239562, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239606, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239608, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239657, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239659, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239689, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239727, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239763, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239765, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239800, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239841, "dur": 89, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239935, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317239964, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240004, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240030, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240032, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240062, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240131, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240164, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240195, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240239, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240264, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240284, "dur": 286, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240577, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240617, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240837, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240884, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240945, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317240983, "dur": 95, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241083, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241120, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241239, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241271, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241275, "dur": 80, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241359, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241394, "dur": 332, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241729, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241769, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241771, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241834, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241868, "dur": 108, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317241977, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242016, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242021, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242180, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242209, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242241, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242269, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242339, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242360, "dur": 349, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242711, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242741, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242773, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242819, "dur": 147, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242968, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317242988, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317243028, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317243059, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317243238, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317243253, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317243498, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317243500, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317243529, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317243533, "dur": 54177, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317297719, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317297725, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317297776, "dur": 24, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317297803, "dur": 6025, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317303833, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317303835, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317303876, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317303878, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304142, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304203, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304296, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304328, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304365, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304369, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304403, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304430, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304484, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304543, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304548, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304589, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304715, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304718, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304739, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304920, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304978, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317304979, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305023, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305093, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305142, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305175, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305237, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305276, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305314, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305352, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305394, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305426, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305461, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305464, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305511, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305541, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305577, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305617, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305618, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305642, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305675, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305692, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305705, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305751, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305778, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305804, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305853, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305858, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305885, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305954, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305976, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317305997, "dur": 352, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317306354, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317306389, "dur": 933, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307326, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307370, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307459, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307461, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307504, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307506, "dur": 331, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307840, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307877, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307930, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317307931, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308109, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308152, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308154, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308200, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308230, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308259, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308280, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308465, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308482, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308505, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308521, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308584, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308613, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947317308642, "dur": 929826, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318238476, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318238486, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318238518, "dur": 31, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318238550, "dur": 2093, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318240651, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318240653, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318240703, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318240707, "dur": 27069, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318267782, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318267784, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318267800, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318267802, "dur": 185, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318267990, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318268045, "dur": 70341, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318338394, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318338398, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318338430, "dur": 23, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318338454, "dur": 5558, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318344017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318344020, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318344044, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318344050, "dur": 1503, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318345557, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318345600, "dur": 23, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318345625, "dur": 120, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318345748, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318345772, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318345781, "dur": 5204, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318350989, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318351070, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318351074, "dur": 21758, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318372839, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318372841, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318372884, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318372886, "dur": 652, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318373548, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318373551, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318373629, "dur": 28, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318373658, "dur": 1016, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318374679, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318374680, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318374729, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753947318374733, "dur": 6644, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26516, "tid": 1337, "ts": 1753947318381824, "dur": 970, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26516, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26516, "tid": 21474836480, "ts": 1753947317191643, "dur": 30609, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26516, "tid": 21474836480, "ts": 1753947317222254, "dur": 24, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26516, "tid": 1337, "ts": 1753947318382796, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26516, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26516, "tid": 17179869184, "ts": 1753947317188082, "dur": 1193333, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26516, "tid": 17179869184, "ts": 1753947317188224, "dur": 3359, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26516, "tid": 17179869184, "ts": 1753947318381418, "dur": 66, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26516, "tid": 17179869184, "ts": 1753947318381431, "dur": 15, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26516, "tid": 1337, "ts": 1753947318382802, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753947317205169, "dur": 1873, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947317207054, "dur": 630, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947317207783, "dur": 53, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753947317207837, "dur": 645, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947317208582, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317208720, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_95DEE68278F6B037.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317209486, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_18D02D41D4689F77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317210222, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317210760, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317210870, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317211026, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4641433ED6B9F892.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317211126, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_38E656AD7AB33EA4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317211266, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317211831, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317212084, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947317213267, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317217816, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947317218308, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317218371, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753947317218482, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753947317218586, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317218651, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947317218816, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753947317218978, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947317220122, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753947317223224, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753947317223397, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753947317225951, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753947317226080, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947317226447, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753947317226533, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947317226666, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947317208508, "dur": 18375, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947317226893, "dur": 1148003, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947318374898, "dur": 285, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947318375183, "dur": 323, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947318375563, "dur": 59, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947318375811, "dur": 80, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947318375942, "dur": 1134, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753947317208813, "dur": 18182, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317227008, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_704C40EB5E4EE2B7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947317227166, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947317227382, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947317227538, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947317227732, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947317227894, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1753947317228009, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753947317228114, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753947317228437, "dur": 469, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753947317228961, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753947317229094, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753947317229197, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317229797, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317230517, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317231423, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317232062, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317233010, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317233839, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317234311, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317235249, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317236038, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317236620, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317237227, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317237373, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317237479, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317237593, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317238168, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317238590, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947317238827, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753947317240059, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317240277, "dur": 1230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317241507, "dur": 2663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317244170, "dur": 56768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317300983, "dur": 5934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753947317307153, "dur": 2301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947317309490, "dur": 1065425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317208756, "dur": 18183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317227005, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C0425020FB5520E6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947317227159, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317227285, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947317227521, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947317227733, "dur": 624, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_92F25047DCB9DD87.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947317228433, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753947317228514, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753947317228715, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753947317228997, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317229569, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317230592, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317231990, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317232354, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317233279, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317234047, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317234437, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317234910, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317235980, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317236898, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317237324, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317237677, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317238188, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317238614, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317239667, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317239887, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947317240092, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753947317240619, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317240807, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317241500, "dur": 2625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317244129, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947317244260, "dur": 56710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317300992, "dur": 5463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753947317306456, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317306653, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317306780, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753947317306882, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317307068, "dur": 1673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317308770, "dur": 1122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947317309910, "dur": 1064983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317209134, "dur": 18032, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317227281, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317227417, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947317227613, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947317227721, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947317228066, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947317228190, "dur": 6488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753947317234763, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317235786, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317236482, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317236998, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317237544, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317238166, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317238599, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947317238895, "dur": 1715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753947317240611, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317240700, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947317240949, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753947317241418, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317241512, "dur": 2627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317244140, "dur": 56806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317300952, "dur": 5189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753947317306142, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947317306250, "dur": 3472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753947317309773, "dur": 1065153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317208680, "dur": 18228, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317226919, "dur": 2161, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317229080, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317229679, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317230302, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317230998, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317231733, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317232209, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317233006, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317233618, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317234435, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317235124, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317236007, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317236714, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317236809, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317237100, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317237579, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317238159, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317238589, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947317238800, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947317240001, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947317240284, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947317241119, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317241508, "dur": 2666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317244175, "dur": 56823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317301009, "dur": 4622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753947317305632, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947317305767, "dur": 3572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753947317309429, "dur": 1042694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947318352156, "dur": 21920, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753947318352126, "dur": 21952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753947318374113, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317208969, "dur": 18137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317227148, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317227264, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947317227319, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947317227519, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317227634, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947317227761, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947317227892, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947317228011, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947317228188, "dur": 456, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753947317228733, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947317228838, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947317228999, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947317229184, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317229778, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317230418, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317231266, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317232759, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317233637, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317234290, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317235109, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317235930, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317236995, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317237573, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317238161, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317238586, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947317239032, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753947317240006, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947317240204, "dur": 1034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753947317241307, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317241527, "dur": 2623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317244150, "dur": 56776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947317300928, "dur": 4587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753947317305576, "dur": 3814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753947317309479, "dur": 1065410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317209072, "dur": 18072, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317227233, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317227297, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947317227545, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947317227674, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947317227869, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753947317227987, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753947317228105, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753947317228341, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753947317228723, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753947317228850, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753947317228921, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753947317228995, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317229611, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317230553, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317231301, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317232269, "dur": 583, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\Texture2DMaterialSlot.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753947317232082, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317233638, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317234607, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317235136, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317235813, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317236431, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317237076, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317237598, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317238173, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317238602, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947317239018, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753947317240041, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317240275, "dur": 1248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317241523, "dur": 2639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317244162, "dur": 56770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317300934, "dur": 4743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753947317305678, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317305818, "dur": 3265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753947317309167, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947317309912, "dur": 1064978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317208771, "dur": 18189, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317226969, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947317227158, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947317227335, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_075C24B5F02EF89C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947317227513, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317227815, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947317228205, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947317228467, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753947317228623, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947317228728, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753947317228786, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947317228912, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947317229081, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947317229133, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317229753, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317230613, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317231277, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317231866, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317232955, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317233383, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317234288, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317234980, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317235785, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317236494, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317237546, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317238155, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317238581, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947317238810, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753947317239873, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317240020, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317240270, "dur": 1223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317241525, "dur": 2612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317244137, "dur": 56776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317300917, "dur": 5427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753947317306344, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317306508, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317306895, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317307054, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317307277, "dur": 2236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947317309546, "dur": 1065395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317208716, "dur": 18205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317227127, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753947317227222, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317227380, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_43A3C34EEA8A405D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753947317227746, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753947317227908, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753947317228118, "dur": 772, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753947317228893, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753947317229086, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317230066, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317231203, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317233048, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\Tiling\\TilingJob.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753947317232193, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317233801, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317234462, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317235180, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317235812, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317236575, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317236639, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317237100, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317237739, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317238192, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317238620, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317239678, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317239888, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317240062, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317240287, "dur": 1232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317241519, "dur": 2616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317244136, "dur": 56786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317300947, "dur": 5241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753947317306189, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317306369, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753947317306455, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317306880, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317306989, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317307136, "dur": 2269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317309413, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947317309531, "dur": 1065421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317208815, "dur": 18203, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317227139, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947317227279, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947317227368, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947317227606, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753947317227699, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753947317227921, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753947317228472, "dur": 510, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753947317228983, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753947317229209, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "D:\\UNRTIY\\<PERSON><PERSON>\\2022.3.61t2\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Abstractions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753947317229083, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317230283, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317231212, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317231813, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317232999, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317233725, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317234406, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317234897, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317235459, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317235978, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317236829, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317237308, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317237431, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317237506, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317237605, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317238175, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317238607, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317239690, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317239905, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317240007, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317240254, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317240813, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317241499, "dur": 2630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317244129, "dur": 56789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317300935, "dur": 6589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753947317307525, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317307641, "dur": 1874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947317309556, "dur": 1065346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317208791, "dur": 18184, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317226989, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753947317227131, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753947317227225, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317227365, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753947317227530, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753947317227724, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947317227982, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947317228118, "dur": 435, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947317228643, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947317229005, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947317229082, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317230046, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\TrackGui\\TimelineTrackGUI.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753947317229752, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317230983, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317231647, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317232115, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317233237, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317234052, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317234937, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317235373, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317236290, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317237074, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317237610, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317238174, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317238618, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317239671, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317239894, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317240013, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317240262, "dur": 1238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317241501, "dur": 2639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317244141, "dur": 56761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317300905, "dur": 4517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753947317305423, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947317305701, "dur": 3694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753947317309475, "dur": 1065412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317208867, "dur": 18186, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317227144, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947317227282, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E69732DB14D8F64.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947317227520, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947317227747, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_AF431D755E5610CB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947317227986, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A6E1C780C67641AE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947317228237, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753947317228386, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947317228502, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753947317228635, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947317228776, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947317228961, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947317229040, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317229867, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317230421, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317231379, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317231831, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317232870, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317233418, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317234012, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317235192, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317235843, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317236262, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317236611, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317237336, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317237438, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317237659, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317238185, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317238612, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317239663, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317239889, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317240016, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317240269, "dur": 1228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317241497, "dur": 1530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317243029, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947317243119, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753947317243471, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317244178, "dur": 56811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317300994, "dur": 4908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753947317305903, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947317306004, "dur": 3830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753947317309919, "dur": 1064980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317208859, "dur": 18180, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317227148, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947317227244, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317227333, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947317227518, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947317227655, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947317228260, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753947317228440, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753947317228642, "dur": 354, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753947317228997, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317229695, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317230723, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317231491, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317232076, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317232925, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317233806, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317234508, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317235237, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317236752, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317236864, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317237386, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317237479, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317237598, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317238172, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317238600, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947317238948, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753947317239819, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317240010, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317240252, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947317240350, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317240452, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753947317241101, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947317241216, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753947317241863, "dur": 2296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317244160, "dur": 56813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317300976, "dur": 4790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753947317305768, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317305837, "dur": 3605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753947317309443, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947317309537, "dur": 1065402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317208905, "dur": 18166, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317227085, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1A0DCA6DE3746D0.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317227140, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317227249, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317227418, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317227599, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317227714, "dur": 640, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947317228356, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317228471, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317228754, "dur": 7589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753947317236343, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317236575, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317237491, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753947317238202, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317238264, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753947317238610, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317238900, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317238958, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753947317239772, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317239900, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317240113, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753947317240862, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947317240996, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753947317241491, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1753947317242026, "dur": 109, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317242523, "dur": 56427, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1753947317300945, "dur": 4065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753947317305079, "dur": 3609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753947317308750, "dur": 1036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947317309786, "dur": 1065146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317208956, "dur": 18136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317227149, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317227289, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947317227635, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947317227734, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947317228007, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753947317228301, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1753947317228444, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1753947317228648, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753947317228817, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753947317228903, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753947317229082, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753947317229148, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317230267, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317230965, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317232321, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\UV\\TilingAndOffsetNode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753947317231619, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317232875, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317233894, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317234603, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317235125, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317235834, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317236585, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317237130, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317237544, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317238146, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317238572, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947317238955, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947317240108, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947317240273, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947317240413, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947317241327, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947317241537, "dur": 1531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947317243131, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947317243258, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947317244140, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947317244253, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947317244529, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947317244791, "dur": 994913, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947318241738, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753947318241368, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947318241912, "dur": 24009, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753947318241909, "dur": 25947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753947318269036, "dur": 226, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947318269278, "dur": 77579, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753947318352125, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1753947318352119, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1753947318352232, "dur": 22722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317209012, "dur": 18109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317227131, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_3728351D5FA9AF42.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947317227223, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317227351, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_3728351D5FA9AF42.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947317227503, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947317227556, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947317227694, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947317227809, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947317227908, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947317228056, "dur": 403, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753947317228473, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753947317228817, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947317229004, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947317229218, "dur": 590, "ph": "X", "name": "File", "args": {"detail": "D:\\UNRTIY\\<PERSON><PERSON>\\2022.3.61t2\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HostFiltering.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753947317229119, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317230361, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317231124, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317231911, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317232795, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317233646, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317234159, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317234947, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317235486, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317236046, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317237102, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317237549, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317238157, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317238594, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947317238923, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753947317239700, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317239904, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317240015, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317240265, "dur": 1226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317241536, "dur": 2615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317244152, "dur": 56860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317301013, "dur": 5619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753947317306633, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317306779, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753947317306861, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317307097, "dur": 2304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947317309433, "dur": 1065464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317209052, "dur": 18085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317227143, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947317227224, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947317227484, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F073B613EC8B985D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947317227588, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317227728, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_974E85225CDD9232.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947317227852, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1753947317227946, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753947317228074, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753947317228200, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1753947317228395, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1753947317228503, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1753947317228995, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753947317229126, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "D:\\UNRTIY\\<PERSON><PERSON>\\2022.3.61t2\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.WebEncoders.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753947317229081, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317230193, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317230961, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317232286, "dur": 840, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Procedural\\Shape\\AntiAlias.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753947317231657, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317233174, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317233945, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317234932, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317235565, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317236372, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317237650, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317238183, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317238608, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317239687, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317239886, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947317240080, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317240136, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753947317240688, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317240799, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317241516, "dur": 2665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317244182, "dur": 56803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317301000, "dur": 5868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753947317306869, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317307067, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317307625, "dur": 960, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317308594, "dur": 1168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947317309798, "dur": 1065160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317209118, "dur": 18032, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317227162, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947317227294, "dur": 326, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947317227658, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947317227816, "dur": 430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2089C4C973FD212.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947317228345, "dur": 396, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753947317228791, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753947317229162, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317230165, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317230942, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317231572, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317232150, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317233423, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317234024, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317235048, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317235478, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317236244, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317237098, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Messages\\ExitPlayMode.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753947317236831, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317237760, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317238151, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317238571, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947317238786, "dur": 1925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753947317240712, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317240880, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947317241022, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753947317242133, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947317242230, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753947317242661, "dur": 1526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317244187, "dur": 56766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317300958, "dur": 5123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753947317306082, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947317306177, "dur": 3575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753947317309796, "dur": 1065125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317209188, "dur": 17991, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317227283, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947317227435, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947317227562, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947317227728, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753947317227823, "dur": 410, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753947317228290, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753947317228436, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753947317228709, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753947317228813, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753947317228916, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753947317229031, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317229759, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317230669, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317231300, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317231938, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317233129, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317233877, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317234437, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317234916, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317235484, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317236085, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317237127, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317237645, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317238180, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317238607, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317239666, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317239888, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317240009, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317240170, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317240259, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317240885, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947317241049, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753947317242252, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947317242352, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753947317243020, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947317243115, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753947317243543, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947317243627, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753947317244017, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317244164, "dur": 56804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317300969, "dur": 5218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753947317306188, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947317306277, "dur": 3557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753947317309901, "dur": 1064991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317209230, "dur": 17957, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317227196, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A5A0099F491A8369.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947317227265, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A5A0099F491A8369.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947317227407, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947317227540, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947317227669, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947317227870, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753947317228024, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753947317228385, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753947317228636, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753947317228712, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753947317228868, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753947317229053, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317229774, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317230195, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317230944, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317231812, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317232282, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\RendererFeatures\\DisallowMultipleRendererFeature.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753947317232254, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317233356, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317234516, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317235228, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317236049, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317236690, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317237138, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317237529, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317238148, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317238570, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947317238714, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317238779, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753947317239683, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317239780, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947317240021, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753947317240510, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317240614, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317241521, "dur": 2644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317244167, "dur": 56826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317300994, "dur": 6024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753947317307021, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317307145, "dur": 2303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947317309484, "dur": 1065475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317209279, "dur": 17919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317227255, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317227423, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753947317227570, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753947317227739, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753947317227974, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_BE343FF0452B4331.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753947317228405, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1753947317228708, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1753947317228768, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753947317228853, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753947317228956, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753947317229037, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317229787, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317230342, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317231213, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317231921, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317233053, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317233972, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317234557, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317235398, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317236144, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317236942, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317237388, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317237543, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317238169, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317238591, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753947317238867, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753947317240034, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317240290, "dur": 1219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317241510, "dur": 2638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317244149, "dur": 56854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317301006, "dur": 6179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753947317307247, "dur": 2211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947317309491, "dur": 1065460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317209325, "dur": 17894, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317227233, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947317227288, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317227355, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947317227528, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317227705, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753947317228246, "dur": 927, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753947317229173, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317229723, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317230211, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317230953, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317231467, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317232103, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317233038, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317233574, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317234325, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317234780, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317235577, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317236212, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317236883, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317237708, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317238190, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317238578, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947317238729, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317238872, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753947317239754, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317239897, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317240008, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317240253, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947317240368, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753947317241041, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317241502, "dur": 2621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317244124, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947317244320, "dur": 56629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317300951, "dur": 4304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753947317305257, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947317305404, "dur": 4002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753947317309497, "dur": 1065431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317209383, "dur": 17855, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317227320, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753947317227577, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317227711, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1753947317228029, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947317228178, "dur": 471, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947317228835, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947317228916, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947317229075, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317229827, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317230408, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317231036, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317231653, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317232336, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317233343, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317233925, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317234884, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317235563, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317236716, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317236950, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317237626, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317238177, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317238598, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753947317238787, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753947317239679, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317239898, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317240021, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317240280, "dur": 1231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317241511, "dur": 2622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317244133, "dur": 56772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317300908, "dur": 6073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753947317307074, "dur": 2052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947317309170, "dur": 932211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947318241432, "dur": 24456, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 22, "ts": 1753947318241387, "dur": 26418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753947318268750, "dur": 129, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947318269266, "dur": 70365, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753947318345127, "dur": 28948, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 22, "ts": 1753947318345121, "dur": 28955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 22, "ts": 1753947318374097, "dur": 707, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 23, "ts": 1753947317209428, "dur": 17828, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317227269, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947317227353, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947317227532, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_C5B475C4637F1A14.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947317227708, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947317227989, "dur": 485, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753947317228504, "dur": 490, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753947317228995, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753947317229096, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317230061, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317230626, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317231021, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317231694, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317232313, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317233344, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317234171, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317234922, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317235499, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317236180, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317236704, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317237266, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317237594, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317238170, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317238610, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317239656, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317239890, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317240007, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317240254, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317240865, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947317241000, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753947317241549, "dur": 2604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317244153, "dur": 56834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317300989, "dur": 4748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753947317305738, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947317305827, "dur": 3533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753947317309424, "dur": 1035701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947318345154, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1753947318345127, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1753947318345279, "dur": 1765, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1753947318347046, "dur": 27924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317209481, "dur": 17798, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317227280, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753947317227387, "dur": 380, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753947317227803, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753947317227867, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753947317228087, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1753947317228440, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1753947317228722, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753947317228973, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753947317229058, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317229783, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317230724, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317231283, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317232247, "dur": 685, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Texture\\CalculateLevelOfDetailTexture2DNode.cs"}}, {"pid": 12345, "tid": 24, "ts": 1753947317231827, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317233178, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317233652, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317234078, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317234957, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317235554, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317236208, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317236481, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317237530, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317238148, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317238573, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753947317238818, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753947317239507, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317239641, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317239799, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317239911, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317240013, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317240261, "dur": 1236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317241498, "dur": 2632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317244130, "dur": 56779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947317300911, "dur": 4690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753947317305650, "dur": 3817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753947317309533, "dur": 1065416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947318381255, "dur": 957, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26516, "tid": 1337, "ts": 1753947318382844, "dur": 1538, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26516, "tid": 1337, "ts": 1753947318384407, "dur": 7158, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26516, "tid": 1337, "ts": 1753947318381802, "dur": 9797, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}