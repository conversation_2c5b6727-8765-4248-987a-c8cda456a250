{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26516, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26516, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26516, "tid": 1482, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26516, "tid": 1482, "ts": 1753947809578245, "dur": 457, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26516, "tid": 1482, "ts": 1753947809581362, "dur": 591, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26516, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26516, "tid": 1, "ts": 1753947808277747, "dur": 4241, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26516, "tid": 1, "ts": 1753947808281991, "dur": 30289, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26516, "tid": 1, "ts": 1753947808312289, "dur": 23595, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26516, "tid": 1482, "ts": 1753947809581957, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 26516, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808276577, "dur": 20034, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808296614, "dur": 1274283, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808297688, "dur": 2056, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808299753, "dur": 1218, "ph": "X", "name": "ProcessMessages 20506", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808300976, "dur": 696, "ph": "X", "name": "ReadAsync 20506", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808301675, "dur": 6, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808301683, "dur": 51, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808301742, "dur": 29, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808301776, "dur": 66, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808301847, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808301932, "dur": 3, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808301936, "dur": 84, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302026, "dur": 2, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302028, "dur": 57, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302089, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302093, "dur": 36, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302131, "dur": 29, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302164, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302191, "dur": 32, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302227, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302229, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302262, "dur": 24, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302288, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302291, "dur": 32, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302325, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302345, "dur": 19, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302366, "dur": 24, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302393, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302418, "dur": 20, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302441, "dur": 16, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302459, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302483, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302504, "dur": 13, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302521, "dur": 18, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302542, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302569, "dur": 18, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302588, "dur": 20, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302610, "dur": 18, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302631, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302660, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302683, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302702, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302722, "dur": 19, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302744, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302769, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302789, "dur": 22, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302814, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302835, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302857, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302878, "dur": 20, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302900, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302924, "dur": 13, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302940, "dur": 35, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808302976, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303016, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303040, "dur": 32, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303075, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303080, "dur": 65, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303147, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303149, "dur": 23, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303175, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303190, "dur": 24, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303215, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303217, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303236, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303253, "dur": 19, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303274, "dur": 21, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303299, "dur": 14, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303315, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303338, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303373, "dur": 29, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303405, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303434, "dur": 40, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303475, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303477, "dur": 16, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303496, "dur": 16, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303514, "dur": 46, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303563, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303583, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303585, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303604, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303606, "dur": 14, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303623, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303647, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303665, "dur": 19, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303686, "dur": 21, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303710, "dur": 18, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303730, "dur": 40, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303772, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303793, "dur": 17, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303813, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303832, "dur": 18, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303853, "dur": 16, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303870, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303890, "dur": 18, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303910, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303928, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303930, "dur": 34, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303966, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808303991, "dur": 26, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304022, "dur": 26, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304050, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304052, "dur": 23, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304076, "dur": 2, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304080, "dur": 29, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304111, "dur": 18, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304132, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304153, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304197, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304202, "dur": 85, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304289, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304296, "dur": 60, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304359, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304363, "dur": 44, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304414, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304417, "dur": 50, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304474, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304478, "dur": 59, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304540, "dur": 3, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304545, "dur": 66, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304614, "dur": 26, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304643, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304701, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304731, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304754, "dur": 19, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304777, "dur": 38, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304815, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304817, "dur": 17, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304836, "dur": 15, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304854, "dur": 34, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304889, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304923, "dur": 18, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304942, "dur": 32, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304976, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808304978, "dur": 35, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305014, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305016, "dur": 23, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305041, "dur": 15, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305061, "dur": 19, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305083, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305104, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305125, "dur": 16, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305143, "dur": 21, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305166, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305204, "dur": 186, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305392, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305409, "dur": 17, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305428, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305451, "dur": 16, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305469, "dur": 37, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305507, "dur": 5, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305513, "dur": 12, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305526, "dur": 34, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305564, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305594, "dur": 25, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305621, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305622, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305652, "dur": 28, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305684, "dur": 13, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305700, "dur": 31, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305733, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305758, "dur": 27, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305790, "dur": 18, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305812, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305832, "dur": 31, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305864, "dur": 18, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305885, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305886, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305905, "dur": 31, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305940, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305957, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305978, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808305998, "dur": 16, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306017, "dur": 15, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306033, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306053, "dur": 37, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306091, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306114, "dur": 70, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306185, "dur": 14, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306203, "dur": 12, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306217, "dur": 39, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306258, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306274, "dur": 19, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306296, "dur": 21, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306320, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306340, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306362, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306363, "dur": 15, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306381, "dur": 29, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306411, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306431, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306460, "dur": 16, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306479, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306497, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306520, "dur": 12, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306533, "dur": 16, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306553, "dur": 19, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306573, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306575, "dur": 18, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306596, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306619, "dur": 24, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306644, "dur": 22, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306668, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306691, "dur": 22, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306716, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306738, "dur": 18, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306758, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306777, "dur": 21, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306802, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306832, "dur": 38, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306873, "dur": 17, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306894, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306913, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306934, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306952, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306973, "dur": 20, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808306995, "dur": 17, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307015, "dur": 39, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307058, "dur": 19, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307079, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307106, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307129, "dur": 18, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307150, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307152, "dur": 16, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307170, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307190, "dur": 17, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307209, "dur": 15, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307226, "dur": 18, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307247, "dur": 30, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307279, "dur": 21, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307302, "dur": 14, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307317, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307319, "dur": 14, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307335, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307352, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307369, "dur": 15, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307385, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307388, "dur": 17, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307409, "dur": 24, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307436, "dur": 15, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307453, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307472, "dur": 14, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307487, "dur": 16, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307506, "dur": 15, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307522, "dur": 15, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307539, "dur": 13, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307553, "dur": 16, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307570, "dur": 14, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307586, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307588, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307608, "dur": 16, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307625, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307648, "dur": 14, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307662, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307664, "dur": 13, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307678, "dur": 15, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307695, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307715, "dur": 16, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307733, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307751, "dur": 12, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307765, "dur": 15, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307782, "dur": 13, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307797, "dur": 14, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307812, "dur": 15, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307828, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307830, "dur": 10, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307843, "dur": 15, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307860, "dur": 41, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307903, "dur": 34, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307938, "dur": 18, "ph": "X", "name": "ReadAsync 1253", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307959, "dur": 15, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307978, "dur": 12, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808307993, "dur": 11, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308006, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308023, "dur": 12, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308036, "dur": 13, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308056, "dur": 14, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308074, "dur": 17, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308094, "dur": 13, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308108, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308110, "dur": 15, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308126, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308128, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308145, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308164, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308181, "dur": 24, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308207, "dur": 21, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308232, "dur": 42, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308280, "dur": 2, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308284, "dur": 86, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308373, "dur": 2, "ph": "X", "name": "ProcessMessages 1484", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308376, "dur": 38, "ph": "X", "name": "ReadAsync 1484", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308415, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308418, "dur": 45, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308470, "dur": 38, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308510, "dur": 1, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308512, "dur": 21, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308535, "dur": 26, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308563, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308600, "dur": 47, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308652, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308655, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308737, "dur": 2, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308740, "dur": 18, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308759, "dur": 2, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308762, "dur": 91, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308856, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308891, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308893, "dur": 19, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308914, "dur": 32, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308950, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808308952, "dur": 96, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309053, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309078, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309081, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309101, "dur": 70, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309172, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309193, "dur": 15, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309234, "dur": 16, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309256, "dur": 52, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309309, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309324, "dur": 16, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309342, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309359, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309377, "dur": 55, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309434, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309450, "dur": 15, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309469, "dur": 21, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309494, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309520, "dur": 47, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309569, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309573, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309641, "dur": 3, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309646, "dur": 39, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309688, "dur": 46, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309737, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309741, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309801, "dur": 2, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309804, "dur": 24, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309830, "dur": 111, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309943, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309966, "dur": 18, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808309987, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310006, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310024, "dur": 12, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310039, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310135, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310138, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310154, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310157, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310175, "dur": 2, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310178, "dur": 14, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310193, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310195, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310213, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310298, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310315, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310331, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310334, "dur": 14, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310349, "dur": 59, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310412, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310429, "dur": 9, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310440, "dur": 14, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310456, "dur": 16, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310473, "dur": 59, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310533, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310550, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310567, "dur": 14, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310583, "dur": 14, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310598, "dur": 47, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310647, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310665, "dur": 19, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310686, "dur": 15, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310703, "dur": 55, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310759, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310776, "dur": 16, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310794, "dur": 40, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310835, "dur": 18, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310854, "dur": 18, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310876, "dur": 43, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310921, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310943, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310959, "dur": 15, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310975, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808310996, "dur": 32, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311030, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311031, "dur": 21, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311055, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311079, "dur": 21, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311103, "dur": 18, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311122, "dur": 51, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311176, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311203, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311206, "dur": 15, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311223, "dur": 68, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311293, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311312, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311333, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311335, "dur": 25, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311362, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311382, "dur": 58, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311441, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311460, "dur": 16, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311479, "dur": 18, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311499, "dur": 61, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311561, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311580, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311600, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311618, "dur": 58, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311677, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311696, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311718, "dur": 21, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311741, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311789, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311818, "dur": 23, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311843, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311862, "dur": 45, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311910, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311929, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311947, "dur": 12, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808311961, "dur": 52, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312016, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312037, "dur": 19, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312058, "dur": 18, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312078, "dur": 40, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312119, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312140, "dur": 18, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312159, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312178, "dur": 47, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312227, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312277, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312281, "dur": 66, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312349, "dur": 2, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312351, "dur": 20, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312373, "dur": 48, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312426, "dur": 23, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312453, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312475, "dur": 52, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312529, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312549, "dur": 19, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312569, "dur": 17, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312588, "dur": 14, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312605, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312654, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312699, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312719, "dur": 23, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312744, "dur": 14, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312759, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312762, "dur": 67, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312830, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312864, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312866, "dur": 46, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312914, "dur": 5, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312920, "dur": 47, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312971, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808312999, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313018, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313036, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313112, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313131, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313132, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313155, "dur": 17, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313174, "dur": 77, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313253, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313283, "dur": 13, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313298, "dur": 13, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313313, "dur": 17, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313331, "dur": 83, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313416, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313434, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313450, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313470, "dur": 17, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313489, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313517, "dur": 11, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313530, "dur": 14, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313545, "dur": 16, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313562, "dur": 14, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313577, "dur": 15, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313594, "dur": 72, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313668, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313686, "dur": 15, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313702, "dur": 23, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313727, "dur": 14, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313743, "dur": 18, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313763, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313765, "dur": 63, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313829, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313849, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313874, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313890, "dur": 17, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313909, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313931, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313960, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808313963, "dur": 50, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314015, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314018, "dur": 32, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314052, "dur": 167, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314221, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314240, "dur": 17, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314259, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314281, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314284, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314308, "dur": 18, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314328, "dur": 14, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314344, "dur": 18, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314364, "dur": 14, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314379, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314408, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314423, "dur": 89, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314513, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314536, "dur": 107, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314650, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314652, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808314706, "dur": 451, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315161, "dur": 137, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315301, "dur": 6, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315308, "dur": 30, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315339, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315362, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315402, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315458, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315461, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315535, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315537, "dur": 75, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315615, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315617, "dur": 105, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315728, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315733, "dur": 51, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315787, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315789, "dur": 37, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315829, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315833, "dur": 52, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315888, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808315891, "dur": 263, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316157, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316159, "dur": 68, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316228, "dur": 3, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316232, "dur": 27, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316263, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316305, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316339, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316341, "dur": 18, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316360, "dur": 20, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316382, "dur": 49, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316436, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316439, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316491, "dur": 62, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316556, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316561, "dur": 44, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316607, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316609, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316654, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316658, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316705, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316707, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316761, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316763, "dur": 37, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316802, "dur": 24, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316830, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316853, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316879, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316910, "dur": 50, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808316962, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808317008, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808317044, "dur": 8252, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808325305, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808325344, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808325501, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808325522, "dur": 893, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808326419, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808326456, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808326509, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808326551, "dur": 513, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327067, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327081, "dur": 474, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327556, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327558, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327597, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327603, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327645, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327646, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327672, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327690, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327754, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327784, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808327786, "dur": 269, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328056, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328078, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328135, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328141, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328391, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328449, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328452, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328493, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328497, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328548, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328597, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328599, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328643, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328644, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328692, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328807, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328816, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328865, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808328871, "dur": 423, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329298, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329351, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329354, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329396, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329424, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329444, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329506, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329567, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329574, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329630, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329634, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329689, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329693, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329793, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329795, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329942, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808329997, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330002, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330054, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330108, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330180, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330186, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330260, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330306, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330343, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330411, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330417, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330492, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330494, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330529, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330585, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330666, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330699, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330729, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330807, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330834, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808330865, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331094, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331140, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331143, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331182, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331348, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331350, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331391, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331433, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331467, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331502, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331565, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331626, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331663, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331665, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331698, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331720, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331760, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331775, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331789, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331809, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331830, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331883, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331918, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808331948, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332047, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332080, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332153, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332182, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332276, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332301, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332303, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332348, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332391, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332426, "dur": 407, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332838, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332874, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332959, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332977, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808332993, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333073, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333096, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333135, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333155, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333254, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333272, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333510, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333514, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333563, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333565, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333695, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808333711, "dur": 506, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334219, "dur": 24, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334245, "dur": 103, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334350, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334379, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334484, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334519, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334537, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334560, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334638, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334657, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334781, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334807, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334879, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334910, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334959, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808334980, "dur": 451, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808335434, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808335454, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808335455, "dur": 69647, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808405118, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808405123, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808405202, "dur": 1949, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808407156, "dur": 4086, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808411245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808411248, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808411271, "dur": 779, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412060, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412066, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412111, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412113, "dur": 84, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412199, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412203, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412254, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412298, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412332, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412359, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412420, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412436, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412439, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412465, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412513, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412536, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412567, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412647, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412674, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412710, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412763, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412766, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412825, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412828, "dur": 39, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412869, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412872, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412913, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412915, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412960, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808412963, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413014, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413016, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413069, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413071, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413103, "dur": 87, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413192, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413194, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413241, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413245, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413268, "dur": 134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413406, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413408, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413453, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413495, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808413500, "dur": 1380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808414883, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808414884, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808414921, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808414956, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808414958, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415062, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415094, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415118, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415379, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415429, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415470, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415510, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415512, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415716, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415773, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415821, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415859, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415892, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808415920, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416103, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416123, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416140, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416185, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416202, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416219, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416235, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416287, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416307, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416333, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947808416347, "dur": 992916, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809409274, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809409278, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809409316, "dur": 30, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809409347, "dur": 1628, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809410983, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809410989, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809411048, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809411051, "dur": 35421, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809446477, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809446478, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809446529, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809446532, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809446555, "dur": 72395, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809518961, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809518966, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809519030, "dur": 25, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809519056, "dur": 5674, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809524743, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809524747, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809524776, "dur": 862, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809525645, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809525649, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809525668, "dur": 550, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809526220, "dur": 3874, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809530099, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809530183, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809530185, "dur": 1586, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809531778, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809531799, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809531816, "dur": 26886, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809558710, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809558713, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809558735, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809558736, "dur": 759, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809559507, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809559514, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809559549, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809559575, "dur": 3099, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809562678, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809562680, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809562753, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809562758, "dur": 1091, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809563852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809563854, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809563888, "dur": 502, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26516, "tid": 12884901888, "ts": 1753947809564392, "dur": 6446, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26516, "tid": 1482, "ts": 1753947809581970, "dur": 952, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26516, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26516, "tid": 8589934592, "ts": 1753947808274634, "dur": 61279, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26516, "tid": 8589934592, "ts": 1753947808335915, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26516, "tid": 8589934592, "ts": 1753947808335917, "dur": 962, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26516, "tid": 1482, "ts": 1753947809582923, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26516, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26516, "tid": 4294967296, "ts": 1753947808262308, "dur": 1309662, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26516, "tid": 4294967296, "ts": 1753947808265083, "dur": 6411, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26516, "tid": 4294967296, "ts": 1753947809572028, "dur": 3859, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26516, "tid": 4294967296, "ts": 1753947809574431, "dur": 37, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26516, "tid": 4294967296, "ts": 1753947809575945, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26516, "tid": 1482, "ts": 1753947809582931, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753947808287938, "dur": 68, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947808288044, "dur": 2022, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947808290079, "dur": 670, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947808290854, "dur": 60, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753947808290915, "dur": 713, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947808292824, "dur": 6188, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947808300166, "dur": 1415, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753947808301959, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947808304344, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753947808304468, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947808308213, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753947808308583, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753947808291664, "dur": 22774, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947808314449, "dur": 1248213, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947809562664, "dur": 590, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947809563300, "dur": 55, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947809563655, "dur": 69, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947809563751, "dur": 929, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753947808291793, "dur": 22686, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808314686, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947808314807, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947808314887, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808315156, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947808315664, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753947808315889, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753947808316071, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753947808316134, "dur": 231, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753947808316463, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753947808316677, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753947808316770, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808316830, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808317816, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808318651, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808319279, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808320160, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808321179, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808322341, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808323141, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808323978, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808324817, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808325630, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808326588, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808326969, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808327561, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808328093, "dur": 1378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808329472, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808329916, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808330516, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808330825, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753947808330998, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753947808331692, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808332328, "dur": 75698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808408027, "dur": 4250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753947808412327, "dur": 3299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753947808415627, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753947808415751, "dur": 1147218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808291798, "dur": 22691, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808314500, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947808314689, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947808315132, "dur": 879, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947808316015, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753947808316094, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753947808316399, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753947808316506, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753947808316622, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753947808317711, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "D:\\UNRTIY\\<PERSON><PERSON>\\2022.3.61t2\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753947808316798, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808318371, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808319107, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808319866, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808320761, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808321374, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808322156, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808323168, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808324454, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808325183, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808326118, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808326959, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808327551, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808328070, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753947808328414, "dur": 1279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753947808329698, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808330018, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808330577, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808330897, "dur": 1487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808332385, "dur": 75476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753947808407865, "dur": 4082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753947808411991, "dur": 4039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753947808416127, "dur": 1146627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808292181, "dur": 22478, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808314665, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947808314819, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947808315131, "dur": 510, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947808315679, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753947808316061, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753947808316497, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753947808316666, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753947808316770, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808316904, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808317558, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808318418, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808319205, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808319838, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808320872, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808321661, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808322764, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808324118, "dur": 929, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Utilities\\TimeUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753947808323943, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808325898, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808326884, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808327540, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808328085, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753947808328519, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808328583, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753947808329123, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808329210, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808329485, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808329918, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808330516, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808330599, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808330861, "dur": 1558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808332419, "dur": 76268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808408695, "dur": 3273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753947808411969, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753947808412171, "dur": 4002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753947808416250, "dur": 1146459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808292080, "dur": 22518, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808314614, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_BB555152ED99EC31.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808314802, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808314935, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808314990, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808315063, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41F9674DB235B210.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808315137, "dur": 429, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41F9674DB235B210.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808315640, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753947808315845, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753947808315984, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753947808316239, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753947808316514, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753947808316633, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753947808316885, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808317677, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808318504, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808319024, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808319602, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808320257, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808322307, "dur": 905, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Generic\\GenericDivide.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753947808321149, "dur": 2237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808323386, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808324312, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808325376, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808326469, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808326617, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808326882, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808327527, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808328052, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808328321, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947808329358, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808329497, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808329930, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808330536, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808330840, "dur": 792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947808331633, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808331849, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947808333049, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808333170, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947808333722, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808333822, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947808334273, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808334399, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947808334710, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753947808334862, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947808335373, "dur": 1073749, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947809410672, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753947809410374, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753947809410825, "dur": 33218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753947809410822, "dur": 34431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753947809446077, "dur": 128, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753947809446423, "dur": 72452, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753947809524495, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753947809524490, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753947809524604, "dur": 38092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808292171, "dur": 22468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808314705, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808314793, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A5A0099F491A8369.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947808314893, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808315033, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947808315103, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808315561, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947808315682, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753947808315802, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947808315873, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947808315975, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947808316095, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947808316239, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753947808316521, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947808316602, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753947808316852, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808317601, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808318672, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808319230, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808320314, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Texture\\SamplerStateNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753947808319603, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808321297, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808322695, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808323505, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808324126, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808325047, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808325980, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808326885, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808327541, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808328073, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947808328444, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808328699, "dur": 1638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753947808330339, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808330624, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808330831, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808331558, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753947808331742, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753947808332309, "dur": 75533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808407843, "dur": 4456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753947808412301, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753947808412419, "dur": 3684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753947808416212, "dur": 1146514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808292248, "dur": 22465, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808314732, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947808315078, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947808315166, "dur": 399, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947808315697, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753947808316060, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753947808316207, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753947808316489, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753947808316735, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753947808316872, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808317828, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808318362, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808318836, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808319768, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808321005, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808321774, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808322508, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808323177, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808323985, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808324836, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808325664, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808326243, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808326705, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808326857, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808327551, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808328053, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947808328498, "dur": 1163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753947808329664, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808330159, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808330316, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808330527, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808330822, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947808330986, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753947808331654, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808331821, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753947808331955, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753947808332754, "dur": 75096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808407889, "dur": 4412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753947808412302, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808412489, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808412720, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1753947808412837, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808412949, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808413371, "dur": 2349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753947808415758, "dur": 1147231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808292348, "dur": 22400, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808314757, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947808314812, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808314954, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947808315050, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808315105, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_96EF797E350252A7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947808315270, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947808315463, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947808315661, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947808315810, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947808315895, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753947808316027, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753947808316096, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753947808316245, "dur": 442, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753947808316737, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753947808316837, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808317811, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808318773, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808319512, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808320288, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808321467, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808322797, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808323667, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808325068, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808326016, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808326926, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808327553, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808328062, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947808328276, "dur": 2817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753947808331095, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808331410, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753947808331579, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808331797, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753947808332235, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808332297, "dur": 6568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808338866, "dur": 69543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808408410, "dur": 5529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753947808413940, "dur": 1397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808415378, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753947808416257, "dur": 1146522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808291925, "dur": 22613, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808314623, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_66FDAEB71878BAE6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753947808314677, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808314827, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753947808314930, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808315082, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753947808315165, "dur": 511, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753947808315704, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753947808315897, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753947808316102, "dur": 499, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753947808316604, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753947808316832, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808317657, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808318264, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808318903, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808319504, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808320314, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\Passes\\DepthNormalOnlyPass.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753947808320189, "dur": 1577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808321767, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808322854, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808323532, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808324244, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808325550, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808326574, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808326817, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808326882, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808327555, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808328097, "dur": 1394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808329492, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808329923, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808330551, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808330844, "dur": 1447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808332292, "dur": 2421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808334714, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753947808334884, "dur": 76106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808410992, "dur": 3932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753947808414985, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753947808416037, "dur": 1146883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808291853, "dur": 22660, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808314524, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947808314664, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947808314922, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808315025, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808315080, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947808315245, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947808315374, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947808315763, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753947808316091, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753947808316437, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753947808316685, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753947808316836, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808316924, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808317639, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808318072, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808318665, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808319694, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808320315, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\GraphData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753947808320066, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808321519, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808322599, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808323642, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808324743, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808325790, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808325897, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808326712, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808326786, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808326871, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808327529, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808328048, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947808328400, "dur": 3155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753947808331627, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947808331831, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753947808332892, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753947808332989, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753947808333410, "dur": 74613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808408025, "dur": 4730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753947808412757, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808412981, "dur": 2303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808415311, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753947808416207, "dur": 1146530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808291904, "dur": 22620, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808314614, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_67A51544EA16867F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753947808314706, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808314815, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808315006, "dur": 307, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753947808315469, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753947808315586, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947808315828, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753947808315912, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947808316063, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947808316243, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947808316599, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947808316752, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753947808316875, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808316931, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808318271, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808318920, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808320209, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Artistic\\Mask\\ChannelMaskNode.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753947808319774, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808320950, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808322036, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808323101, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808323747, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808324669, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808325419, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808326200, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808327007, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808327568, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808328104, "dur": 1365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808329470, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808329907, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808330513, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808330827, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808331335, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808332357, "dur": 75507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808407867, "dur": 3085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753947808410954, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808411139, "dur": 3588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753947808414792, "dur": 984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753947808415825, "dur": 1147108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808291997, "dur": 22559, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808314573, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947808314702, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947808314847, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947808314898, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808314991, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808315071, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_43A3C34EEA8A405D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947808315404, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947808315479, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947808315869, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753947808316084, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947808316307, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947808316378, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947808316458, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808316510, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947808316716, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753947808316842, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808317949, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808319050, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808319562, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808320154, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808321449, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808322310, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808323496, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808324103, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808325096, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808325904, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808326892, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808327543, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808328063, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947808328373, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753947808329375, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808329537, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753947808329921, "dur": 1712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753947808331634, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808331710, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808332320, "dur": 75519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808407841, "dur": 4044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753947808411886, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808412185, "dur": 3343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753947808415529, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947808415640, "dur": 1114161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753947809529841, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1753947809529805, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1753947809530010, "dur": 1666, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1753947809531682, "dur": 30985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808292069, "dur": 22510, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808314595, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_9D28C4643EF997CA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947808314671, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808314801, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17CAD97A34817613.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947808314903, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808315029, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808315124, "dur": 683, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9AD432D09FAC516B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947808315809, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753947808316035, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753947808316225, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753947808316454, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753947808316790, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808317633, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808318081, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808318958, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808320212, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808321327, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808322222, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808322812, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808323524, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808324145, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808325232, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808325979, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808326887, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808327539, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808328083, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947808328287, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753947808329246, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808329333, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808329456, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808329901, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808329999, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753947808330391, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753947808331278, "dur": 1098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808332376, "dur": 75654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808408036, "dur": 3783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753947808411820, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808411933, "dur": 3291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753947808415303, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753947808416102, "dur": 1146734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808292148, "dur": 22472, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808314635, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_7359D068AFC0FC5C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947808314718, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808314815, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947808314952, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947808315486, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947808315555, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947808315721, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947808315897, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947808316107, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947808316192, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947808316435, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1753947808316550, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947808316680, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947808316785, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753947808316912, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "D:\\UNRTIY\\<PERSON><PERSON>\\2022.3.61t2\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clretwrc.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753947808316912, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808318143, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808318898, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808319989, "dur": 823, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\SubGraph\\SubGraphAsset.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753947808319499, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808320946, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808321782, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808322900, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808323642, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808324682, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808326016, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808326951, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808327565, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808328083, "dur": 1356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808329490, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808329572, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808329960, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808330562, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808330848, "dur": 1440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808332289, "dur": 1439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808333729, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753947808333912, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753947808334410, "dur": 73658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808408073, "dur": 4956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753947808413144, "dur": 2477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753947808415690, "dur": 1147293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808292193, "dur": 22484, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808314702, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_27474F036FBEA425.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947808314859, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_27474F036FBEA425.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947808314986, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947808315147, "dur": 565, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_C5B475C4637F1A14.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947808315858, "dur": 413, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1753947808316335, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753947808316659, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753947808316755, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753947808316871, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808316927, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808318031, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808318556, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808319244, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808319871, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808320313, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808321326, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808322572, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808323307, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808324544, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808325533, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808326512, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808326922, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808327557, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808328081, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753947808328421, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753947808329408, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808329550, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808329644, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808329898, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808330003, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808330565, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808330852, "dur": 1435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808332308, "dur": 75536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753947808407848, "dur": 4099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753947808411985, "dur": 3987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753947808416056, "dur": 1146832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808291789, "dur": 22672, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808314663, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808314718, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947808314895, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808315085, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947808315213, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947808315389, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947808315468, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947808315720, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753947808316021, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947808316120, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947808316402, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947808316652, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753947808316794, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808317893, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808318455, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808319209, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808319860, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808320870, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808321944, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808322534, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808323503, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808324105, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808324977, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808325808, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808325996, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808326918, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808327552, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808328064, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753947808328336, "dur": 992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753947808329329, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808329508, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808329948, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808330569, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808330863, "dur": 1539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808332402, "dur": 75479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947808407884, "dur": 4013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753947808411948, "dur": 3611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753947808415633, "dur": 1108862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753947809524525, "dur": 37990, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753947809524497, "dur": 38020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753947808292296, "dur": 22437, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808314742, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947808314991, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808315098, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_075C24B5F02EF89C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947808315205, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947808315351, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947808315563, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_974E85225CDD9232.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947808315894, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1753947808316036, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753947808316120, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753947808316504, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1753947808316634, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753947808316888, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808317674, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808318624, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808319585, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808320319, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808321611, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808322379, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808322928, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808323866, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808324431, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808325390, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808326189, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808327079, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808327530, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808328051, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753947808328533, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753947808329199, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808329333, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808329493, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808329928, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808330544, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808330841, "dur": 1451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808332293, "dur": 4504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808336797, "dur": 2060, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808338857, "dur": 69164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808408022, "dur": 4156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753947808412179, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753947808412263, "dur": 3745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753947808416121, "dur": 1146682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808292402, "dur": 22364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808314779, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947808315050, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947808315132, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947808315593, "dur": 507, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753947808316102, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947808316239, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947808316546, "dur": 8494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753947808325042, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808325175, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947808325418, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808326437, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808326912, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808327550, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808328058, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947808328408, "dur": 1443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753947808329852, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808330253, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753947808330612, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808330726, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753947808331618, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808332337, "dur": 75691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808408029, "dur": 5227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753947808413327, "dur": 2300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753947808415675, "dur": 1146980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808292444, "dur": 22344, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808314801, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B4D1B567B4047FDB.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947808314893, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808315073, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947808315147, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808315328, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947808315467, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1753947808315771, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_440D2CBC9242B582.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947808315945, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753947808316158, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753947808316501, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753947808316607, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753947808316803, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808317564, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808318046, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808319185, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808319568, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808320780, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808321768, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808322502, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808323328, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808324075, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808325163, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808326428, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808326730, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808326880, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808327532, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808328046, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947808328417, "dur": 1308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753947808329726, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808329965, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947808330255, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808330324, "dur": 1125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753947808331449, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808331551, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753947808331713, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753947808332293, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 18, "ts": 1753947808332959, "dur": 198, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808333624, "dur": 71327, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 18, "ts": 1753947808407859, "dur": 4645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753947808412505, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808412713, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1753947808412778, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808412858, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808412939, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808413139, "dur": 2475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808415621, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753947808415685, "dur": 1146968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808292483, "dur": 22332, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808314837, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808314933, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808314988, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808315149, "dur": 942, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_192890A86BC12540.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808316104, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808316238, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808316478, "dur": 9683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753947808326163, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808326370, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808326972, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753947808327583, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808327669, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753947808328061, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808328344, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753947808329298, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808329486, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808329596, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753947808330120, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753947808331181, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808331252, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808331313, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808332365, "dur": 75524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808407891, "dur": 4654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753947808412746, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808412990, "dur": 2361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947808415382, "dur": 995017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947809410431, "dur": 33638, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753947809410403, "dur": 34850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753947809446304, "dur": 143, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753947809446454, "dur": 79096, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753947809529793, "dur": 28746, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753947809529788, "dur": 28753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753947809558567, "dur": 812, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753947809559382, "dur": 3300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808292520, "dur": 22332, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808314878, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753947808315028, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753947808315345, "dur": 300, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753947808315677, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1753947808315866, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1753947808315932, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753947808316030, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753947808316351, "dur": 374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753947808316806, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808317418, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808318223, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808318859, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808319540, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808320064, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808321453, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808322402, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808322991, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808324124, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808324948, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808325959, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808326745, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808326836, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808326894, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808327547, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808328085, "dur": 1361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808329447, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808329938, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808330549, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808330852, "dur": 1434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808332312, "dur": 75707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808408021, "dur": 4028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753947808412094, "dur": 3454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753947808415549, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753947808415671, "dur": 1146987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808292556, "dur": 22332, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808314906, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8444FDB0B5BEDD1D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947808314989, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8444FDB0B5BEDD1D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947808315340, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947808315899, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753947808316064, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753947808316246, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1753947808316665, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753947808316921, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808317755, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808318498, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808318998, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808320160, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808321198, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808321947, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808322558, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808323166, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808323929, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808325101, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808326547, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808326882, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808327536, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808328049, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947808328279, "dur": 2211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753947808330490, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808330852, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947808331047, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753947808332075, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947808332181, "dur": 1360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753947808333617, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947808333726, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753947808334459, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753947808334549, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753947808334786, "dur": 73256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808408054, "dur": 4457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753947808412698, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808412763, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808412962, "dur": 1814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808414833, "dur": 1172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753947808416091, "dur": 1146733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808292595, "dur": 22315, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808314925, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753947808315057, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808315114, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753947808315378, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947808315546, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947808315635, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947808315781, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_B79360A6DA5C7A31.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753947808315893, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1753947808316122, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1753947808316375, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947808316618, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947808316691, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753947808316834, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808317739, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808318421, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808318909, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808319677, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808320767, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808321319, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808322501, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808323555, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808323948, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808324734, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808325998, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808326977, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808327564, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808328103, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808329476, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808329919, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808330568, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808330865, "dur": 1529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808332394, "dur": 75473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808407871, "dur": 4227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753947808412099, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753947808412196, "dur": 3789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753947808416063, "dur": 1146799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808292638, "dur": 22303, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808314960, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947808315073, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947808315390, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947808315470, "dur": 409, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947808315881, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753947808316016, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753947808316100, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753947808316443, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753947808316611, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753947808316679, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753947808316861, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808317609, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808318050, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808319124, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808319856, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808320818, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808321480, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808322281, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808322803, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808323533, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808324307, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808325051, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808325723, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808326173, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808327069, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808327571, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808328084, "dur": 1359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808329444, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808329603, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753947808330106, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808330225, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753947808331398, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808332345, "dur": 75693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808408040, "dur": 4627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753947808412712, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753947808412823, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808412976, "dur": 1991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808415011, "dur": 1044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753947808416055, "dur": 1146853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808292674, "dur": 22287, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808315025, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753947808315117, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_95DEE68278F6B037.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753947808315168, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808315348, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753947808315683, "dur": 809, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1753947808316515, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1753947808316692, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753947808316867, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808317677, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808318403, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808319422, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808320068, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808321159, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808322623, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808323559, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808324264, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808324982, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808325866, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808326174, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808327010, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808327572, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808328092, "dur": 1350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808329529, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808329955, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808330553, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808330862, "dur": 1547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808332410, "dur": 75643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808408066, "dur": 4184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753947808412252, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753947808412368, "dur": 3343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753947808415808, "dur": 1147143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753947809569186, "dur": 1085, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26516, "tid": 1482, "ts": 1753947809583415, "dur": 1727, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26516, "tid": 1482, "ts": 1753947809585186, "dur": 1708, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26516, "tid": 1482, "ts": 1753947809580336, "dur": 7062, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}