{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21340, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21340, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21340, "tid": 19291, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21340, "tid": 19291, "ts": 1753957225297339, "dur": 471, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21340, "tid": 19291, "ts": 1753957225301554, "dur": 1193, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21340, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21340, "tid": 1, "ts": 1753957224964126, "dur": 3298, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21340, "tid": 1, "ts": 1753957224967425, "dur": 19461, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21340, "tid": 1, "ts": 1753957224986894, "dur": 20038, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21340, "tid": 19291, "ts": 1753957225302756, "dur": 20, "ph": "X", "name": "", "args": {}}, {"pid": 21340, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224962941, "dur": 9253, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224972196, "dur": 316827, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224973043, "dur": 1417, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224974464, "dur": 845, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224975311, "dur": 449, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224975764, "dur": 334, "ph": "X", "name": "ProcessMessages 20482", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976099, "dur": 56, "ph": "X", "name": "ReadAsync 20482", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976157, "dur": 3, "ph": "X", "name": "ProcessMessages 8448", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976161, "dur": 28, "ph": "X", "name": "ReadAsync 8448", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976194, "dur": 3, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976200, "dur": 76, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976279, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976282, "dur": 59, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976344, "dur": 1, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976346, "dur": 31, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976380, "dur": 18, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976400, "dur": 16, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976417, "dur": 12, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976430, "dur": 26, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976462, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976467, "dur": 52, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976521, "dur": 2, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976525, "dur": 73, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976605, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976611, "dur": 74, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976687, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976690, "dur": 57, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976749, "dur": 2, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976754, "dur": 69, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976826, "dur": 3, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976831, "dur": 83, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976921, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976925, "dur": 58, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976989, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224976992, "dur": 58, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977055, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977060, "dur": 37, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977104, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977107, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977130, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977166, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977170, "dur": 35, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977206, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977209, "dur": 33, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977246, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977249, "dur": 34, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977285, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977288, "dur": 21, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977313, "dur": 91, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977407, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977454, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977457, "dur": 28, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977492, "dur": 31, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977526, "dur": 16, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977546, "dur": 23, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977573, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977642, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977645, "dur": 68, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977715, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977718, "dur": 30, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977751, "dur": 38, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977793, "dur": 38, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977835, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977838, "dur": 61, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977902, "dur": 2, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977905, "dur": 49, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977959, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224977962, "dur": 53, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978020, "dur": 56, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978079, "dur": 2, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978082, "dur": 40, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978127, "dur": 40, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978168, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978171, "dur": 38, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978212, "dur": 57, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978275, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978278, "dur": 50, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978335, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978340, "dur": 81, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978423, "dur": 1, "ph": "X", "name": "ProcessMessages 1349", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978426, "dur": 46, "ph": "X", "name": "ReadAsync 1349", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978478, "dur": 52, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978533, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978535, "dur": 24, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978561, "dur": 21, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978591, "dur": 41, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978634, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978635, "dur": 33, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978672, "dur": 39, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978717, "dur": 26, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978747, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978777, "dur": 48, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978828, "dur": 27, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978859, "dur": 43, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978908, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978937, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978939, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978969, "dur": 26, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224978997, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979002, "dur": 22, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979029, "dur": 40, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979070, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979073, "dur": 37, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979113, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979115, "dur": 53, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979172, "dur": 29, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979202, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979206, "dur": 27, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979241, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979284, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979289, "dur": 40, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979332, "dur": 37, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979372, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979377, "dur": 60, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979439, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979442, "dur": 34, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979478, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979480, "dur": 72, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979555, "dur": 1, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979558, "dur": 47, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979611, "dur": 32, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979644, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979646, "dur": 50, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979701, "dur": 48, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979756, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979759, "dur": 58, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979820, "dur": 2, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979823, "dur": 43, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979869, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979870, "dur": 31, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979906, "dur": 31, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979940, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979945, "dur": 52, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224979999, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980003, "dur": 40, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980044, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980046, "dur": 28, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980075, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980079, "dur": 51, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980133, "dur": 65, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980203, "dur": 50, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980255, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980258, "dur": 24, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980284, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980289, "dur": 27, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980318, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980321, "dur": 30, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980352, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980355, "dur": 44, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980402, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980404, "dur": 22, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980429, "dur": 20, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980452, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980490, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980495, "dur": 111, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980610, "dur": 1, "ph": "X", "name": "ProcessMessages 1165", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980613, "dur": 50, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980666, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980671, "dur": 59, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980731, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980733, "dur": 47, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980783, "dur": 47, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980831, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980834, "dur": 24, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224980860, "dur": 188, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981051, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981082, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981087, "dur": 58, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981146, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981149, "dur": 69, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981221, "dur": 3, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981228, "dur": 46, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981277, "dur": 4, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981283, "dur": 57, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981344, "dur": 69, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981415, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981421, "dur": 48, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981473, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981476, "dur": 32, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981509, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981512, "dur": 36, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981551, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981555, "dur": 35, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981592, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981595, "dur": 33, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981631, "dur": 36, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981669, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981672, "dur": 46, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981719, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981722, "dur": 46, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981769, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981773, "dur": 41, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981817, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981819, "dur": 51, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981874, "dur": 5, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981880, "dur": 40, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981925, "dur": 34, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224981965, "dur": 38, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982011, "dur": 63, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982078, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982082, "dur": 37, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982121, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982123, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982153, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982155, "dur": 49, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982207, "dur": 37, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982247, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982252, "dur": 48, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982302, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982303, "dur": 44, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982351, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982354, "dur": 31, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982387, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982392, "dur": 43, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982436, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982439, "dur": 41, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982483, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982485, "dur": 43, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982531, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982534, "dur": 72, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982608, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982610, "dur": 44, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982656, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982658, "dur": 22, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982682, "dur": 59, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982743, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982748, "dur": 37, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982788, "dur": 50, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982840, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982843, "dur": 21, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982867, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982886, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982888, "dur": 25, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982919, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982944, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982949, "dur": 43, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224982995, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983031, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983038, "dur": 17, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983059, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983084, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983086, "dur": 55, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983143, "dur": 22, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983167, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983171, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983199, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983201, "dur": 21, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983225, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983249, "dur": 4, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983255, "dur": 18, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983280, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983306, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983308, "dur": 28, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983339, "dur": 2, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983342, "dur": 18, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983361, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983365, "dur": 24, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983395, "dur": 18, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983417, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983447, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983450, "dur": 17, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983471, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983491, "dur": 3, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983495, "dur": 25, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983523, "dur": 18, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983546, "dur": 14, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983564, "dur": 28, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983594, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983597, "dur": 25, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983628, "dur": 21, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983651, "dur": 18, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983671, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983674, "dur": 24, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983700, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983702, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983729, "dur": 23, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983756, "dur": 22, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983780, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983784, "dur": 17, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983805, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983807, "dur": 20, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983831, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983856, "dur": 19, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983878, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983904, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983927, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983931, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983953, "dur": 15, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983973, "dur": 17, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224983995, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984019, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984021, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984045, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984068, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984071, "dur": 30, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984104, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984107, "dur": 51, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984160, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984162, "dur": 58, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984221, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984223, "dur": 51, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984276, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984279, "dur": 41, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984322, "dur": 68, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984396, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984399, "dur": 33, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984438, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984478, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984480, "dur": 46, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984528, "dur": 4, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984533, "dur": 25, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984561, "dur": 29, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984592, "dur": 14, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984613, "dur": 39, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984656, "dur": 40, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984698, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984699, "dur": 31, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984734, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984736, "dur": 52, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984789, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984791, "dur": 20, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984815, "dur": 27, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984845, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984848, "dur": 50, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984900, "dur": 4, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984907, "dur": 45, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984953, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224984955, "dur": 49, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985006, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985009, "dur": 38, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985049, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985052, "dur": 56, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985109, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985111, "dur": 30, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985144, "dur": 34, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985181, "dur": 30, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985216, "dur": 2, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985220, "dur": 49, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985271, "dur": 33, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985307, "dur": 36, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985346, "dur": 30, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985380, "dur": 28, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985411, "dur": 66, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985480, "dur": 40, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985524, "dur": 40, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985568, "dur": 45, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985616, "dur": 59, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985678, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985680, "dur": 66, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985747, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985751, "dur": 43, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985797, "dur": 66, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985865, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985867, "dur": 40, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985909, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985911, "dur": 52, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985964, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224985966, "dur": 50, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986018, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986019, "dur": 25, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986046, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986048, "dur": 36, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986088, "dur": 27, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986117, "dur": 25, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986145, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986164, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986166, "dur": 29, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986198, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986238, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986290, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986292, "dur": 29, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986323, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986324, "dur": 112, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986437, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986454, "dur": 23, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986479, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986482, "dur": 39, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986522, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986525, "dur": 25, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986551, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986553, "dur": 82, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986638, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986684, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986686, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986706, "dur": 19, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986727, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986753, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986756, "dur": 98, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986857, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986861, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986909, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986927, "dur": 32, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986961, "dur": 2, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224986963, "dur": 85, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987053, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987109, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987112, "dur": 63, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987178, "dur": 31, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987213, "dur": 58, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987274, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987318, "dur": 57, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987376, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987378, "dur": 36, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987416, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987418, "dur": 108, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987533, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987581, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987583, "dur": 50, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987636, "dur": 41, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987679, "dur": 80, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987762, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987768, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987802, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987804, "dur": 39, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987845, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987847, "dur": 31, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987882, "dur": 107, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224987991, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988041, "dur": 37, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988084, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988086, "dur": 25, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988113, "dur": 46, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988162, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988207, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988209, "dur": 32, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988243, "dur": 24, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988269, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988271, "dur": 44, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988317, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988355, "dur": 18, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988374, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988394, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988396, "dur": 54, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988452, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988472, "dur": 19, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988493, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988494, "dur": 55, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988551, "dur": 63, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988618, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988672, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988673, "dur": 42, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988717, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988719, "dur": 25, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988746, "dur": 120, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988869, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988912, "dur": 36, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988951, "dur": 36, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224988989, "dur": 46, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989041, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989090, "dur": 42, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989134, "dur": 3, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989138, "dur": 25, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989165, "dur": 33, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989200, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989242, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989286, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989288, "dur": 27, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989318, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989370, "dur": 44, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989418, "dur": 47, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989468, "dur": 29, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989498, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989529, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989598, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989619, "dur": 25, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989646, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989648, "dur": 24, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989674, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989676, "dur": 84, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989765, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989816, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989818, "dur": 36, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989857, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989892, "dur": 23, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989918, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989941, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989943, "dur": 54, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224989999, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990026, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990027, "dur": 24, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990052, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990054, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990079, "dur": 119, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990200, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990202, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990255, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990257, "dur": 21, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990280, "dur": 52, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990335, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990371, "dur": 36, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990411, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990413, "dur": 58, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990475, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990478, "dur": 27, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990508, "dur": 27, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990537, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990539, "dur": 69, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990610, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990612, "dur": 43, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990657, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990661, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990707, "dur": 33, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990742, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990744, "dur": 36, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990783, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990805, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990806, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990832, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990834, "dur": 44, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990881, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990883, "dur": 65, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990951, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990955, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224990976, "dur": 23, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991002, "dur": 58, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991062, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991065, "dur": 40, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991106, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991108, "dur": 42, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991154, "dur": 52, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991207, "dur": 3, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991211, "dur": 30, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991245, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991285, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991312, "dur": 36, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991350, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991352, "dur": 28, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991382, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991387, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991438, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991484, "dur": 37, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991522, "dur": 2, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991524, "dur": 45, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991570, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991575, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991596, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991598, "dur": 49, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991648, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991650, "dur": 36, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991688, "dur": 66, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991756, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991797, "dur": 2, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991801, "dur": 49, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991851, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991853, "dur": 22, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991876, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991899, "dur": 16, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991919, "dur": 34, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991959, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224991962, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992016, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992120, "dur": 3, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992123, "dur": 54, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992180, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992183, "dur": 57, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992244, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992245, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992280, "dur": 3, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992284, "dur": 43, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992332, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992335, "dur": 68, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992404, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992407, "dur": 41, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992450, "dur": 48, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992503, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992506, "dur": 28, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992537, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992574, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992577, "dur": 52, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992633, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992635, "dur": 23, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992661, "dur": 114, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992778, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992819, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992822, "dur": 50, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992874, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992877, "dur": 27, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992906, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992907, "dur": 31, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992940, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992942, "dur": 47, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224992991, "dur": 44, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993037, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993040, "dur": 33, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993076, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993078, "dur": 80, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993162, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993165, "dur": 50, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993217, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993219, "dur": 60, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993283, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993285, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993325, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993327, "dur": 44, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993374, "dur": 36, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993412, "dur": 43, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993458, "dur": 34, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993494, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993525, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993584, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993586, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993627, "dur": 101, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993732, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993735, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224993791, "dur": 426, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994220, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994308, "dur": 4, "ph": "X", "name": "ProcessMessages 1188", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994314, "dur": 73, "ph": "X", "name": "ReadAsync 1188", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994390, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994393, "dur": 53, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994449, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994451, "dur": 62, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994515, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994518, "dur": 55, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994575, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994580, "dur": 61, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994644, "dur": 46, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994693, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994697, "dur": 63, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994764, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994766, "dur": 131, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994901, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994905, "dur": 79, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994986, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224994992, "dur": 67, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995062, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995065, "dur": 75, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995144, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995146, "dur": 62, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995212, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995218, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995286, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995290, "dur": 60, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995353, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995355, "dur": 50, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995407, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995410, "dur": 44, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995456, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995459, "dur": 57, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995518, "dur": 3, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995522, "dur": 91, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995617, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995652, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995689, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995725, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995762, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995799, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995802, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995856, "dur": 62, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995920, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995922, "dur": 46, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995972, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224995976, "dur": 48, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224996026, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224996028, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224996064, "dur": 325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224996392, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957224996437, "dur": 6106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225002549, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225002596, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225002598, "dur": 842, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225003444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225003446, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225003507, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225003510, "dur": 898, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225004412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225004415, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225004466, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225004467, "dur": 1205, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005679, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005721, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005723, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005761, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005763, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005797, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005839, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005865, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225005866, "dur": 201, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006072, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006103, "dur": 227, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006335, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006369, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006371, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006416, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006418, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006453, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006492, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006525, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006575, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006577, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006621, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006654, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225006656, "dur": 639, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007301, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007348, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007508, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007564, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007605, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007641, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007643, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007680, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007724, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007725, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007774, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007807, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007837, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007884, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007886, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007952, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225007955, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008013, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008053, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008093, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008126, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008154, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008220, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008223, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008255, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008258, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008318, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008358, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008389, "dur": 262, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008658, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008708, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008808, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008848, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008850, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008882, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008914, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008926, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008946, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008959, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008975, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225008998, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009039, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009056, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009185, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009206, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009222, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009238, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009252, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009285, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009391, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009408, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009424, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009425, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009444, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009611, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009613, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009645, "dur": 144, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009792, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009795, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009830, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009833, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009864, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225009895, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010004, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010026, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010091, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010134, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010165, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010258, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010294, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010296, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010374, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010414, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010448, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010513, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010544, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010567, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010569, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010796, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225010835, "dur": 305, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011144, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011180, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011212, "dur": 347, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011561, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011608, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011614, "dur": 136, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011753, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011757, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011803, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011807, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011837, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011919, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225011960, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012127, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012158, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012201, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012203, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012232, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012283, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012315, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012335, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012505, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012539, "dur": 413, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012955, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012983, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225012988, "dur": 53604, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225066602, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225066608, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225066646, "dur": 1226, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225067874, "dur": 5441, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225073321, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225073323, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225073383, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225073387, "dur": 541, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225073932, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225073983, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225073985, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074112, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074116, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074178, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074180, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074218, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074260, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074312, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074401, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074435, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074519, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074561, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074564, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074658, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074694, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074768, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074807, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074894, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074895, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225074949, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075027, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075073, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075115, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075117, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075166, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075168, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075200, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075202, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075238, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075240, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075277, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075322, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075324, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075371, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075373, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075418, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075452, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075501, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075543, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075598, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075631, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075677, "dur": 41, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075719, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075720, "dur": 57, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075782, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075784, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075829, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075874, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225075876, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225076068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225076070, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225076110, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225076111, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225076144, "dur": 1554, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225077703, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225077730, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225077866, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225077906, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225077908, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225077944, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078071, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078072, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078117, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078119, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078166, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078170, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078214, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078252, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078254, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078291, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078325, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078510, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078559, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078561, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078598, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078638, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078640, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078670, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078705, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078706, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225078736, "dur": 68454, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225147202, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225147207, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225147259, "dur": 25, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225147285, "dur": 1846, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225149141, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225149146, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225149197, "dur": 5117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225154318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225154320, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225154369, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225154374, "dur": 274, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225154653, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225154656, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225154719, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225154722, "dur": 75069, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225229799, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225229805, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225229841, "dur": 29, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225229871, "dur": 67, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225229948, "dur": 12, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225229962, "dur": 9978, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225239948, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225239957, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225239990, "dur": 289, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225240282, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225240323, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225240330, "dur": 1938, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225242274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225242278, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225242339, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225242368, "dur": 23716, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225266093, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225266098, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225266126, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225266128, "dur": 755, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225266889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225266891, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225266912, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225266937, "dur": 12557, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225279499, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225279502, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225279598, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225279602, "dur": 1005, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225280612, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225280614, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225280661, "dur": 495, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753957225281161, "dur": 7818, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21340, "tid": 19291, "ts": 1753957225302777, "dur": 1316, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21340, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21340, "tid": 8589934592, "ts": 1753957224961154, "dur": 45873, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21340, "tid": 8589934592, "ts": 1753957225007031, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21340, "tid": 8589934592, "ts": 1753957225007034, "dur": 1322, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21340, "tid": 19291, "ts": 1753957225304095, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21340, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21340, "tid": 4294967296, "ts": 1753957224950691, "dur": 339116, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753957224953140, "dur": 5024, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753957225289857, "dur": 4044, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753957225292303, "dur": 39, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753957225294014, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21340, "tid": 19291, "ts": 1753957225304103, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753957224971768, "dur": 1553, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957224973330, "dur": 616, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957224974088, "dur": 799, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957224975935, "dur": 540, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753957224976829, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F073B613EC8B985D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753957224976940, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753957224977549, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753957224978447, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753957224979086, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753957224979936, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_656D7410E7809ECD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753957224980907, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753957224981264, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753957224982617, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753957224982704, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753957224983240, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753957224983390, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753957224985029, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753957224986453, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753957224986693, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753957224988002, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753957224988234, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753957224989333, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753957224989810, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753957224992325, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753957224992745, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753957224993066, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753957224993794, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753957224974947, "dur": 19352, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957224994310, "dur": 285934, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957225280245, "dur": 419, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957225280746, "dur": 101, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957225281176, "dur": 78, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957225281298, "dur": 1057, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753957224975349, "dur": 19239, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957224994869, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753957224994980, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753957224995253, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753957224995798, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1753957224995961, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753957224996192, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753957224996266, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753957224996572, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957224997380, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957224998074, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957224998736, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957224999585, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225000975, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225001993, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225002830, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225003397, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225004012, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225004678, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225005450, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225005978, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225006453, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225006848, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753957225007146, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225007233, "dur": 1237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753957225008471, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225008557, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225008665, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225008837, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753957225009060, "dur": 1562, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225010622, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225010833, "dur": 58365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225069201, "dur": 5304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753957225074506, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225074622, "dur": 3728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753957225078406, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753957225079317, "dur": 200957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957224975501, "dur": 19141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957224994701, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957224994875, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753957224995045, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753957224995288, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753957224995441, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753957224995805, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753957224995937, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753957224996061, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753957224996141, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753957224996420, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753957224996534, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753957224996644, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957224998021, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957224998952, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957224999816, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225001001, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225001530, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225002307, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225003134, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225004532, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225005062, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225005340, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225005880, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225006434, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225006877, "dur": 1344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225008222, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225008457, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225008781, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225009429, "dur": 1387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225010842, "dur": 58360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225069204, "dur": 7284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753957225076488, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225076781, "dur": 2449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753957225079230, "dur": 201027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957224974996, "dur": 19456, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957224994461, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_9D28C4643EF997CA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753957224994559, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957224994833, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753957224994922, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753957224995048, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957224995101, "dur": 404, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753957224995511, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753957224995660, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753957224995784, "dur": 7347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753957225003132, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225003247, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225003910, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225005117, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225006010, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225006471, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225006876, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753957225007164, "dur": 1299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753957225008515, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225008569, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225008678, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225009064, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753957225009763, "dur": 739, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225010534, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225010840, "dur": 58365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225069207, "dur": 6516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753957225075724, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225075823, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225076009, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225076540, "dur": 2672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753957225079239, "dur": 201020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957224974977, "dur": 19448, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957224994442, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_099DA8B5C5E5CF06.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753957224994641, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753957224994777, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753957224994860, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753957224995042, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753957224995197, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753957224995476, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753957224995687, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753957224995854, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753957224996144, "dur": 349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753957224996495, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753957224996607, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957224997096, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957224998020, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957224998487, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957224999445, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225000652, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225001728, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225002176, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225002905, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225003853, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225004499, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225004982, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225005102, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225006078, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225006431, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225006843, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753957225007168, "dur": 1591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753957225008834, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753957225009022, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753957225009791, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225009942, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753957225010100, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753957225010572, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225010819, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1753957225011453, "dur": 117, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225011900, "dur": 55353, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1753957225069243, "dur": 5524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753957225074768, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225074871, "dur": 3732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753957225078603, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753957225079318, "dur": 200952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957224975088, "dur": 19403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957224994509, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_18D02D41D4689F77.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753957224994560, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957224994892, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957224995035, "dur": 443, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753957224995554, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753957224995657, "dur": 898, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753957224996556, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753957224996648, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957224997732, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957224998334, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957224999344, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225000613, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225001280, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225001752, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225002764, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225003306, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225004453, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225005091, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225005328, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225005974, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225006472, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225006892, "dur": 1322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225008255, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225008489, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225008727, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225009448, "dur": 1468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225010916, "dur": 58380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225069300, "dur": 5640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753957225074942, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753957225075086, "dur": 4092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753957225079248, "dur": 200994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957224974746, "dur": 19607, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957224994366, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753957224994552, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753957224994640, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957224994831, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753957224994950, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753957224995146, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957224995208, "dur": 625, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753957224995881, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753957224996025, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753957224996189, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753957224996405, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753957224996530, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753957224996610, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753957224996714, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957224997434, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957224997904, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957224998771, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957224999362, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225000012, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225001011, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225001943, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225002809, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225004019, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225005035, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225005160, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225006042, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225006462, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225006899, "dur": 1350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225008250, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225008478, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225008699, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225009450, "dur": 1445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225010895, "dur": 58352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225069250, "dur": 6846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753957225076097, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225076430, "dur": 2337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225078808, "dur": 161696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753957225240524, "dur": 39601, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753957225240506, "dur": 39621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753957224974793, "dur": 19574, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957224994380, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753957224994548, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753957224994700, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957224994798, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753957224994906, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957224995039, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957224995169, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753957224995236, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753957224995468, "dur": 985, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753957224996489, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753957224996660, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957224997471, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957224998358, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957224999277, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225000830, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225001649, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225002938, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225003674, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225004563, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225005553, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225005914, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225006448, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225006851, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753957225007073, "dur": 1283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753957225008358, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225008487, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753957225008787, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753957225009576, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225010880, "dur": 58373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225069254, "dur": 6163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753957225075419, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753957225075595, "dur": 3764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753957225079400, "dur": 200886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957224974878, "dur": 19517, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957224994551, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957224994653, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957224994881, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753957224995034, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753957224995231, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753957224995334, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753957224995491, "dur": 410, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753957224995935, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753957224996002, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753957224996220, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753957224996291, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753957224996588, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957224997617, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957224999211, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225000078, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225001130, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225002064, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225002942, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225003708, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225004793, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225005488, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225005650, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225005922, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225006446, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225006871, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753957225007046, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753957225008135, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225008281, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225008433, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225008709, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225009415, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753957225009626, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753957225010153, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225010874, "dur": 58361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225069237, "dur": 6351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753957225075589, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225075912, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225076097, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1753957225076277, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225076342, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1753957225076420, "dur": 1977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225078410, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753957225078882, "dur": 201357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957224974928, "dur": 19477, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957224994480, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_BB555152ED99EC31.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753957224994537, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957224994633, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753957224994878, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753957224994992, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957224995156, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753957224995298, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753957224995481, "dur": 404, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753957224996002, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753957224996079, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753957224996161, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753957224996428, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753957224996502, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753957224996571, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957224997506, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957224998065, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957224998868, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957224999366, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225000114, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Interfaces\\IGroupItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753957224999899, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225001216, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225001858, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225002538, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225003286, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225003818, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225004644, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225005274, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225006073, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225006466, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225006869, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753957225007278, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753957225007994, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225008382, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225008466, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225008750, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225009433, "dur": 1509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225010943, "dur": 58276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753957225069268, "dur": 5891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753957225075215, "dur": 4042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753957225079315, "dur": 201007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224974663, "dur": 19658, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224994352, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224994424, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753957224994476, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224994535, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753957224994719, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224994896, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224995000, "dur": 450, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753957224995479, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753957224995717, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753957224995858, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753957224996190, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753957224996459, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753957224996523, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753957224996582, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224997882, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224998632, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957224999556, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225000727, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225001435, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225002457, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225003520, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225004274, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225004746, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225005288, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225005887, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225006445, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225006889, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225008215, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225008421, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225008664, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225008745, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225009434, "dur": 1521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225010955, "dur": 58349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225069306, "dur": 5379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753957225074686, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225074790, "dur": 4350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753957225079141, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753957225079251, "dur": 201026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957224975045, "dur": 19424, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957224994483, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0411215F191676AE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753957224994534, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957224994834, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1983ABD9EB6C4B5C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753957224994904, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753957224995009, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957224995238, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753957224995427, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753957224995640, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753957224995878, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753957224995984, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753957224996181, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753957224996268, "dur": 458, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753957224996728, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957224997584, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957224998303, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957224998820, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957224999737, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225001114, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225001517, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225002430, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225003453, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225004384, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225005178, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225005818, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225005881, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225006439, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225006844, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753957225007032, "dur": 2423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753957225009546, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753957225009744, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753957225010976, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753957225011073, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753957225011517, "dur": 57759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225069291, "dur": 6793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753957225076085, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225076364, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753957225076430, "dur": 2132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225078619, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753957225079420, "dur": 200870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957224975138, "dur": 19371, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957224994562, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957224994790, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753957224994880, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753957224995022, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957224995122, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753957224995377, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753957224995533, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753957224995652, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753957224995961, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753957224996022, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753957224996093, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957224996188, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753957224996532, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753957224996632, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957224997427, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957224998628, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957224999458, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225000525, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225001426, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225002202, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225003008, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225003463, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225004840, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225005739, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225005875, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225006429, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225006847, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753957225007224, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225007294, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753957225008437, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225008715, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225009414, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753957225009667, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753957225010335, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753957225010500, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225010560, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753957225011230, "dur": 58061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225069303, "dur": 6354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753957225075659, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225075801, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225076090, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225076484, "dur": 2454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753957225079000, "dur": 201261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957224975193, "dur": 19328, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957224994532, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EA8F9A3BC19611D7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753957224994606, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957224994828, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753957224994903, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753957224995108, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_92F25047DCB9DD87.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753957224995576, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753957224995748, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753957224995893, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1753957224995994, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753957224996152, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1753957224996487, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753957224996565, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957224997363, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957224998417, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957224999489, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225000562, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225001209, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225001935, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225002729, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225003160, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225003659, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225004094, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225004921, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225005701, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225005812, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225005886, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225006443, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225006862, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753957225007237, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753957225008153, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225008447, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225008790, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225009420, "dur": 1206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225010626, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225010827, "dur": 58383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225069227, "dur": 6026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753957225075254, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753957225075354, "dur": 3916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753957225079335, "dur": 200928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957224975253, "dur": 19283, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957224994542, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753957224994636, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957224994857, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957224994975, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_C5B475C4637F1A14.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753957224995308, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1753957224995653, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753957224995806, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753957224996177, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753957224996255, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753957224996594, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957224997410, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957224997951, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957224998789, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225000135, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\Texture2DMaterialSlot.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753957224999989, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225001252, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225001874, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225003116, "dur": 643, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Profiling\\ProfiledSegment.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753957225003065, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225004369, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225004801, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225005435, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225006006, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225006461, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225006879, "dur": 1352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225008231, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225008498, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225008688, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225009419, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225009946, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753957225010138, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753957225010741, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225010820, "dur": 2079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225012900, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753957225013025, "dur": 56235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225069289, "dur": 6616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753957225075953, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753957225076109, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1753957225076286, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225076403, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225076823, "dur": 2483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753957225079337, "dur": 200943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957224975294, "dur": 19261, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957224994572, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753957224994870, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957224994989, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753957224995270, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753957224995372, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_BE343FF0452B4331.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753957224995646, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753957224995817, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753957224996018, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753957224996094, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753957224996207, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753957224996443, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753957224996590, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957224997298, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957224998023, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957224998645, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957224999975, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225000966, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225001608, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225002312, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225002978, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225003746, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225004247, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225005253, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225005761, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225005908, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225006463, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225006895, "dur": 1321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225008218, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225008469, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225008749, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753957225008940, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753957225009628, "dur": 854, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225010509, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225010863, "dur": 58377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225069242, "dur": 6134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753957225075377, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753957225075478, "dur": 3418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753957225078962, "dur": 201275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957224975331, "dur": 19245, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957224994584, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753957224994719, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957224995052, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753957224995328, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753957224995474, "dur": 8591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753957225004066, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957225004171, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753957225005102, "dur": 1246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753957225006476, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753957225006547, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753957225006859, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753957225007099, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753957225008550, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957225008735, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753957225008927, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753957225009473, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957225009557, "dur": 1347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957225010905, "dur": 58395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957225069304, "dur": 7365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753957225076670, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957225076812, "dur": 2494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753957225079341, "dur": 200957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957224974701, "dur": 19638, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957224994352, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957224994414, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_A8FE29C950FB2CD8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753957224994541, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753957224994790, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753957224995100, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753957224995253, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753957224995425, "dur": 306, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1753957224995885, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1753957224996059, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753957224996157, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753957224996506, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753957224996627, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957224997753, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957224999018, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957224999639, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225000621, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225001257, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225001891, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225002962, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225003719, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225004369, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225004753, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225005867, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225006444, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225006888, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753957225007315, "dur": 1228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753957225008544, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225008689, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225009422, "dur": 1397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225010820, "dur": 1044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225011865, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753957225011998, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753957225012467, "dur": 56741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225069221, "dur": 5604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753957225074826, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225074930, "dur": 3793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753957225078776, "dur": 70473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225149295, "dur": 2740, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753957225149254, "dur": 4441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753957225154954, "dur": 364, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753957225155355, "dur": 75257, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753957225240781, "dur": 25921, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753957225240775, "dur": 25931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753957225266733, "dur": 861, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753957225267598, "dur": 12651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224975393, "dur": 19206, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224994652, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224994725, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_43A3C34EEA8A405D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753957224994869, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224994963, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753957224995122, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753957224995484, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753957224995745, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753957224995968, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753957224996083, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224996195, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753957224996379, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753957224996469, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753957224996681, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224997420, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224998108, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224999084, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957224999684, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225000894, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.19\\Editor\\BurstMenu.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753957225000767, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225001911, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225002676, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225003400, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225004207, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225004905, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225005542, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225005994, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225006454, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225006858, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753957225007132, "dur": 2450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753957225009582, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225009701, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753957225009896, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753957225010519, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225010846, "dur": 58385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225069234, "dur": 6663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753957225075898, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225075993, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753957225076078, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225076246, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225076418, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225076795, "dur": 2495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753957225079352, "dur": 200956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957224975428, "dur": 19183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957224994619, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_F5A419B9F1F8246D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753957224994866, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753957224995030, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957224995173, "dur": 368, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753957224995732, "dur": 349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753957224996084, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957224996156, "dur": 474, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753957224996631, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957224997362, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957224998116, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957224998650, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957224999409, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225000578, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225001092, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225001670, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225002466, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225003021, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225003808, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225004437, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225005299, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225005917, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225006447, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225006851, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753957225007083, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753957225008773, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225009418, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225009547, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753957225009756, "dur": 1339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753957225011145, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753957225011258, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753957225011861, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753957225011984, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753957225012526, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753957225012617, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753957225012894, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753957225012988, "dur": 56239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225069232, "dur": 6672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753957225075905, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225076049, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225076452, "dur": 2399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225078855, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753957225078958, "dur": 201288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957224975491, "dur": 19131, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957224994677, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957224994794, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753957224994929, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753957224995171, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1753957224995338, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_440D2CBC9242B582.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753957224995494, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753957224995593, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753957224995803, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1753957224996001, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753957224996083, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753957224996141, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753957224996220, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753957224996517, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753957224996612, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957224996679, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957224997655, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957224998200, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957224998774, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957224999412, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225000627, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225001674, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225002479, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225003107, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225004126, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225004733, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225005725, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225005869, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225006439, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225006884, "dur": 1340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225008224, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225008509, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225008714, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225009426, "dur": 1391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225010847, "dur": 58433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225069296, "dur": 4627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753957225073990, "dur": 4821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753957225078865, "dur": 161916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753957225240817, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1753957225240784, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1753957225240987, "dur": 1967, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1753957225242961, "dur": 37339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957224974821, "dur": 19559, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957224994390, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9EEBEC0FC3FAC728.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753957224994539, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957224994640, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957224994709, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753957224994875, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957224994965, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753957224995123, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753957224995374, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753957224995532, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753957224995659, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753957224996045, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753957224996191, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753957224996451, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753957224996567, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957224997314, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957224998548, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957224999220, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225000086, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\PropertyConnectionStateMaterialSlot.cs"}}, {"pid": 12345, "tid": 21, "ts": 1753957225000011, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225001240, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225002237, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225003253, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225004191, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225004821, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225005516, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225005601, "dur": 127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225005728, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225005932, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225006455, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225006861, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753957225007337, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753957225008064, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225008276, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225008448, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225008800, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225009418, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225009584, "dur": 1302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225010887, "dur": 58356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225069268, "dur": 5867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753957225075136, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753957225075462, "dur": 3896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753957225079414, "dur": 200912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957224975551, "dur": 19119, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957224994874, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957224994982, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_192890A86BC12540.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753957224995070, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753957224995132, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753957224995238, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753957224995427, "dur": 478, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1753957224995923, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1753957224995999, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753957224996095, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753957224996182, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753957224996261, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753957224996560, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957224997412, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957224997965, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957224999004, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957224999572, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225000534, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225001147, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225002150, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225002912, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225003698, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225004440, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225005370, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225005848, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225006427, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225006846, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753957225007156, "dur": 1842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225008998, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225009430, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753957225009645, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225010587, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753957225010695, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225012024, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753957225012143, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225012843, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753957225012927, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225013190, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225013694, "dur": 134149, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225149633, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 22, "ts": 1753957225149242, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225149807, "dur": 2145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 22, "ts": 1753957225149804, "dur": 3863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225154576, "dur": 274, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753957225155342, "dur": 75138, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753957225240507, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1753957225240498, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 22, "ts": 1753957225240618, "dur": 39678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957224975609, "dur": 19071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957224994911, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753957224995239, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753957224995433, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753957224995569, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753957224995769, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753957224995887, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753957224996012, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753957224996091, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753957224996202, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753957224996445, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753957224996574, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957224997211, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957224998287, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957224999220, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957224999847, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225001071, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225002003, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225003006, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225003448, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225003934, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225005115, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225005857, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225006447, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225006888, "dur": 1324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225008266, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225008459, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225008760, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225009441, "dur": 1489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225010931, "dur": 58340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225069273, "dur": 5617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753957225074891, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753957225074984, "dur": 4278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753957225079328, "dur": 200955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957224975641, "dur": 19052, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957224994694, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753957224994784, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957224994871, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957224994970, "dur": 330, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753957224995378, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_965F5DAE5AE88371.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753957224995536, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753957224995606, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753957224995671, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753957224996016, "dur": 402, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753957224996420, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753957224996601, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753957224996690, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957224997497, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957224998137, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957224998806, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957224999666, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225000836, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225001685, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225002393, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225002962, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225003469, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225004180, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225004929, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225005312, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225005761, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225005918, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225006451, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225006870, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753957225007192, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753957225008197, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225008321, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753957225008676, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753957225009972, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225010857, "dur": 58431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225069291, "dur": 5303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753957225074647, "dur": 3874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753957225078577, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753957225079426, "dur": 200890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753957225288041, "dur": 1164, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21340, "tid": 19291, "ts": 1753957225304653, "dur": 2377, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21340, "tid": 19291, "ts": 1753957225307077, "dur": 2112, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21340, "tid": 19291, "ts": 1753957225300076, "dur": 9730, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}