{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21340, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21340, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21340, "tid": 19772, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21340, "tid": 19772, "ts": 1753958098351973, "dur": 465, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21340, "tid": 19772, "ts": 1753958098355457, "dur": 889, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21340, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21340, "tid": 1, "ts": 1753958097999204, "dur": 3603, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21340, "tid": 1, "ts": 1753958098002811, "dur": 18655, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21340, "tid": 1, "ts": 1753958098021473, "dur": 21568, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21340, "tid": 19772, "ts": 1753958098356352, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 21340, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958097997936, "dur": 8416, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098006354, "dur": 327764, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098007093, "dur": 1738, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098008839, "dur": 833, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098009673, "dur": 482, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098010160, "dur": 680, "ph": "X", "name": "ProcessMessages 20482", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098010842, "dur": 125, "ph": "X", "name": "ReadAsync 20482", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098010968, "dur": 7, "ph": "X", "name": "ProcessMessages 16802", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098010977, "dur": 31, "ph": "X", "name": "ReadAsync 16802", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011010, "dur": 17, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011029, "dur": 38, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011069, "dur": 29, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011099, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011100, "dur": 27, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011129, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011153, "dur": 30, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011186, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011189, "dur": 42, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011232, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011234, "dur": 60, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011301, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011375, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011377, "dur": 44, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011426, "dur": 43, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011474, "dur": 79, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011556, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011558, "dur": 51, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011611, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011613, "dur": 38, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011654, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011656, "dur": 26, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011683, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011685, "dur": 12, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011700, "dur": 15, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011717, "dur": 24, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011744, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011771, "dur": 26, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011800, "dur": 21, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011824, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011852, "dur": 25, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011879, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011883, "dur": 26, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011915, "dur": 22, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011940, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011965, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011992, "dur": 2, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098011996, "dur": 25, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012025, "dur": 26, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012053, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012079, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012105, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012129, "dur": 41, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012173, "dur": 27, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012203, "dur": 22, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012228, "dur": 49, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012278, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012306, "dur": 29, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012338, "dur": 26, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012366, "dur": 20, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012388, "dur": 22, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012414, "dur": 25, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012441, "dur": 23, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012467, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012492, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012494, "dur": 19, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012515, "dur": 25, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012542, "dur": 24, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012567, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012569, "dur": 24, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012597, "dur": 24, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012623, "dur": 32, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012661, "dur": 37, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012700, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012703, "dur": 39, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012745, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012761, "dur": 12, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012775, "dur": 16, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012793, "dur": 15, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012809, "dur": 13, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012824, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012839, "dur": 25, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012866, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012885, "dur": 107, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012994, "dur": 1, "ph": "X", "name": "ProcessMessages 2409", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098012995, "dur": 13, "ph": "X", "name": "ReadAsync 2409", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013010, "dur": 15, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013028, "dur": 17, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013046, "dur": 14, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013063, "dur": 31, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013096, "dur": 13, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013111, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013129, "dur": 16, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013146, "dur": 14, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013163, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013185, "dur": 13, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013200, "dur": 18, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013220, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013240, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013261, "dur": 12, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013276, "dur": 16, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013295, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013310, "dur": 14, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013325, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013343, "dur": 14, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013359, "dur": 13, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013374, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013390, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013406, "dur": 11, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013419, "dur": 14, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013434, "dur": 14, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013450, "dur": 11, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013465, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013485, "dur": 14, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013501, "dur": 14, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013516, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013531, "dur": 134, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013666, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013686, "dur": 17, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013704, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013723, "dur": 14, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013740, "dur": 12, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013759, "dur": 13, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013776, "dur": 32, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013810, "dur": 16, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013829, "dur": 17, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013848, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013866, "dur": 13, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013881, "dur": 12, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013895, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013910, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013926, "dur": 13, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013941, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013958, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013974, "dur": 13, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098013989, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014010, "dur": 11, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014026, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014044, "dur": 13, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014059, "dur": 15, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014077, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014092, "dur": 10, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014104, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014121, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014136, "dur": 15, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014152, "dur": 14, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014169, "dur": 14, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014184, "dur": 13, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014199, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014217, "dur": 14, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014233, "dur": 11, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014245, "dur": 14, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014261, "dur": 13, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014276, "dur": 14, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014292, "dur": 13, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014306, "dur": 13, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014321, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014338, "dur": 14, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014354, "dur": 13, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014370, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014389, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014408, "dur": 13, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014423, "dur": 12, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014437, "dur": 14, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014452, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014453, "dur": 12, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014467, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014485, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014501, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014518, "dur": 12, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014532, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014547, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014563, "dur": 15, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014581, "dur": 14, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014597, "dur": 14, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014612, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014628, "dur": 13, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014643, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014661, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014679, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014698, "dur": 42, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014743, "dur": 11, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014757, "dur": 92, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014852, "dur": 2, "ph": "X", "name": "ProcessMessages 1622", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014855, "dur": 64, "ph": "X", "name": "ReadAsync 1622", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014922, "dur": 40, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098014965, "dur": 47, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015016, "dur": 1, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015017, "dur": 31, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015051, "dur": 18, "ph": "X", "name": "ReadAsync 1393", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015070, "dur": 17, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015089, "dur": 15, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015106, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015122, "dur": 13, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015137, "dur": 14, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015153, "dur": 10, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015165, "dur": 28, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015200, "dur": 28, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015232, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015238, "dur": 67, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015309, "dur": 2, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015313, "dur": 76, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015391, "dur": 3, "ph": "X", "name": "ProcessMessages 1177", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015396, "dur": 67, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015466, "dur": 3, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015470, "dur": 41, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015515, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015517, "dur": 62, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015581, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015586, "dur": 54, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015643, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015646, "dur": 47, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015697, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015703, "dur": 42, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015745, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015749, "dur": 34, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015788, "dur": 45, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015834, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015841, "dur": 29, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015874, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015879, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015967, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098015972, "dur": 49, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016023, "dur": 1, "ph": "X", "name": "ProcessMessages 1764", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016025, "dur": 34, "ph": "X", "name": "ReadAsync 1764", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016061, "dur": 2, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016064, "dur": 28, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016093, "dur": 2, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016097, "dur": 24, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016122, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016124, "dur": 24, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016150, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016153, "dur": 26, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016180, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016181, "dur": 57, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016241, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016243, "dur": 29, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016274, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016276, "dur": 27, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016305, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016307, "dur": 29, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016339, "dur": 26, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016368, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016393, "dur": 30, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016425, "dur": 23, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016450, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016474, "dur": 36, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016511, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016513, "dur": 25, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016540, "dur": 24, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016567, "dur": 24, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016592, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016595, "dur": 23, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016620, "dur": 38, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016659, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016661, "dur": 22, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016684, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016686, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016714, "dur": 14, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016729, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016766, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016767, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016782, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016798, "dur": 13, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016814, "dur": 13, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016829, "dur": 68, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016900, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016918, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016920, "dur": 16, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016937, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098016955, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017018, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017038, "dur": 14, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017054, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017072, "dur": 64, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017137, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017155, "dur": 25, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017189, "dur": 16, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017206, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017208, "dur": 55, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017266, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017285, "dur": 17, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017304, "dur": 10, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017315, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017375, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017394, "dur": 18, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017414, "dur": 13, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017428, "dur": 3, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017431, "dur": 53, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017488, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017504, "dur": 15, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017521, "dur": 12, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017535, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017591, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017608, "dur": 14, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017625, "dur": 15, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017642, "dur": 14, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017657, "dur": 51, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017709, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017727, "dur": 15, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017744, "dur": 17, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017762, "dur": 9, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017773, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017824, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017839, "dur": 15, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017856, "dur": 14, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017872, "dur": 61, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017934, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017951, "dur": 18, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017970, "dur": 14, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098017986, "dur": 59, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018047, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018067, "dur": 14, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018083, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018100, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018156, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018173, "dur": 16, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018192, "dur": 15, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018208, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018258, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018277, "dur": 14, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018295, "dur": 14, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018311, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018362, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018379, "dur": 14, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018395, "dur": 8, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018404, "dur": 13, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018419, "dur": 14, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018435, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018450, "dur": 49, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018500, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018517, "dur": 10, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018529, "dur": 19, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018549, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018565, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018581, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018598, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018614, "dur": 18, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018634, "dur": 13, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018651, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018666, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018724, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018742, "dur": 15, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018758, "dur": 14, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018774, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018825, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018845, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018863, "dur": 13, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018877, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018895, "dur": 58, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018954, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018994, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098018996, "dur": 31, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019029, "dur": 36, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019066, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019092, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019094, "dur": 15, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019112, "dur": 56, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019169, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019172, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019194, "dur": 15, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019211, "dur": 58, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019272, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019295, "dur": 15, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019312, "dur": 15, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019328, "dur": 12, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019341, "dur": 68, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019414, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019417, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019482, "dur": 2, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019486, "dur": 33, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019522, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019525, "dur": 60, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019586, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019588, "dur": 29, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019619, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019693, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019755, "dur": 2, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019758, "dur": 23, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019783, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019785, "dur": 49, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019836, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019838, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019866, "dur": 26, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019894, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019898, "dur": 27, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019929, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019950, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019952, "dur": 29, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098019983, "dur": 65, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020053, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020078, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020100, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020123, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020178, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020196, "dur": 18, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020217, "dur": 19, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020239, "dur": 47, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020288, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020317, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020344, "dur": 22, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020369, "dur": 139, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020510, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020528, "dur": 15, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020545, "dur": 24, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020570, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020571, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020595, "dur": 18, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020615, "dur": 106, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020724, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020747, "dur": 23, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020771, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020795, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020844, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020873, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020874, "dur": 24, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020899, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020900, "dur": 57, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020960, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020987, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098020989, "dur": 23, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021015, "dur": 67, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021084, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021115, "dur": 26, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021144, "dur": 25, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021170, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021171, "dur": 28, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021202, "dur": 23, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021228, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021251, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021308, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021335, "dur": 23, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021360, "dur": 50, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021413, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021436, "dur": 29, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021468, "dur": 24, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021493, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021494, "dur": 24, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021519, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021521, "dur": 28, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021552, "dur": 21, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021575, "dur": 20, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021597, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021650, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021677, "dur": 23, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021701, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021702, "dur": 15, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021719, "dur": 13, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021733, "dur": 13, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021748, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021762, "dur": 31, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021795, "dur": 14, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021811, "dur": 13, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021825, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021885, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098021899, "dur": 106, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022009, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022068, "dur": 368, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022444, "dur": 109, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022555, "dur": 4, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022560, "dur": 44, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022612, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022614, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022664, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022667, "dur": 46, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022716, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022718, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022746, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022798, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022800, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022860, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022862, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022912, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098022951, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023000, "dur": 63, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023065, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023067, "dur": 48, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023117, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023119, "dur": 48, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023172, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023175, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023233, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023237, "dur": 48, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023289, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023319, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023356, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023385, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023417, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023451, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023452, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023504, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023507, "dur": 42, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023551, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023553, "dur": 49, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023605, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023643, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023676, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023698, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023710, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023732, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023767, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023797, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023832, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023836, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023870, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023918, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023964, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098023967, "dur": 55, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024025, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024027, "dur": 31, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024062, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024097, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024099, "dur": 46, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024148, "dur": 4, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024153, "dur": 42, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024198, "dur": 77, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024280, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024321, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024323, "dur": 32, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024357, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024400, "dur": 140, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024543, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098024576, "dur": 7594, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098032175, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098032204, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098032226, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098032328, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098032367, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098032368, "dur": 559, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098032930, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098032956, "dur": 409, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098033367, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098033390, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098033458, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098033493, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098033532, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098033551, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098033752, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098033772, "dur": 250, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034024, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034061, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034082, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034148, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034169, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034203, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034234, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034253, "dur": 217, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034473, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034488, "dur": 476, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034967, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098034994, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035025, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035052, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035075, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035116, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035156, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035222, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035247, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035250, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035293, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035294, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035343, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035348, "dur": 40, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035391, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035415, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035464, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035496, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035520, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035522, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035556, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035592, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035644, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035667, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035707, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035728, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035771, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035795, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098035825, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036022, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036071, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036073, "dur": 198, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036274, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036320, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036355, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036375, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036396, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036445, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036447, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036486, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036547, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036548, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036596, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036628, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036631, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036675, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036714, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036716, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036731, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036762, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036787, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036836, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036852, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036871, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036968, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098036984, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037001, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037108, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037138, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037162, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037213, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037240, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037338, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037358, "dur": 220, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037583, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037623, "dur": 258, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037888, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098037906, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038017, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038040, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038122, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038142, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038191, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038196, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038304, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038355, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038416, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038418, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038458, "dur": 431, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038891, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038937, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038981, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098038983, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039043, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039075, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039108, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039110, "dur": 458, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039569, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039597, "dur": 64, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039665, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039685, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039701, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039724, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039840, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039886, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098039968, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040005, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040031, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040044, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040175, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040213, "dur": 374, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040590, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040595, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040636, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098040638, "dur": 55924, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098096575, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098096579, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098096605, "dur": 1413, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098098020, "dur": 3915, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098101941, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098101972, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098101974, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102393, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102429, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102464, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102486, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102540, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102569, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102662, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102683, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102794, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102873, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102874, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098102910, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103044, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103063, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103117, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103153, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103215, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103238, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103257, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103284, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103314, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103332, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103350, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103374, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103398, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103419, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103448, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103469, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103484, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103509, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103551, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103581, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103584, "dur": 23, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103609, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103634, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103656, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103683, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103714, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098103735, "dur": 295, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098104036, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098104077, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098104098, "dur": 1608, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105710, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105751, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105753, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105804, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105809, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105875, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105877, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105925, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105954, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098105980, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106031, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106033, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106078, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106116, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106154, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106171, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106197, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106211, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106225, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106245, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106271, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106365, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106387, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106404, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106458, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106471, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106473, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106487, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106527, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098106563, "dur": 42978, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098149551, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098149555, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098149587, "dur": 20, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098149608, "dur": 1631, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098151246, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098151249, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098151360, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098151362, "dur": 31449, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098182818, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098182824, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098182846, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098182848, "dur": 222, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098183075, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098183112, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098183114, "dur": 87394, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098270520, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098270530, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098270603, "dur": 34, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098270638, "dur": 3800, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098274450, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098274455, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098274510, "dur": 31, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098274543, "dur": 4108, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098278655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098278659, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098278698, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098278701, "dur": 3257, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098281963, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098281965, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098282012, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098282015, "dur": 1500, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098283520, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098283522, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098283549, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098283577, "dur": 21881, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098305463, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098305466, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098305491, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098305493, "dur": 782, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098306278, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098306280, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098306313, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098306336, "dur": 16647, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098322996, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098323006, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098323070, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098323072, "dur": 1003, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098324085, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098324091, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098324121, "dur": 1006, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753958098325134, "dur": 8919, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21340, "tid": 19772, "ts": 1753958098356369, "dur": 1203, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21340, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21340, "tid": 8589934592, "ts": 1753958097996183, "dur": 46915, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21340, "tid": 8589934592, "ts": 1753958098043100, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21340, "tid": 8589934592, "ts": 1753958098043103, "dur": 1176, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21340, "tid": 19772, "ts": 1753958098357573, "dur": 12, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21340, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21340, "tid": 4294967296, "ts": 1753958097984115, "dur": 350945, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753958097986923, "dur": 5845, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753958098335115, "dur": 14765, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753958098347985, "dur": 71, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753958098349953, "dur": 17, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21340, "tid": 19772, "ts": 1753958098357586, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753958098004926, "dur": 1635, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098006571, "dur": 709, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098007436, "dur": 842, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098009396, "dur": 749, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753958098010882, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753958098012245, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753958098014800, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753958098015357, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753958098015648, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_3628369EB48E4C19.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753958098008312, "dur": 13601, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098021923, "dur": 301110, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098323035, "dur": 490, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098323573, "dur": 60, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098323920, "dur": 97, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098324063, "dur": 1515, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753958098008348, "dur": 13675, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098022034, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753958098022132, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753958098022314, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098022376, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F073B613EC8B985D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753958098022477, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753958098022532, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098022719, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753958098022988, "dur": 449, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753958098023465, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753958098023602, "dur": 448, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753958098024171, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753958098024348, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098025610, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098026192, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098026942, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098028145, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098028738, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098029375, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098030234, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098030939, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098031598, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098032633, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098032939, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098033468, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098033888, "dur": 1432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098035347, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098035625, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098035875, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098037215, "dur": 61397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753958098098656, "dur": 4418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753958098103136, "dur": 3302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753958098106488, "dur": 216571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098008846, "dur": 13381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098022240, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753958098022468, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753958098022836, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753958098023047, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753958098023456, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753958098023837, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753958098024010, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753958098024254, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098025231, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098025825, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098026449, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098026883, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098027286, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098028214, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098028849, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098029550, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098030387, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098030951, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098031655, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098032304, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098032866, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098033449, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098033858, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753958098034184, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753958098035347, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753958098035526, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753958098036787, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098036878, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098037208, "dur": 61414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098098631, "dur": 4781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753958098103413, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098103485, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098103580, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098103702, "dur": 2289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753958098106022, "dur": 217055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098008285, "dur": 13694, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098022136, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098022211, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753958098022419, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098022496, "dur": 342, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753958098022886, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753958098023055, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753958098023277, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753958098023567, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753958098023993, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753958098024233, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753958098024373, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098025119, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098025874, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098026398, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098026923, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098027404, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098028397, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098029161, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098029841, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098030408, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098031149, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098031736, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098032770, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098033468, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098033886, "dur": 1405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098035363, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098035630, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098035853, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098036378, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753958098036581, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753958098037205, "dur": 61478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098098684, "dur": 4109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753958098102794, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098102879, "dur": 2934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753958098105899, "dur": 44866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098150809, "dur": 29044, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753958098150770, "dur": 30882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753958098182743, "dur": 322, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753958098183079, "dur": 91375, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753958098281803, "dur": 23613, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753958098281797, "dur": 23621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753958098305441, "dur": 855, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753958098306299, "dur": 16816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098008272, "dur": 13692, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098022118, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098022316, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098022371, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F083CD511DD2D79E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753958098022454, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_192890A86BC12540.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753958098022521, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098022705, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753958098022844, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_974E85225CDD9232.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753958098022955, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753958098023103, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753958098023211, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753958098023601, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753958098023904, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753958098024077, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753958098024207, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753958098024380, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098025628, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098026527, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098027260, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098028505, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098029204, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098030014, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098030581, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098030954, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098031437, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098031966, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098032374, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098032741, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098033447, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098033836, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753958098034037, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753958098035217, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098035359, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753958098035565, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753958098036363, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753958098036558, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753958098037183, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1753958098037789, "dur": 168, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098038425, "dur": 58118, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1753958098098612, "dur": 4660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753958098103273, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098103378, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098103509, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098103616, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098103716, "dur": 2377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753958098106131, "dur": 217005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098008512, "dur": 13594, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098022117, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_45163133026B1C76.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753958098022227, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098022375, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753958098022518, "dur": 477, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_C5B475C4637F1A14.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753958098023058, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753958098023284, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753958098023720, "dur": 407, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753958098024216, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753958098024363, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098025715, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098026526, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098027242, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098028185, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098028876, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098029956, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098030370, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098031120, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098031804, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098032193, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098032827, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098033456, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098033868, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753958098034168, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753958098035056, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098035351, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098035631, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098035873, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098037203, "dur": 61468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098098674, "dur": 4309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753958098102985, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098103382, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098103689, "dur": 2033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098105758, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753958098106499, "dur": 216623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098008203, "dur": 13753, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098021963, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753958098022148, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753958098022429, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098022493, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753958098022659, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753958098022845, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753958098022980, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753958098023210, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753958098023280, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753958098023557, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753958098023903, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753958098023996, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753958098024185, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753958098024362, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098025261, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098025881, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098026488, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098027081, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098027684, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\Overrides\\ChromaticAberration.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753958098027621, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098028838, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098029593, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098030404, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098030860, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098031445, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098032077, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098032786, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098033469, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098033879, "dur": 1413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098035334, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098035645, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098035861, "dur": 1322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098037212, "dur": 61462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098098677, "dur": 3164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753958098101843, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098101941, "dur": 4064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753958098106006, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753958098106104, "dur": 217027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098008356, "dur": 13689, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098022057, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1A0DCA6DE3746D0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753958098022115, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098022176, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753958098022308, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098022410, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CC2D34224542963B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753958098022586, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753958098022703, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753958098022788, "dur": 797, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753958098023604, "dur": 391, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753958098024051, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753958098024134, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753958098024269, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098024998, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098025711, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098026780, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098027315, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098028174, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098028974, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098029600, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098030223, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098030949, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098031715, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098032318, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098032918, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098033472, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098033875, "dur": 1420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098035295, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098035349, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098035626, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098035858, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098037187, "dur": 1758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098038946, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753958098039095, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753958098039604, "dur": 59039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098098645, "dur": 3688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753958098102334, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753958098102479, "dur": 3698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753958098106236, "dur": 216837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098008328, "dur": 13670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098022015, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_704C40EB5E4EE2B7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753958098022075, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098022147, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753958098022387, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098022520, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_95DEE68278F6B037.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753958098022730, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753958098022857, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753958098022951, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753958098023120, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_B79360A6DA5C7A31.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753958098023205, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753958098023348, "dur": 430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753958098024122, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753958098024264, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098025210, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098025958, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098026381, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098027160, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098028151, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098028692, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098029625, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098030122, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098030523, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098031250, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098032335, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098032466, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098032558, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098032821, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098033461, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098033870, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753958098034190, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753958098035155, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098035349, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098035622, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098035871, "dur": 1328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098037199, "dur": 8793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098045992, "dur": 52635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098098629, "dur": 4757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753958098103387, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098103474, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098103710, "dur": 2364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753958098106094, "dur": 216953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098008108, "dur": 13826, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098022125, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098022223, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753958098022356, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098022498, "dur": 623, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8B81E757EEA17D01.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753958098023219, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753958098023560, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753958098023841, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753958098023899, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753958098024007, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753958098024245, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098024933, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098025570, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098026342, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098027503, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Procedural\\Noise\\SimpleNoiseNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753958098027013, "dur": 1787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098028801, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098029266, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098030150, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098030782, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098031272, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098032062, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098032665, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098032874, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098033466, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098033871, "dur": 1229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098035102, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753958098035443, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098035726, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753958098036452, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098037216, "dur": 61451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098098681, "dur": 4391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753958098103073, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753958098103228, "dur": 3284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753958098106561, "dur": 216491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098008409, "dur": 13655, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098022077, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_18D02D41D4689F77.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753958098022223, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098022435, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753958098022537, "dur": 791, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753958098023501, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753958098023650, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753958098024056, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753958098024123, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753958098024735, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "D:\\UNRTIY\\<PERSON><PERSON>\\2022.3.61t2\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753958098024262, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098025387, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098026053, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098026798, "dur": 1686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098028485, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098029467, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098030248, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098030898, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098031716, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098032209, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098032832, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098033446, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098033854, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753958098034151, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098034488, "dur": 1179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753958098035668, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098036051, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753958098036276, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753958098036930, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098037010, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098037225, "dur": 61381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098098610, "dur": 3867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753958098102478, "dur": 734, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098103224, "dur": 2583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753958098105852, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098105926, "dur": 175877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753958098281825, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753958098281804, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753958098281931, "dur": 1581, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753958098283517, "dur": 39558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098008439, "dur": 13646, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098022102, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_78AB31D22221CD7D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753958098022372, "dur": 717, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753958098023224, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753958098023442, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753958098023648, "dur": 261, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1753958098023911, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753958098023993, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753958098024280, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098025095, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098025567, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098026193, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098026921, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098027530, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098028607, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098029179, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098029781, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098030367, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098031093, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098031650, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098032208, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098032803, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098033448, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098033867, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753958098034213, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753958098035629, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098035857, "dur": 1332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098037190, "dur": 2794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098039986, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753958098040176, "dur": 58438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098098618, "dur": 3727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753958098102346, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753958098102426, "dur": 3781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753958098106261, "dur": 216856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098008566, "dur": 13553, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098022126, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753958098022220, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753958098022389, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098022496, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753958098022659, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753958098022859, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753958098023088, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753958098023429, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753958098023834, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753958098024175, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753958098024325, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098024554, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098025533, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098026344, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098026879, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098027281, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098028329, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098029067, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098029649, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098030174, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098030871, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098031975, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098032328, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098032414, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098032730, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098033443, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098033842, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753958098034066, "dur": 935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753958098035002, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098035154, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098035332, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098035610, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098035850, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753958098036046, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753958098036721, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753958098036856, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753958098037598, "dur": 61020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098098621, "dur": 4613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753958098103235, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098103364, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098103461, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753958098103570, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098103699, "dur": 2176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098105905, "dur": 172634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753958098278567, "dur": 44303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753958098278542, "dur": 44333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753958098008610, "dur": 13527, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098022154, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098022369, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_075C24B5F02EF89C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098022440, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098022537, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098022697, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098023056, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753958098023215, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1753958098023382, "dur": 535, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753958098023988, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753958098024061, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753958098024137, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753958098024275, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098025134, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098025621, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098026319, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098027444, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098028675, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098029274, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098029829, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098030508, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098031076, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098031770, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098032210, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098032766, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098033442, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098033840, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098034195, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098034863, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098035008, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098035343, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098035609, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098035851, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098036375, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098036555, "dur": 1534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098038163, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098038299, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098038939, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098039069, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098039501, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098039597, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098039676, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098039978, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753958098040060, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098040619, "dur": 108910, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098151068, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753958098150758, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098151224, "dur": 28619, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753958098151221, "dur": 30306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098182481, "dur": 217, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753958098183068, "dur": 87421, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753958098278541, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1753958098278531, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1753958098278656, "dur": 44448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753958098008653, "dur": 13506, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753958098022173, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753958098022498, "dur": 1216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753958098023720, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753958098023838, "dur": 8097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753958098031936, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753958098032223, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753958098032362, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753958098032936, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753958098033478, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753958098033545, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753958098033872, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753958098034062, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753958098035096, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753958098035403, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753958098035883, "dur": 698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753958098036610, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753958098037207, "dur": 61380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753958098098590, "dur": 4432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753958098103088, "dur": 2855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753958098106011, "dur": 217016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098008705, "dur": 13468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098022240, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753958098022407, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753958098022510, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753958098022858, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_AF431D755E5610CB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753958098023210, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753958098023501, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753958098023691, "dur": 464, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753958098024157, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753958098024366, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098025068, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098026038, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Editor\\Camera\\UniversalRenderPipelineCameraEditor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753958098025841, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098027502, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098028529, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098029274, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098030125, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098031108, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098031663, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098032104, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098032803, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098033460, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098033871, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753958098034081, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753958098035133, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098035380, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098035645, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098035865, "dur": 1319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098037217, "dur": 61443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098098671, "dur": 4178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753958098102850, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098103145, "dur": 2793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753958098105939, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753958098106036, "dur": 217088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098008737, "dur": 13447, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098022192, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B4D1B567B4047FDB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753958098022282, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B4D1B567B4047FDB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753958098022354, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753958098022453, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753958098022552, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098022673, "dur": 513, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753958098023207, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1753958098023285, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753958098023493, "dur": 515, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1753958098024065, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753958098024243, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753958098024338, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098024393, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098024917, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098025860, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098026738, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098027212, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098028184, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098028702, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098029197, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098029754, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098030581, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098031357, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098031767, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098032615, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098032779, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098033467, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098033873, "dur": 1423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098035296, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098035348, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098035616, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098035870, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098037213, "dur": 61451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753958098098666, "dur": 3842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753958098102550, "dur": 3793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753958098106404, "dur": 216681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098008792, "dur": 13401, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098022200, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17CAD97A34817613.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753958098022443, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098022493, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41F9674DB235B210.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753958098022706, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753958098022856, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753958098022985, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753958098023205, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753958098023539, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753958098023880, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753958098024063, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753958098024182, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753958098024351, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098025098, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098025833, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098026585, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098027144, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098028437, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098029177, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098029764, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098030271, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098031610, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098032324, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098032519, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098032743, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098033444, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098033837, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753958098034135, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098034236, "dur": 2064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753958098036373, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753958098036577, "dur": 1220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753958098037798, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098037908, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753958098038040, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753958098038562, "dur": 60068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098098640, "dur": 4695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753958098103391, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753958098103706, "dur": 2312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753958098106045, "dur": 216978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098008824, "dur": 13384, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098022218, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753958098022358, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9AD432D09FAC516B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753958098022549, "dur": 563, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9AD432D09FAC516B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753958098023153, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753958098023363, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753958098023500, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753958098023645, "dur": 607, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1753958098024253, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098025183, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098026100, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098027018, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098027683, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098028624, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098029309, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098029901, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098030688, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098031298, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098031975, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098032876, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098033457, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098033861, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753958098034260, "dur": 1825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753958098036086, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098036405, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753958098036604, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753958098037183, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098037354, "dur": 61243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098098626, "dur": 4554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753958098103181, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098103371, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098103434, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098103599, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098103695, "dur": 2159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098105886, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753958098106564, "dur": 216524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098008151, "dur": 13796, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098022079, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098022393, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098022482, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098022549, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753958098023059, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753958098023236, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753958098023600, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753958098023903, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753958098024134, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753958098024288, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098025329, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098026058, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098026987, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098027680, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098028817, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098029331, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098030060, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098030967, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098031570, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098032207, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098032907, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098033459, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098033878, "dur": 1416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098035294, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098035361, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753958098035633, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753958098036342, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098037220, "dur": 61413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753958098098647, "dur": 4134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753958098102861, "dur": 3172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753958098106151, "dur": 216950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098008874, "dur": 13380, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098022295, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753958098022505, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098022633, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753958098022860, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_92F25047DCB9DD87.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753958098023035, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753958098023225, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1753958098023644, "dur": 422, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1753958098024107, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753958098024187, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753958098024350, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098025702, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098026843, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098027262, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098027692, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098028744, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098029239, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098029792, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098030377, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098030914, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098031503, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098031908, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098031984, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098032482, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098032680, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098032772, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098033463, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098033893, "dur": 1434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098035328, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098035611, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098035849, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753958098036043, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753958098036709, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098037201, "dur": 61447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098098651, "dur": 4916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753958098103568, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098103640, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098103729, "dur": 2392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753958098106174, "dur": 216865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098008910, "dur": 13364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098022277, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753958098022392, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098022492, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753958098022652, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753958098022792, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753958098023044, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1753958098023377, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753958098023603, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753958098024009, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1753958098024127, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753958098024271, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098025040, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098025805, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098026593, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098027685, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\SerializableTextureArray.cs"}}, {"pid": 12345, "tid": 21, "ts": 1753958098027287, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098028213, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098028749, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098029408, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098030432, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098031284, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098031873, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098032164, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098032565, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098032862, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098033462, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098033867, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753958098034175, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753958098035143, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098035342, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098035641, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098035868, "dur": 1331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098037200, "dur": 61391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098098607, "dur": 4570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753958098103178, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098103287, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098103490, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098103686, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098104084, "dur": 2165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753958098106271, "dur": 216792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098008943, "dur": 13425, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098022369, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753958098022445, "dur": 380, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753958098022877, "dur": 521, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753958098023423, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1753958098023721, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753958098023874, "dur": 8194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753958098032069, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098032206, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098032764, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098033458, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098033860, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753958098034167, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753958098035344, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098035628, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098035887, "dur": 1335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098037222, "dur": 61433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098098657, "dur": 3981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753958098102678, "dur": 2988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753958098105733, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753958098106417, "dur": 216626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098008985, "dur": 13351, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098022337, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753958098022516, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098022644, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753958098022748, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753958098022842, "dur": 566, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753958098023424, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1753958098023683, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753958098023956, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753958098024122, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753958098024259, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098024904, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098026089, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098027146, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098027595, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098028681, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098029267, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098029825, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098030615, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098031274, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098031789, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098032698, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098032894, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098033478, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098033842, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753958098034266, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753958098035493, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098035634, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098035854, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098036614, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098037191, "dur": 7092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098044284, "dur": 1703, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098045988, "dur": 52658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098098648, "dur": 5341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753958098104070, "dur": 2155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753958098106268, "dur": 216769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098009021, "dur": 13270, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098022293, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753958098022444, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098022503, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753958098022804, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753958098022910, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753958098022990, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753958098023102, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753958098023418, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753958098023508, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753958098023763, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1753958098023908, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753958098024139, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753958098024247, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098025050, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098025564, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098026209, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098026729, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098027162, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098027603, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098028893, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098029839, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098030444, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098031361, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098031974, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098032330, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098032865, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098033445, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098033838, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753958098034040, "dur": 1700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753958098035869, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753958098036030, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753958098037013, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753958098037189, "dur": 1673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753958098038922, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753958098039051, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753958098039723, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753958098039811, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753958098040029, "dur": 58612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098098642, "dur": 4050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753958098102693, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753958098102791, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753958098102855, "dur": 3201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753958098106141, "dur": 216914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753958098331780, "dur": 1570, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21340, "tid": 19772, "ts": 1753958098358217, "dur": 2331, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21340, "tid": 19772, "ts": 1753958098360579, "dur": 1698, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21340, "tid": 19772, "ts": 1753958098353988, "dur": 8926, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}