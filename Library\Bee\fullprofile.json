{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21340, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21340, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21340, "tid": 19144, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21340, "tid": 19144, "ts": 1753956747078214, "dur": 491, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21340, "tid": 19144, "ts": 1753956747082100, "dur": 590, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21340, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21340, "tid": 1, "ts": 1753956745701408, "dur": 3740, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21340, "tid": 1, "ts": 1753956745705153, "dur": 19797, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21340, "tid": 1, "ts": 1753956745724959, "dur": 22973, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21340, "tid": 19144, "ts": 1753956747082694, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 21340, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745700042, "dur": 12244, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745712289, "dur": 1357457, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745713123, "dur": 2256, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745715390, "dur": 1365, "ph": "X", "name": "ProcessMessages 5320", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745716761, "dur": 431, "ph": "X", "name": "ReadAsync 5320", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745717196, "dur": 15, "ph": "X", "name": "ProcessMessages 20565", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745717212, "dur": 73, "ph": "X", "name": "ReadAsync 20565", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745717290, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745717291, "dur": 48, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745717343, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745717345, "dur": 728, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718076, "dur": 92, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718169, "dur": 3, "ph": "X", "name": "ProcessMessages 8254", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718174, "dur": 18, "ph": "X", "name": "ReadAsync 8254", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718195, "dur": 35, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718233, "dur": 49, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718286, "dur": 14, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718301, "dur": 2, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718303, "dur": 35, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718341, "dur": 38, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718380, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718382, "dur": 18, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718403, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718420, "dur": 16, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718437, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718466, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718483, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718498, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718500, "dur": 16, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718517, "dur": 52, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718571, "dur": 14, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718587, "dur": 13, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718601, "dur": 142, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718744, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718770, "dur": 21, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718793, "dur": 17, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718812, "dur": 31, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718844, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718858, "dur": 14, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718875, "dur": 27, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718904, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718918, "dur": 12, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718932, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718948, "dur": 12, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718962, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718979, "dur": 14, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745718995, "dur": 14, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719013, "dur": 14, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719029, "dur": 14, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719044, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719072, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719089, "dur": 14, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719104, "dur": 16, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719122, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719145, "dur": 19, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719169, "dur": 18, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719189, "dur": 36, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719227, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719243, "dur": 19, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719266, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719287, "dur": 16, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719305, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719328, "dur": 13, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719343, "dur": 66, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719411, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719431, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719448, "dur": 19, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719470, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719490, "dur": 17, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719508, "dur": 32, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719543, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719565, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719598, "dur": 48, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719649, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719651, "dur": 42, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719696, "dur": 24, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719724, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719750, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719774, "dur": 35, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719812, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719814, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719839, "dur": 22, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719864, "dur": 25, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719893, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719917, "dur": 15, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719934, "dur": 16, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719952, "dur": 32, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719988, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745719991, "dur": 62, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720055, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720057, "dur": 30, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720090, "dur": 23, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720114, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720116, "dur": 20, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720138, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720140, "dur": 23, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720165, "dur": 25, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720192, "dur": 24, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720219, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720236, "dur": 13, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720251, "dur": 14, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720266, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720283, "dur": 35, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720320, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720339, "dur": 13, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720354, "dur": 12, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720367, "dur": 13, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720382, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720409, "dur": 58, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720474, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720477, "dur": 46, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720524, "dur": 4, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720539, "dur": 49, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720593, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720595, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720620, "dur": 36, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720658, "dur": 30, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720688, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720691, "dur": 15, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720707, "dur": 19, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720728, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720747, "dur": 2, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720750, "dur": 25, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720778, "dur": 16, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720796, "dur": 45, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720843, "dur": 18, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720863, "dur": 19, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720884, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720903, "dur": 8, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720912, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720930, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720947, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745720961, "dur": 114, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721077, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721095, "dur": 14, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721111, "dur": 11, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721124, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721126, "dur": 13, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721141, "dur": 18, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721161, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721176, "dur": 16, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721194, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721212, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721232, "dur": 9, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721242, "dur": 13, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721257, "dur": 12, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721270, "dur": 20, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721292, "dur": 16, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721310, "dur": 13, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721324, "dur": 13, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721339, "dur": 13, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721355, "dur": 15, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721371, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721388, "dur": 13, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721403, "dur": 13, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721418, "dur": 13, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721432, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721448, "dur": 31, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721482, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721486, "dur": 155, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721648, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721689, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721692, "dur": 21, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721716, "dur": 41, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721759, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721762, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721786, "dur": 20, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721808, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721830, "dur": 15, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721846, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721847, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721864, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721881, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721897, "dur": 23, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721923, "dur": 21, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721946, "dur": 17, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721964, "dur": 18, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721983, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745721999, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722017, "dur": 8, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722027, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722047, "dur": 30, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722079, "dur": 15, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722096, "dur": 16, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722115, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722138, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722164, "dur": 16, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722181, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722199, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722226, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722245, "dur": 13, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722259, "dur": 14, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722275, "dur": 16, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722292, "dur": 16, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722312, "dur": 15, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722330, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722350, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722371, "dur": 49, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722422, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722444, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722462, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722482, "dur": 13, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722497, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722522, "dur": 15, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722539, "dur": 16, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722557, "dur": 17, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722576, "dur": 38, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722616, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722638, "dur": 62, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722702, "dur": 43, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722747, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722748, "dur": 41, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722796, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722799, "dur": 61, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722863, "dur": 58, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722924, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722926, "dur": 26, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722954, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722956, "dur": 34, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722992, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745722993, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723016, "dur": 25, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723044, "dur": 33, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723081, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723085, "dur": 62, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723149, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723152, "dur": 52, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723208, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723210, "dur": 27, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723239, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723242, "dur": 33, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723277, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723280, "dur": 50, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723333, "dur": 27, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723363, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723407, "dur": 38, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723448, "dur": 38, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723488, "dur": 27, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723517, "dur": 28, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723546, "dur": 27, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723576, "dur": 29, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723608, "dur": 34, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723646, "dur": 43, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723690, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723693, "dur": 56, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723761, "dur": 5, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723767, "dur": 89, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723860, "dur": 3, "ph": "X", "name": "ProcessMessages 1661", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723864, "dur": 33, "ph": "X", "name": "ReadAsync 1661", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723899, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723924, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723951, "dur": 28, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745723984, "dur": 21, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724008, "dur": 29, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724041, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724043, "dur": 42, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724086, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724088, "dur": 35, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724125, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724127, "dur": 26, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724155, "dur": 14, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724170, "dur": 21, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724192, "dur": 30, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724226, "dur": 31, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724260, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724262, "dur": 24, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724288, "dur": 17, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724307, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724309, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724332, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724352, "dur": 12, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724365, "dur": 14, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724380, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724397, "dur": 14, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724414, "dur": 13, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724428, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724445, "dur": 12, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724459, "dur": 10, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724472, "dur": 23, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724497, "dur": 39, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724537, "dur": 15, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724554, "dur": 15, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724570, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724586, "dur": 14, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724601, "dur": 29, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724631, "dur": 11, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724643, "dur": 16, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724661, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724677, "dur": 10, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724689, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724715, "dur": 29, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724745, "dur": 48, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724795, "dur": 46, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724843, "dur": 49, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724894, "dur": 31, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724928, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724952, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724954, "dur": 43, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745724999, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725027, "dur": 35, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725064, "dur": 23, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725089, "dur": 21, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725113, "dur": 30, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725144, "dur": 24, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725170, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725191, "dur": 16, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725208, "dur": 16, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725225, "dur": 14, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725241, "dur": 18, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725260, "dur": 18, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725280, "dur": 24, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725306, "dur": 15, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725322, "dur": 24, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725349, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725375, "dur": 16, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725392, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725419, "dur": 17, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725438, "dur": 33, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725473, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725493, "dur": 84, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725580, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725602, "dur": 17, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725620, "dur": 15, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725636, "dur": 16, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725654, "dur": 17, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725672, "dur": 18, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725691, "dur": 142, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725834, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745725852, "dur": 1146, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727000, "dur": 39, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727040, "dur": 1, "ph": "X", "name": "ProcessMessages 2137", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727071, "dur": 98, "ph": "X", "name": "ReadAsync 2137", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727170, "dur": 3, "ph": "X", "name": "ProcessMessages 6081", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727174, "dur": 43, "ph": "X", "name": "ReadAsync 6081", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727220, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727222, "dur": 60, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727284, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727317, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727320, "dur": 14, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727336, "dur": 110, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727449, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727481, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727505, "dur": 17, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727524, "dur": 88, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727614, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727654, "dur": 34, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727691, "dur": 29, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727724, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727727, "dur": 162, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727893, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727937, "dur": 3, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727941, "dur": 32, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727978, "dur": 2, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745727982, "dur": 69, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728054, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728090, "dur": 21, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728114, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728138, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728142, "dur": 98, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728243, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728244, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728286, "dur": 23, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728314, "dur": 27, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728342, "dur": 252, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728601, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728632, "dur": 38, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728671, "dur": 13, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728688, "dur": 47, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728736, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728751, "dur": 13, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728768, "dur": 16, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728786, "dur": 16, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728804, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728837, "dur": 24, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728863, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728879, "dur": 38, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728920, "dur": 34, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745728955, "dur": 44, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729001, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729032, "dur": 13, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729046, "dur": 182, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729229, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729247, "dur": 20, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729268, "dur": 14, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729284, "dur": 16, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729302, "dur": 123, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729427, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729457, "dur": 18, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729477, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729498, "dur": 17, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729517, "dur": 95, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729614, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729631, "dur": 20, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729652, "dur": 16, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729670, "dur": 14, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729685, "dur": 97, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729784, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729799, "dur": 19, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729819, "dur": 19, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729839, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729862, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745729892, "dur": 205, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730099, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730122, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730140, "dur": 18, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730161, "dur": 13, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730176, "dur": 94, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730271, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730294, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730314, "dur": 24, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730340, "dur": 18, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730361, "dur": 99, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730462, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730526, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730528, "dur": 54, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730583, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730585, "dur": 34, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730621, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730653, "dur": 89, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730743, "dur": 49, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730793, "dur": 36, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745730831, "dur": 194, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731028, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731030, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731114, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731115, "dur": 56, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731174, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731175, "dur": 102, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731280, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731340, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731341, "dur": 44, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731388, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731391, "dur": 27, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731419, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731420, "dur": 22, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731446, "dur": 98, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731547, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731576, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731596, "dur": 20, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731618, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731638, "dur": 102, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731741, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731762, "dur": 22, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731785, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731807, "dur": 3, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731810, "dur": 39, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731851, "dur": 117, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731970, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745731988, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732012, "dur": 13, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732027, "dur": 127, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732156, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732193, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732218, "dur": 15, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732234, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732253, "dur": 77, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732331, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732348, "dur": 14, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732365, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732389, "dur": 33, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732424, "dur": 206, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732633, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732664, "dur": 15, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732680, "dur": 64, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732747, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732763, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732764, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732782, "dur": 11, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732795, "dur": 75, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732872, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732892, "dur": 16, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732910, "dur": 18, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732929, "dur": 14, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732945, "dur": 27, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732978, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745732981, "dur": 67, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733052, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733054, "dur": 66, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733123, "dur": 41, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733167, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733187, "dur": 91, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733281, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733310, "dur": 44, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733358, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733361, "dur": 84, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733448, "dur": 3, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733452, "dur": 46, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733500, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733501, "dur": 98, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733604, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733667, "dur": 1, "ph": "X", "name": "ProcessMessages 1308", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733669, "dur": 60, "ph": "X", "name": "ReadAsync 1308", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733733, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733736, "dur": 54, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733794, "dur": 46, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733844, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745733870, "dur": 162, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734034, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734049, "dur": 124, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734177, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734250, "dur": 288, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734540, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734650, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734655, "dur": 59, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734716, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734719, "dur": 50, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734772, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734774, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734836, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734839, "dur": 68, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734910, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734912, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734955, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745734998, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735004, "dur": 34, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735042, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735043, "dur": 50, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735097, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735102, "dur": 68, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735174, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735176, "dur": 51, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735229, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735232, "dur": 56, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735292, "dur": 49, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735343, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735345, "dur": 50, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735399, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735401, "dur": 56, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735460, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735463, "dur": 38, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735504, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735543, "dur": 39, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735584, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735586, "dur": 54, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735642, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735645, "dur": 48, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735695, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735726, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735730, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735771, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735773, "dur": 65, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735840, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735842, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735898, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735900, "dur": 53, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735956, "dur": 4, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745735965, "dur": 78, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736048, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736051, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736097, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736099, "dur": 36, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736139, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736190, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736191, "dur": 37, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736232, "dur": 31, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736266, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736268, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736335, "dur": 42, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736380, "dur": 91, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736474, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736476, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736543, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736544, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736584, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736628, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736630, "dur": 40, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736672, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736673, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745736708, "dur": 8475, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745745190, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745745192, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745745256, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745745258, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745745305, "dur": 1408, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745746718, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745746719, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745746776, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745746837, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745746840, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745746987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745746989, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745747041, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745747105, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745747107, "dur": 491, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745747600, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745747603, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745747623, "dur": 490, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748116, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748149, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748225, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748259, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748261, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748325, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748346, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748648, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748663, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748680, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748736, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745748988, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749008, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749029, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749042, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749069, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749089, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749157, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749198, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749200, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749240, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749270, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749273, "dur": 564, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749840, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749844, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749878, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749937, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745749965, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750131, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750174, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750176, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750202, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750248, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750250, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750297, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750333, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750373, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750408, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750445, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750476, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750515, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750551, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750568, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750608, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750612, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750653, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750657, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750691, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750725, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750754, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750881, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750915, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750951, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745750953, "dur": 230, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751187, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751224, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751261, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751262, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751303, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751305, "dur": 289, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751596, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751598, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751648, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751651, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751670, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751694, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751719, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751721, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751750, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751806, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751835, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751838, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745751946, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752005, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752007, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752059, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752061, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752107, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752109, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752149, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752153, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752193, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752214, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752351, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752376, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752378, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752402, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752421, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752439, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752458, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752479, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752521, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752567, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752662, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752692, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752758, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752790, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752813, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752924, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752926, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752967, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745752969, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753008, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753034, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753296, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753329, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753333, "dur": 479, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753814, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753831, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753853, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745753874, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754020, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754062, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754134, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754177, "dur": 369, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754548, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754592, "dur": 101, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754696, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754725, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754752, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754868, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754890, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754918, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745754936, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755160, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755193, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755215, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755301, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755327, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755329, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755390, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755408, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755439, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755460, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755503, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755531, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755534, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755623, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755653, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755734, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755764, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755864, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745755878, "dur": 524, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745756408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745756411, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745756458, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745756464, "dur": 56717, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745813191, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745813199, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745813220, "dur": 1453, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745814675, "dur": 5681, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745820362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745820364, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745820391, "dur": 262, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745820657, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745820715, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745820720, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745820767, "dur": 488, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821258, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821306, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821308, "dur": 418, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821730, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821732, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821769, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821857, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821887, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745821980, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822027, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822066, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822086, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822127, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822143, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822228, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822246, "dur": 154, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822403, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822448, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822522, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822524, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822578, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822681, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822728, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822764, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822821, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822827, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822878, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822880, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822910, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822942, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745822979, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823017, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823050, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823075, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823113, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823138, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823165, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823201, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823229, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823257, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823292, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823332, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823368, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823404, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823436, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823438, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823483, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823524, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823627, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823659, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823726, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823751, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823752, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823790, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745823819, "dur": 1764, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745825587, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745825628, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745825649, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745825814, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745825842, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745825863, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745825875, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826087, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826111, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826140, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826170, "dur": 491, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826666, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826687, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826727, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826728, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826775, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826818, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826894, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826937, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826975, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745826977, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827011, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827154, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827157, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827191, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827228, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827260, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827296, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827298, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827325, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827360, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827380, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827409, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827432, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827465, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827483, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956745827501, "dur": 880178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746707689, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746707694, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746707733, "dur": 28, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746707762, "dur": 14379, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746722150, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746722154, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746722183, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746722185, "dur": 21680, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746743877, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746743881, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746743907, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746743910, "dur": 54303, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746798226, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746798229, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746798285, "dur": 23, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746798309, "dur": 5743, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746804057, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746804089, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746804092, "dur": 1638, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746805733, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746805749, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746805767, "dur": 17605, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746823379, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746823382, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746823451, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746823454, "dur": 629, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746824087, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746824106, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746824124, "dur": 79040, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746903176, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746903180, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746903206, "dur": 22, "ph": "X", "name": "ProcessMessages 1753", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746903229, "dur": 56240, "ph": "X", "name": "ReadAsync 1753", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746959484, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746959490, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746959547, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956746959551, "dur": 59110, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747018673, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747018677, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747018719, "dur": 20, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747018741, "dur": 5446, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747024199, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747024204, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747024241, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747024244, "dur": 709, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747024958, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747024961, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747024992, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747025014, "dur": 35026, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747060049, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747060053, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747060081, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747060083, "dur": 986, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747061074, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747061075, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747061160, "dur": 42, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747061204, "dur": 951, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747062163, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747062196, "dur": 662, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21340, "tid": 12884901888, "ts": 1753956747062861, "dur": 6820, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21340, "tid": 19144, "ts": 1753956747082706, "dur": 849, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21340, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21340, "tid": 8589934592, "ts": 1753956745698282, "dur": 49681, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21340, "tid": 8589934592, "ts": 1753956745747968, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21340, "tid": 8589934592, "ts": 1753956745747971, "dur": 1434, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21340, "tid": 19144, "ts": 1753956747083555, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21340, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21340, "tid": 4294967296, "ts": 1753956745683818, "dur": 1387219, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753956745688382, "dur": 6681, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753956747071107, "dur": 3824, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753956747073555, "dur": 43, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21340, "tid": 4294967296, "ts": 1753956747074988, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21340, "tid": 19144, "ts": 1753956747083560, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753956745711776, "dur": 1919, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956745713706, "dur": 654, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956745714481, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753956745714534, "dur": 761, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956745716588, "dur": 1598, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753956745718218, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753956745719096, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_AF431D755E5610CB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753956745721009, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753956745721970, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753956745722669, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753956745724784, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753956745731638, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753956745732048, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753956745734375, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753956745715325, "dur": 19719, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956745735054, "dur": 1327124, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956747062180, "dur": 501, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956747062704, "dur": 61, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956747062791, "dur": 55, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956747063049, "dur": 56, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956747063142, "dur": 1160, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753956745715433, "dur": 19650, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745735099, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745735161, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_A8FE29C950FB2CD8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753956745735335, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745735473, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745735676, "dur": 968, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753956745736651, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753956745736785, "dur": 10833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753956745747619, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745747729, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745747982, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753956745748585, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753956745749241, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753956745749324, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753956745749765, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753956745750240, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753956745751436, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745751550, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753956745751740, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753956745752524, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745752615, "dur": 1361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745753976, "dur": 62588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745816566, "dur": 6136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753956745822704, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753956745822840, "dur": 5258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753956745828162, "dur": 1234040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745715568, "dur": 19586, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745735160, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C0425020FB5520E6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745735510, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745735617, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745735728, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745735927, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745736036, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753956745736183, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753956745736491, "dur": 319, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753956745736923, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753956745737186, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753956745737254, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753956745737624, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745738545, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745739635, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745740500, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745741561, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\FunctionMultiInput.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753956745741124, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745742837, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745743627, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745744698, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745745496, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745746156, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745747118, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745748424, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745749181, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745749808, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745751134, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745751293, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745751662, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745752008, "dur": 1950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745753959, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956745754872, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745755012, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753956745755742, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745755871, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753956745756420, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745756507, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753956745756754, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753956745756853, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753956745757411, "dur": 951217, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753956746710011, "dur": 12643, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753956746709716, "dur": 13011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753956746723013, "dur": 66, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956746723093, "dur": 181036, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753956746905447, "dur": 52705, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753956746905439, "dur": 53925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753956746960179, "dur": 128, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956746960309, "dur": 50, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753956746960362, "dur": 59254, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753956747025006, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753956747024999, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753956747025123, "dur": 848, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753956747025974, "dur": 36210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745715433, "dur": 19632, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745735097, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745735164, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753956745735292, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745735353, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753956745735478, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753956745735734, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753956745735812, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745735942, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745736058, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753956745736126, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753956745736244, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745736314, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_B79360A6DA5C7A31.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753956745736489, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753956745736756, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753956745736928, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753956745737200, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753956745737266, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753956745737491, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745738203, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745739258, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745740371, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745741107, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745742349, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745742915, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745743747, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745744448, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745745054, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745746009, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745746645, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745747756, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745748018, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745748125, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745749176, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745749751, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753956745749928, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745750152, "dur": 1215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753956745751451, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745751651, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745752017, "dur": 1943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745753960, "dur": 1788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745755750, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753956745755917, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753956745756429, "dur": 60109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745816540, "dur": 7307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753956745823848, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745823929, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Internal.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753956745823984, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745824133, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745824377, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745824483, "dur": 2328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745826852, "dur": 1400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753956745828279, "dur": 1233961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745715803, "dur": 19487, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745735305, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_66FDAEB71878BAE6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753956745735408, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745735524, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745735716, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745735840, "dur": 553, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753956745736460, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753956745736604, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753956745736756, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753956745737000, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753956745737097, "dur": 408, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753956745737507, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745738436, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745739589, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745740346, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745740946, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745742212, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745742812, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745743690, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_4_6.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753956745743543, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745745027, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Unity\\UnityThread.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753956745744653, "dur": 1683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745746338, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745747594, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.nuget.mono-cecil@1.11.4\\Mono.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753956745747369, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745748483, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745749187, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745749775, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753956745750163, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753956745751416, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745751542, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745751655, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745752027, "dur": 1935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745753963, "dur": 2793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745756757, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753956745756882, "dur": 59670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745816579, "dur": 7021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753956745823603, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745823814, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753956745823886, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745824077, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745824179, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745824338, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745824429, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745824751, "dur": 2945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745827699, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753956745827759, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753956745827996, "dur": 1234214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745715617, "dur": 19558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745735193, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_099DA8B5C5E5CF06.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753956745735263, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745735336, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753956745735406, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745735549, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745735685, "dur": 732, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_C23248CF018BB36F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753956745736422, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753956745736551, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753956745736620, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753956745736808, "dur": 443, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753956745737253, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753956745737364, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753956745737558, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745738610, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745739941, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745740991, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745741662, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745742862, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745744208, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745744969, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745745813, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745746361, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745746962, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745747311, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745748456, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745749188, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745749786, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753956745749965, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745750032, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753956745750795, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745750862, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745750930, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745751156, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745751277, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745751631, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745751980, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753956745752228, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753956745752861, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745752936, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745753020, "dur": 980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745754001, "dur": 62592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745816595, "dur": 7706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753956745824302, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745824400, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745824617, "dur": 3050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745827714, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753956745828492, "dur": 1233784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745715870, "dur": 19447, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745735333, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753956745735387, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745735501, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745735586, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753956745735714, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745735860, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_192890A86BC12540.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753956745736088, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753956745736206, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753956745736419, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753956745736532, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753956745736798, "dur": 534, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753956745737333, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753956745737538, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745738383, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745739842, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745740847, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745741413, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745742616, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745743489, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745744177, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745744798, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745745488, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745746405, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745747413, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745748371, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745749175, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745749748, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753956745750143, "dur": 1710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753956745751980, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753956745752169, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753956745753352, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745753455, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753956745753656, "dur": 1626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753956745755356, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753956745755454, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753956745756186, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753956745756305, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753956745756621, "dur": 59960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745816582, "dur": 4983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753956745821567, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956745821651, "dur": 5990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753956745827752, "dur": 881969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956746709751, "dur": 32927, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753956746709724, "dur": 34049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753956746744653, "dur": 137, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753956746744823, "dur": 54348, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753956746804926, "dur": 19351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753956746804918, "dur": 19364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753956746824309, "dur": 783, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753956746825096, "dur": 237177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745715528, "dur": 19594, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745735136, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9EEBEC0FC3FAC728.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753956745735352, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753956745735489, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745735576, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745735649, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_38E656AD7AB33EA4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753956745735736, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745735866, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_95DEE68278F6B037.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753956745735980, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753956745736295, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753956745736529, "dur": 354, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753956745736912, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753956745737010, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753956745737164, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753956745737439, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753956745737523, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745737592, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745738670, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745739616, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745740504, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745741626, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745742637, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745743881, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745744608, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745745514, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745746678, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745747339, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745748238, "dur": 964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745749202, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745749753, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753956745750039, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753956745751094, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745751171, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745751274, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745751639, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745751979, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753956745752219, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745752274, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753956745753063, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745753147, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745753207, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753956745753433, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753956745754317, "dur": 62217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745816586, "dur": 7370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753956745823957, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745824257, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745824493, "dur": 2599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745827152, "dur": 1259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753956745828423, "dur": 1233859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745715477, "dur": 19619, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745735102, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753956745735230, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745735310, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EA8F9A3BC19611D7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753956745735575, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745735659, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745735728, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753956745735991, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745736123, "dur": 398, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753956745736551, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753956745736665, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753956745736807, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753956745737156, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753956745737254, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753956745737391, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753956745737544, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745738630, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745739685, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745740484, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745741165, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745741774, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\Overrides\\ColorCurves.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753956745741726, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745742954, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745743831, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745745184, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745746061, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745746715, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745747775, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745748007, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745748276, "dur": 937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745749213, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745749768, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753956745749978, "dur": 1515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753956745751495, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745751704, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745751983, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745752723, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753956745753007, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753956745753788, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745753979, "dur": 62605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745816585, "dur": 6563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753956745823151, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753956745823214, "dur": 4682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753956745827968, "dur": 1234227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745715509, "dur": 19596, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745735115, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745735471, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745735588, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FF667941448595B3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745735734, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4DCDB8CF3244D770.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745735805, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745735928, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745736101, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2089C4C973FD212.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745736362, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753956745736531, "dur": 390, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753956745736973, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753956745737081, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753956745737345, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753956745737479, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745738385, "dur": 1700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745740086, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745740975, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745741775, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745743194, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745744114, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745744794, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745745884, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745746880, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745747652, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745747994, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745748103, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745749202, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745749747, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745750162, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753956745752719, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745753152, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745753363, "dur": 1291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753956745754655, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745754869, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753956745754991, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753956745755470, "dur": 61100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745816573, "dur": 7731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753956745824306, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745824475, "dur": 2109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745826609, "dur": 1542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753956745828173, "dur": 1234033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745715558, "dur": 19583, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745735155, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_704C40EB5E4EE2B7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753956745735213, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745735333, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745735518, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745735578, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753956745735740, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745735863, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_4BE57D83E222AE35.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753956745736011, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753956745736141, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753956745736241, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753956745736369, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753956745736766, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753956745736993, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753956745737089, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753956745737218, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753956745737335, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753956745737503, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745738104, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745738760, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745740101, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745740749, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745741583, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745742580, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745743691, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745744591, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745744976, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745745564, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745746614, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745747551, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745748485, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745749200, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745749801, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745751140, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745751267, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745751558, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753956745751745, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753956745752436, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745752942, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745753005, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745753998, "dur": 66735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745820734, "dur": 5757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753956745826492, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745826625, "dur": 1575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753956745828225, "dur": 1234059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745715678, "dur": 19534, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745735222, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0411215F191676AE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753956745735349, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745735463, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AB588DD8E6EC9F75.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753956745735524, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753956745735734, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753956745735869, "dur": 492, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753956745736392, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753956745736750, "dur": 290, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753956745737078, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753956745737263, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753956745737415, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753956745737551, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745738943, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745740330, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745741105, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745742471, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745742956, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745743826, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745744592, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745745629, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745746388, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745746908, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745747052, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745747215, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745748312, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745749183, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745749799, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745751142, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745751296, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745751642, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745752050, "dur": 1906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745754003, "dur": 62593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745816599, "dur": 7864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753956745824464, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745824773, "dur": 3115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753956745827962, "dur": 1234228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745715661, "dur": 19534, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745735212, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_9D28C4643EF997CA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753956745735281, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745735391, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745735550, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745735723, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745735833, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_C5B475C4637F1A14.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753956745736216, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753956745736489, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753956745736956, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753956745737233, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753956745737347, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753956745737435, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753956745737570, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745738611, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745739905, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745740523, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745741250, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745742521, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745743481, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745744920, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745745603, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745746573, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745747820, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745747939, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745748205, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745749212, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745749800, "dur": 1331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745751137, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745751210, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745751273, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745751618, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745751717, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753956745751927, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753956745752682, "dur": 1327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745754009, "dur": 62566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745816581, "dur": 6410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753956745822996, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753956745823125, "dur": 5014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753956745828213, "dur": 1233995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745715710, "dur": 19523, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745735255, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1A0DCA6DE3746D0.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753956745735333, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745735442, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745735573, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745735743, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753956745735909, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753956745736076, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753956745736177, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753956745736440, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753956745736743, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1753956745737148, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1753956745737481, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745738279, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745738958, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745740022, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745740961, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745742447, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745743214, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745744066, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745744624, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745745456, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745746302, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745747618, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745747769, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745747985, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745748285, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745749184, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745749764, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753956745750222, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745750827, "dur": 2148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753956745752976, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745753077, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753956745753292, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745753351, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753956745753983, "dur": 62603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745816602, "dur": 6770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753956745823373, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753956745823497, "dur": 4932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753956745828488, "dur": 1233746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745715791, "dur": 19466, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745735318, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745735456, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745735656, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745735739, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745735848, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753956745736067, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1753956745736119, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753956745736302, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_965F5DAE5AE88371.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753956745736368, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1753956745736675, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753956745736938, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753956745737096, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1753956745737257, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753956745737440, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753956745737565, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753956745737673, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745738468, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745739563, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745740393, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745741398, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745742528, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745743754, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745744299, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745745038, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745746112, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745747168, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745748406, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745749193, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745749807, "dur": 1325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745751178, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745751269, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745751641, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745752056, "dur": 1914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745753971, "dur": 62544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745816518, "dur": 6351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753956745822870, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753956745823072, "dur": 5210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753956745828328, "dur": 1233921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745715984, "dur": 19399, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745735392, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_F5A419B9F1F8246D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753956745735461, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745735581, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745735687, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753956745735776, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753956745735876, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745735927, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753956745736058, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_AF431D755E5610CB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753956745736173, "dur": 432, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753956745736627, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753956745736972, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753956745737027, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745737087, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753956745737209, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753956745737275, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753956745737475, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753956745737579, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745738792, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745739932, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745740547, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745741070, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745742336, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745743130, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745744326, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745745121, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745745988, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745746826, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745747667, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745747843, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745748189, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745749196, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745749804, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753956745750192, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753956745751372, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745751494, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745751575, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745751627, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745751981, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745752598, "dur": 1380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745753978, "dur": 62569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745816550, "dur": 6518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753956745823069, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753956745823225, "dur": 5150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753956745828420, "dur": 1233845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745715919, "dur": 19417, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745735348, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753956745735470, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745735593, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753956745735826, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745735901, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753956745736012, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753956745736307, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_BE343FF0452B4331.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753956745736528, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1753956745736742, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753956745736986, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753956745737159, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753956745737322, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753956745737457, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753956745737582, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745738376, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745739574, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745741027, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745741754, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\Passes\\DrawObjectsPass.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753956745741677, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745742970, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745743916, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745745012, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745745916, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745746596, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745747505, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745748543, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745749190, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745749803, "dur": 1332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745751135, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745751270, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745751644, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745752041, "dur": 1924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745753965, "dur": 62556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745816522, "dur": 6045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753956745822569, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753956745822707, "dur": 5511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753956745828261, "dur": 1233957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745715961, "dur": 19403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745735383, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753956745735451, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745735576, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745735693, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753956745736078, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1753956745736202, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753956745736291, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753956745736414, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753956745736665, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753956745736868, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753956745737072, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753956745737149, "dur": 500, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753956745737650, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745738870, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745740132, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745741288, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745742402, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745743256, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745744541, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745744989, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745745819, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745746836, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745747557, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745748283, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745749217, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745749833, "dur": 1315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745751148, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745751268, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745751619, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745751982, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745752600, "dur": 1372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745753973, "dur": 62536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745816512, "dur": 8036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753956745824549, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745824750, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745824805, "dur": 3139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753956745827946, "dur": 1234270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745716016, "dur": 19379, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745735443, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745735597, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753956745735774, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753956745736031, "dur": 557, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753956745736620, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753956745736866, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1753956745736961, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753956745737014, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745737129, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753956745737206, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753956745737354, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753956745737558, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745738855, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745739967, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745740902, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745742433, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745743817, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745744437, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745744967, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745745783, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745746553, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745747859, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745748350, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745749204, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745749772, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753956745750065, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753956745751430, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745751694, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753956745751884, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753956745752795, "dur": 1191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745753995, "dur": 62523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745816547, "dur": 5630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753956745822179, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745822251, "dur": 4789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753956745827110, "dur": 1246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753956745828378, "dur": 1233908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745716058, "dur": 19352, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745735430, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753956745735583, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745735651, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1983ABD9EB6C4B5C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753956745735795, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753956745735891, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745735976, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753956745736121, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753956745736414, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753956745736614, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753956745736942, "dur": 307, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753956745737250, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753956745737351, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753956745737501, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753956745737636, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745738361, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745739928, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Editor\\Overrides\\LiftGammaGainEditor.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753956745739496, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745740819, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745741569, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745742565, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745743422, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745744188, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745744735, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745745521, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745746242, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745747479, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745748319, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745749211, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745749803, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753956745750034, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753956745751416, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745751524, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745751661, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753956745751875, "dur": 1536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753956745753412, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745753521, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745754002, "dur": 62540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745816544, "dur": 4423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753956745820970, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745821341, "dur": 6274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753956745827673, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956745827762, "dur": 977159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753956746804943, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1753956746804923, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1753956746805053, "dur": 1710, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1753956746806768, "dur": 255484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745716105, "dur": 19325, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745735440, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753956745735624, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745735702, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753956745735917, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753956745735986, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745736139, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1753956745736315, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1753956745736555, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753956745736672, "dur": 355, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753956745737028, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753956745737120, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753956745737281, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753956745737516, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753956745737664, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745738398, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745739112, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745740649, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745741508, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745743138, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745744212, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745745142, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745745909, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745746709, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745747687, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745748054, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745748121, "dur": 1056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745749177, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745749745, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753956745750171, "dur": 1377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753956745751713, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753956745751878, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753956745752718, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753956745752927, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753956745753600, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745753987, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 20, "ts": 1753956745754680, "dur": 106, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745755154, "dur": 58996, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 20, "ts": 1753956745816531, "dur": 5576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753956745822110, "dur": 1543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745823671, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745823801, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745823873, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745823985, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745824166, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745824251, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745824405, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745824756, "dur": 2971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956745827765, "dur": 1197246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753956747025051, "dur": 35882, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753956747025017, "dur": 35920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753956747060970, "dur": 1092, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 21, "ts": 1753956745716185, "dur": 19265, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745735462, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CF1830D689C2C8C9.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753956745735535, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745735709, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745735846, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753956745735999, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745736066, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_92F25047DCB9DD87.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753956745736176, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753956745736406, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753956745736565, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753956745736673, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1753956745736862, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753956745736973, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753956745737119, "dur": 478, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753956745737598, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745738309, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745739614, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745740287, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745740886, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745741747, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\External\\LibTessDotNet\\PriorityQueue.cs"}}, {"pid": 12345, "tid": 21, "ts": 1753956745741746, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745743002, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745743859, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745744687, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745745485, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745746468, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745746981, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745747141, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745748184, "dur": 989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745749210, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745749755, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753956745750234, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753956745751240, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745751366, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745751500, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745751663, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745751997, "dur": 1159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745753157, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753956745753385, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753956745754850, "dur": 61717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745816570, "dur": 4962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753956745821534, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745821700, "dur": 6111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753956745827811, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753956745827923, "dur": 1234246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745716232, "dur": 19234, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745735475, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8444FDB0B5BEDD1D.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753956745735535, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745735678, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8444FDB0B5BEDD1D.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753956745735790, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753956745735967, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753956745736051, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753956745736215, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1753956745736666, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1753956745736877, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753956745736986, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753956745737113, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753956745737294, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753956745737433, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753956745737681, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745738666, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745739895, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745740631, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745741584, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745742773, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745743758, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745744657, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745745395, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745746468, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745747380, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745748258, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745749207, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745749772, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753956745750154, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753956745751136, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745751312, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745751645, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745752034, "dur": 1920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745753996, "dur": 62528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745816530, "dur": 6780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753956745823312, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753956745823397, "dur": 4919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753956745828367, "dur": 1233889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745716278, "dur": 19198, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745735535, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745735696, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_ED31B0C2C1FC524C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753956745735864, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753956745736066, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753956745736264, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_440D2CBC9242B582.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753956745736530, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753956745736652, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753956745736772, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753956745736970, "dur": 9112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753956745746085, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745746165, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745746260, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745747185, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745748339, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745749219, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745749770, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753956745750009, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753956745750893, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745751134, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753956745751196, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745751280, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745751637, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745752066, "dur": 1909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745753975, "dur": 62625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745816602, "dur": 5628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753956745822231, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745822978, "dur": 4682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753956745827660, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753956745827767, "dur": 1234408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745716350, "dur": 19141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745735492, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753956745735744, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745735843, "dur": 512, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_203FBA3B0AC878DD.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753956745736421, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753956745736598, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1753956745736948, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753956745737061, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745737202, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753956745737265, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753956745737475, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745738227, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745739058, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745740203, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745741494, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745742553, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745743726, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745744297, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745745203, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745745823, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745746271, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745746903, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745747097, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745748411, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745749179, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745749798, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745750413, "dur": 2180, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745752623, "dur": 1357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745753980, "dur": 67031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745821016, "dur": 5653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753956745826670, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745826814, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745826871, "dur": 1445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753956745828335, "dur": 1233890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753956747068920, "dur": 1173, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21340, "tid": 19144, "ts": 1753956747084328, "dur": 2110, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21340, "tid": 19144, "ts": 1753956747086475, "dur": 1694, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21340, "tid": 19144, "ts": 1753956747080741, "dur": 7980, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}