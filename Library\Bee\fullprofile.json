{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 26516, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 26516, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 26516, "tid": 1053, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 26516, "tid": 1053, "ts": 1753946408620077, "dur": 18, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 26516, "tid": 1053, "ts": 1753946408620111, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 26516, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 26516, "tid": 1, "ts": 1753946406336056, "dur": 1299, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26516, "tid": 1, "ts": 1753946406337357, "dur": 16038, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 26516, "tid": 1, "ts": 1753946406353397, "dur": 17930, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 26516, "tid": 1053, "ts": 1753946408620114, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 26516, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406336022, "dur": 14170, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406350192, "dur": 2269258, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406350207, "dur": 55, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406350269, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406350271, "dur": 361, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406350642, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406350652, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406350726, "dur": 23, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406350750, "dur": 3476, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354233, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354303, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354306, "dur": 56, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354365, "dur": 76, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354452, "dur": 7, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354461, "dur": 135, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354598, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354600, "dur": 47, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354655, "dur": 51, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354710, "dur": 83, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354796, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354798, "dur": 55, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354857, "dur": 30, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354889, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354918, "dur": 66, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406354985, "dur": 28, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355015, "dur": 30, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355048, "dur": 53, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355103, "dur": 100, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355208, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355254, "dur": 50, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355306, "dur": 32, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355341, "dur": 40, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355382, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355417, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355439, "dur": 17, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355457, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355459, "dur": 29, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355489, "dur": 31, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355522, "dur": 31, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355554, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355578, "dur": 26, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355606, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355631, "dur": 48, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355680, "dur": 30, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355711, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355736, "dur": 23, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355761, "dur": 25, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355787, "dur": 22, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355812, "dur": 44, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355858, "dur": 3, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355863, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355899, "dur": 49, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355950, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406355978, "dur": 30, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356011, "dur": 23, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356037, "dur": 28, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356067, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356090, "dur": 28, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356121, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356149, "dur": 28, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356178, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356202, "dur": 27, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356230, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356251, "dur": 50, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356304, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356322, "dur": 25, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356348, "dur": 23, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356372, "dur": 28, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356403, "dur": 28, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356433, "dur": 30, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356465, "dur": 22, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356488, "dur": 33, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356522, "dur": 21, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356545, "dur": 26, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356572, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356574, "dur": 30, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356605, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356630, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356656, "dur": 24, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356683, "dur": 38, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356723, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356750, "dur": 31, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356782, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356784, "dur": 34, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356820, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356822, "dur": 40, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356864, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356866, "dur": 41, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356909, "dur": 38, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356952, "dur": 42, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406356996, "dur": 35, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357035, "dur": 38, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357076, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357077, "dur": 45, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357124, "dur": 31, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357158, "dur": 1, "ph": "X", "name": "ProcessMessages 106", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357160, "dur": 103, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357266, "dur": 1, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357267, "dur": 39, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357310, "dur": 32, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357345, "dur": 40, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357386, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357388, "dur": 31, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357421, "dur": 33, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357456, "dur": 25, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357482, "dur": 30, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357514, "dur": 46, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357561, "dur": 38, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357602, "dur": 28, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357631, "dur": 29, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357662, "dur": 33, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357696, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357719, "dur": 25, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357745, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357774, "dur": 33, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357808, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357833, "dur": 91, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357927, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357931, "dur": 47, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357979, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406357981, "dur": 40, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358023, "dur": 27, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358051, "dur": 26, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358079, "dur": 23, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358103, "dur": 36, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358140, "dur": 25, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358167, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358189, "dur": 18, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358208, "dur": 17, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358227, "dur": 22, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358250, "dur": 19, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358270, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358340, "dur": 34, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358376, "dur": 24, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358402, "dur": 23, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358426, "dur": 33, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358462, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358495, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358520, "dur": 23, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358544, "dur": 23, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358568, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358570, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358592, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358614, "dur": 22, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358637, "dur": 25, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358663, "dur": 22, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358687, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358711, "dur": 17, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358732, "dur": 22, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358755, "dur": 24, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358780, "dur": 23, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358805, "dur": 16, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358823, "dur": 19, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358843, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358868, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358892, "dur": 13, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358907, "dur": 17, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358926, "dur": 25, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406358952, "dur": 46, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359000, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359024, "dur": 21, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359048, "dur": 13, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359063, "dur": 99, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359163, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359168, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359188, "dur": 33, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359223, "dur": 23, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359249, "dur": 35, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359286, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359302, "dur": 16, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359319, "dur": 41, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359361, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359383, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359403, "dur": 17, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359421, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359441, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359460, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359476, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359498, "dur": 24, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359526, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359552, "dur": 17, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359570, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359594, "dur": 25, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359621, "dur": 37, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359659, "dur": 24, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359686, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359689, "dur": 59, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359749, "dur": 14, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359765, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359798, "dur": 35, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359835, "dur": 48, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359885, "dur": 38, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359924, "dur": 19, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359945, "dur": 22, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359968, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406359990, "dur": 81, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360073, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360101, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360128, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360157, "dur": 22, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360180, "dur": 24, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360205, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360235, "dur": 18, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360257, "dur": 15, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360273, "dur": 30, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360305, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360322, "dur": 15, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360338, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360354, "dur": 13, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360368, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360390, "dur": 16, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360407, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360423, "dur": 15, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360440, "dur": 27, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360468, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360485, "dur": 29, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360515, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360516, "dur": 16, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360534, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360558, "dur": 16, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360575, "dur": 16, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360592, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360609, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360612, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360633, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360658, "dur": 19, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360678, "dur": 19, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360699, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360718, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360738, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360756, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360773, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360794, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360812, "dur": 15, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360830, "dur": 17, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360848, "dur": 19, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360869, "dur": 19, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360890, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360907, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360924, "dur": 24, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360950, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360976, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406360995, "dur": 53, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361050, "dur": 48, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361102, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361104, "dur": 71, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361178, "dur": 76, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361256, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361258, "dur": 82, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361343, "dur": 7, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361352, "dur": 56, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361410, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361412, "dur": 49, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361467, "dur": 40, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361510, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361512, "dur": 56, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361570, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361572, "dur": 39, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361612, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361614, "dur": 46, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361662, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361666, "dur": 128, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361797, "dur": 2, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361800, "dur": 55, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361860, "dur": 2, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361863, "dur": 56, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361923, "dur": 2, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361927, "dur": 54, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361982, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406361984, "dur": 23, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362010, "dur": 39, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362053, "dur": 49, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362106, "dur": 27, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362136, "dur": 26, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362164, "dur": 19, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362184, "dur": 29, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362215, "dur": 16, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362233, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362268, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362289, "dur": 41, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362332, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362354, "dur": 31, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362386, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362389, "dur": 55, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362448, "dur": 50, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362500, "dur": 40, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362542, "dur": 35, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362579, "dur": 30, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362611, "dur": 60, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362672, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362694, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362715, "dur": 15, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362732, "dur": 19, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362752, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362774, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362791, "dur": 71, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362863, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362882, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406362909, "dur": 179, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363090, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363121, "dur": 18, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363141, "dur": 27, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363174, "dur": 26, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363203, "dur": 21, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363227, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363250, "dur": 20, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363272, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363304, "dur": 9, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363314, "dur": 17, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363335, "dur": 17, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363353, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363355, "dur": 23, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363380, "dur": 16, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363397, "dur": 16, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363415, "dur": 15, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363432, "dur": 18, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363452, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363471, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363490, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363509, "dur": 14, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363524, "dur": 18, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363543, "dur": 14, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363560, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363577, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363578, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363595, "dur": 13, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363610, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363629, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363644, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363663, "dur": 3, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363666, "dur": 20, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363689, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363709, "dur": 17, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363728, "dur": 16, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363746, "dur": 27, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363775, "dur": 19, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363796, "dur": 32, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363830, "dur": 16, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363848, "dur": 15, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363866, "dur": 22, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363889, "dur": 27, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363918, "dur": 3, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363923, "dur": 29, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363954, "dur": 36, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406363992, "dur": 39, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364033, "dur": 44, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364079, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364080, "dur": 30, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364113, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364160, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364162, "dur": 49, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364212, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364216, "dur": 45, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364264, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364266, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364298, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364320, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364321, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364343, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364365, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364382, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364408, "dur": 17, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364427, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364438, "dur": 35, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364476, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364507, "dur": 17, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364526, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364551, "dur": 19, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364573, "dur": 14, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364591, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364611, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364630, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364649, "dur": 14, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364664, "dur": 50, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364715, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364732, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364734, "dur": 12, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364747, "dur": 35, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364783, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364786, "dur": 13, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364801, "dur": 10, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364812, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364830, "dur": 15, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364847, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364859, "dur": 55, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364915, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364935, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364955, "dur": 26, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406364983, "dur": 16, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365002, "dur": 14, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365017, "dur": 23, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365042, "dur": 13, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365056, "dur": 39, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365097, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365125, "dur": 15, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365142, "dur": 15, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365162, "dur": 18, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365181, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365198, "dur": 96, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365298, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365305, "dur": 83, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365390, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365392, "dur": 63, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365458, "dur": 4, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365464, "dur": 43, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365509, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365511, "dur": 39, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365557, "dur": 52, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365611, "dur": 135, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365750, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365752, "dur": 36, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365790, "dur": 48, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365839, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365876, "dur": 26, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365904, "dur": 24, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365930, "dur": 8, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406365939, "dur": 900, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406366847, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406366851, "dur": 330, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367182, "dur": 4, "ph": "X", "name": "ProcessMessages 5473", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367188, "dur": 76, "ph": "X", "name": "ReadAsync 5473", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367267, "dur": 65, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367334, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367335, "dur": 47, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367385, "dur": 44, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367431, "dur": 50, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367484, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367488, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367536, "dur": 36, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367575, "dur": 35, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367612, "dur": 35, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367650, "dur": 36, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367687, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367690, "dur": 36, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367729, "dur": 48, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367779, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367783, "dur": 51, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367838, "dur": 34, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367877, "dur": 28, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367907, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367908, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367931, "dur": 21, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367955, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367979, "dur": 15, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406367997, "dur": 43, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368043, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368065, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368087, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368109, "dur": 15, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368127, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368150, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368171, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368173, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368189, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368212, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368214, "dur": 14, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368230, "dur": 29, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368262, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368306, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368308, "dur": 45, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368355, "dur": 35, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368392, "dur": 33, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368428, "dur": 1, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368430, "dur": 39, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368471, "dur": 24, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368496, "dur": 15, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368512, "dur": 20, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368534, "dur": 22, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368558, "dur": 14, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368573, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368595, "dur": 17, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368614, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368634, "dur": 20, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368656, "dur": 13, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368673, "dur": 20, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368695, "dur": 13, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368709, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368732, "dur": 16, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368750, "dur": 16, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368768, "dur": 29, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368799, "dur": 25, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368825, "dur": 10, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368836, "dur": 46, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368886, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368929, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368930, "dur": 12, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368944, "dur": 17, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368964, "dur": 15, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368982, "dur": 15, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406368999, "dur": 16, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369020, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369040, "dur": 16, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369058, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369083, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369100, "dur": 64, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369165, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369190, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369209, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369230, "dur": 12, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369243, "dur": 105, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369352, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369416, "dur": 29, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369448, "dur": 78, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369528, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369559, "dur": 27, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369588, "dur": 51, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369643, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369645, "dur": 52, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369700, "dur": 80, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369786, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369906, "dur": 27, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406369962, "dur": 50, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370014, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370016, "dur": 82, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370102, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370159, "dur": 35, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370196, "dur": 43, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370241, "dur": 113, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370360, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370469, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370471, "dur": 73, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370548, "dur": 119, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370669, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370721, "dur": 25, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370747, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370765, "dur": 38, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370805, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370807, "dur": 155, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370964, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406370987, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371010, "dur": 17, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371030, "dur": 94, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371127, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371131, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371193, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371195, "dur": 23, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371221, "dur": 179, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371402, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371436, "dur": 38, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371477, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371506, "dur": 15, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371525, "dur": 66, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371595, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371654, "dur": 30, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371687, "dur": 75, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371765, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371793, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371795, "dur": 55, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371853, "dur": 30, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371887, "dur": 55, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371944, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406371979, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372001, "dur": 19, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372022, "dur": 19, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372043, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372118, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372173, "dur": 13, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372188, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372210, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372225, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372289, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372304, "dur": 15, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372321, "dur": 15, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372338, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372372, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372388, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372448, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372464, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372480, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372515, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372534, "dur": 14, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372550, "dur": 15, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372567, "dur": 14, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372583, "dur": 14, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372598, "dur": 14, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372614, "dur": 16, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372632, "dur": 13, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372646, "dur": 57, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372704, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372720, "dur": 14, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372736, "dur": 15, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372752, "dur": 12, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372766, "dur": 59, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372826, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372847, "dur": 14, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372862, "dur": 17, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372883, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372910, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372960, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372976, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406372993, "dur": 14, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373009, "dur": 14, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373024, "dur": 50, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373076, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373093, "dur": 14, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373108, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373110, "dur": 16, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373128, "dur": 87, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373221, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373236, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373256, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373272, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373324, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373356, "dur": 23, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373382, "dur": 14, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373398, "dur": 42, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373441, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373469, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373492, "dur": 47, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373542, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373559, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373575, "dur": 15, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373593, "dur": 70, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373667, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373707, "dur": 25, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373733, "dur": 127, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406373863, "dur": 147, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374014, "dur": 43, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374059, "dur": 52, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374113, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374114, "dur": 23, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374139, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374221, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374260, "dur": 48, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374312, "dur": 15, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374329, "dur": 42, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374374, "dur": 20, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374397, "dur": 206, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374607, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374612, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374690, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374692, "dur": 66, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374762, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374769, "dur": 150, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374923, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374984, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406374987, "dur": 43, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375034, "dur": 43, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375081, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375084, "dur": 148, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375237, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375290, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375293, "dur": 36, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375330, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375332, "dur": 23, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375357, "dur": 141, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375499, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375503, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375576, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375578, "dur": 44, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375625, "dur": 39, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375667, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375707, "dur": 27, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375735, "dur": 20, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375758, "dur": 27, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375787, "dur": 52, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375840, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375876, "dur": 21, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375899, "dur": 28, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375929, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406375954, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376023, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376062, "dur": 44, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376108, "dur": 47, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376157, "dur": 25, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376184, "dur": 33, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376220, "dur": 93, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376314, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376335, "dur": 26, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376362, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376398, "dur": 91, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376490, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376531, "dur": 29, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376562, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376583, "dur": 17, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376601, "dur": 63, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376668, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376701, "dur": 23, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376726, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376749, "dur": 28, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376778, "dur": 16, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376796, "dur": 21, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376819, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376840, "dur": 19, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376860, "dur": 14, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376875, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376892, "dur": 15, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376909, "dur": 63, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376974, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406376993, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377018, "dur": 17, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377037, "dur": 15, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377054, "dur": 14, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377069, "dur": 17, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377088, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377106, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377123, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377185, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377203, "dur": 122, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377335, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377359, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377361, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377393, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377418, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377422, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377470, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377520, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377522, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377582, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377584, "dur": 53, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377640, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377641, "dur": 31, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377675, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377677, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377726, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377728, "dur": 45, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377775, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377777, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377824, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377826, "dur": 56, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377885, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377921, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406377960, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378004, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378005, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378055, "dur": 16, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378073, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378138, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378139, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378200, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378202, "dur": 41, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378244, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378267, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378300, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378351, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378413, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378456, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378458, "dur": 22, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378482, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378508, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378541, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378586, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378614, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378663, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378698, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378722, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378739, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378762, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378789, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378816, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378854, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378855, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406378938, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379016, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379029, "dur": 71, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379107, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379110, "dur": 63, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379177, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379182, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379258, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379262, "dur": 46, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379312, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379315, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379382, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379385, "dur": 65, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379452, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379454, "dur": 41, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379501, "dur": 42, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379545, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379549, "dur": 85, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379637, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379639, "dur": 54, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379697, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379699, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379741, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379744, "dur": 104, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379851, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379854, "dur": 49, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379905, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379907, "dur": 38, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379948, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379976, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406379978, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406380008, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406380011, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406380032, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406380064, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406380097, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406380099, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406380143, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406380145, "dur": 7589, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406387739, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406387778, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406387780, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406387863, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406387910, "dur": 1200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406389114, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406389155, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406389157, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406389233, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406389235, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406389284, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406389287, "dur": 883, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390175, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390202, "dur": 537, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390742, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390748, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390811, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390814, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390861, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390891, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390929, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406390949, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391196, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391200, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391254, "dur": 202, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391460, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391497, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391500, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391522, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391588, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391622, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391650, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391676, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391709, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391710, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391749, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391795, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391804, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406391852, "dur": 381, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392236, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392280, "dur": 120, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392404, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392452, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392455, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392493, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392497, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392547, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392580, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392615, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392633, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392661, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392663, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392701, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392742, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392796, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392838, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392840, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392870, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392872, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392911, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392979, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406392997, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393039, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393075, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393111, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393114, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393145, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393182, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393319, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393353, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393392, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393420, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393441, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393497, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393520, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393590, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393629, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393663, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393665, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393704, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393744, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393773, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393799, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393826, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393851, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393875, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393891, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393909, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393927, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393946, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406393962, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394059, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394079, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394102, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394159, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394182, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394200, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394226, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394254, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394283, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394309, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394368, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394390, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394410, "dur": 293, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394707, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394748, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394794, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394844, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394846, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394874, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394938, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394967, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406394997, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395081, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395083, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395125, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395127, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395162, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395252, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395288, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395290, "dur": 313, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395605, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395623, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395647, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395722, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395742, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395847, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395866, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395890, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395936, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406395940, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396026, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396056, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396131, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396168, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396170, "dur": 248, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396421, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396427, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396459, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396478, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396515, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396547, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396568, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396619, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396641, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396809, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396847, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396849, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396918, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396949, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406396975, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406397000, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406397016, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406397215, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406397232, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406397233, "dur": 59907, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406457161, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406457168, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406457287, "dur": 38, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406457328, "dur": 7445, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406464780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406464783, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406464835, "dur": 405, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465244, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465280, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465308, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465325, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465374, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465389, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465403, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465426, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465453, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465489, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465520, "dur": 398, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406465922, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466044, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466048, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466130, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466136, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466160, "dur": 273, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466439, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466480, "dur": 101, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466590, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466598, "dur": 214, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466815, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466817, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466864, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466866, "dur": 52, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466922, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466963, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406466996, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467026, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467060, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467095, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467097, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467168, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467193, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467218, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467244, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467298, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467301, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467343, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406467345, "dur": 1515, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406468863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406468866, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406468888, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406468916, "dur": 844, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469771, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469823, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469826, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469861, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469893, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469920, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469949, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469971, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469995, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406469997, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470026, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470076, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470119, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470157, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470188, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470276, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470320, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470375, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470378, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470446, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470448, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470492, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470497, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470549, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470550, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470595, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470621, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470651, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946406470675, "dur": 968422, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407439108, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407439115, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407439154, "dur": 27, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407439183, "dur": 7157, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407446344, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407446346, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407446369, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407446370, "dur": 6936, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407453311, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407453355, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407453357, "dur": 65480, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407518849, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407518856, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407518885, "dur": 22, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407518907, "dur": 5480, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407524395, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407524396, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407524435, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407524439, "dur": 1241, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407525684, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407525704, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407525721, "dur": 16815, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407542542, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407542552, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407542609, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407542612, "dur": 701, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407543318, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407543335, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946407543350, "dur": 924341, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408467699, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408467703, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408467726, "dur": 22, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408467749, "dur": 22184, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408489942, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408489945, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408489969, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408489971, "dur": 94734, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408584717, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408584721, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408584786, "dur": 22, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408584809, "dur": 4808, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408589621, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408589623, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408589654, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408589657, "dur": 569, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408590230, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408590260, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408590278, "dur": 20339, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408610630, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408610635, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408610677, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408610680, "dur": 749, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408611437, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408611445, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408611515, "dur": 36, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408611552, "dur": 837, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408612391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408612393, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408612432, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 26516, "tid": 25769803776, "ts": 1753946408612434, "dur": 7011, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 26516, "tid": 1053, "ts": 1753946408620123, "dur": 1175, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 26516, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 26516, "tid": 21474836480, "ts": 1753946406335956, "dur": 35378, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 26516, "tid": 21474836480, "ts": 1753946406371335, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 26516, "tid": 21474836480, "ts": 1753946406371336, "dur": 59, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 26516, "tid": 1053, "ts": 1753946408621299, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 26516, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 26516, "tid": 17179869184, "ts": 1753946406331302, "dur": 2288201, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 26516, "tid": 17179869184, "ts": 1753946406331470, "dur": 3996, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 26516, "tid": 17179869184, "ts": 1753946408619508, "dur": 64, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 26516, "tid": 17179869184, "ts": 1753946408619522, "dur": 18, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 26516, "tid": 17179869184, "ts": 1753946408619574, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 26516, "tid": 1053, "ts": 1753946408621307, "dur": 29, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753946406351013, "dur": 2019, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946406353045, "dur": 808, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946406353962, "dur": 78, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753946406354040, "dur": 963, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946406355085, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406355176, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5A88EE17DDEDEAD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406355270, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406355382, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406355484, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406355609, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_C5B475C4637F1A14.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406355775, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_05E5825DA2C11E52.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406356135, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406356251, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C0425020FB5520E6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406356497, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_BB555152ED99EC31.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406357113, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_B2C7FF93FB6B2588.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406357208, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406358069, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406358673, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406358850, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406359113, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753946406360514, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406360693, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753946406361981, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753946406362034, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753946406362113, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753946406362520, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753946406362969, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753946406363149, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753946406363218, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753946406363318, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753946406364142, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753946406364894, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753946406366043, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753946406366184, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753946406366266, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753946406366425, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406366703, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753946406366801, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753946406367305, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753946406367535, "dur": 485, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406368653, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753946406369298, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753946406370393, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753946406370633, "dur": 116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753946406370977, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753946406371214, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753946406371994, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753946406374716, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753946406375120, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753946406375770, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753946406376367, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753946406377052, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753946406355034, "dur": 23012, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946406378056, "dur": 2234279, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946408612336, "dur": 96, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946408612433, "dur": 75, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946408612508, "dur": 56, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946408612565, "dur": 358, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946408613227, "dur": 849, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753946406354973, "dur": 23103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406378319, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406378401, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2A9B2B462225EF36.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753946406378527, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_9A5F8A3D2041CCD4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753946406378687, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406379030, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_11590DABE376A203.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753946406379487, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753946406379905, "dur": 332, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753946406380290, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753946406380378, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753946406380510, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753946406380616, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753946406380711, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753946406380812, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406380916, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406381768, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406382457, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406383745, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406384608, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\ShadingRateWindow.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753946406384579, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406385819, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406387160, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406388264, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406389034, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406389627, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406390433, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406391037, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406391686, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406392140, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753946406392473, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753946406393352, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406393622, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406393683, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406393903, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406394126, "dur": 1788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406395914, "dur": 65448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406461389, "dur": 5404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753946406466796, "dur": 1008, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406467815, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406467912, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406468066, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406468143, "dur": 2785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753946406471004, "dur": 2141327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406354969, "dur": 23096, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406378074, "dur": 3219, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406381294, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406381945, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406382811, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406383737, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406384649, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\Overrides\\ChromaticAberration.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753946406384617, "dur": 1431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406386049, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406386651, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406387250, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406388440, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406389859, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406390327, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406390839, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406390893, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406391692, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406392134, "dur": 1128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406393289, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406393670, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406393931, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406394105, "dur": 1804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406395909, "dur": 65487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406461398, "dur": 6605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753946406468004, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406468148, "dur": 2809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753946406470957, "dur": 2141376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406355043, "dur": 23069, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406378131, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_620467912CAF740A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753946406378315, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_039E8C14B531BC6E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753946406378372, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406378583, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406378854, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_EB1CC9E829EACBF7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753946406379082, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753946406379292, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_440D2CBC9242B582.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753946406379526, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753946406379713, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753946406380071, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753946406380255, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753946406380731, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753946406380800, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406381555, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406382274, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406383025, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406384603, "dur": 590, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Geometry\\BitangentVectorNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753946406383917, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406385264, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406385764, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406386335, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406386973, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406388287, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406389174, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406389740, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406390706, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406391000, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406391672, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406392126, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753946406392557, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753946406393501, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406393679, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753946406393888, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753946406395037, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406395938, "dur": 65444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406461396, "dur": 5072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753946406466469, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753946406466763, "dur": 4413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753946406471276, "dur": 2141257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406355985, "dur": 22489, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406378536, "dur": 383, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_6E149857A17FA9D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753946406378973, "dur": 494, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753946406379475, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753946406379828, "dur": 582, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753946406380436, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753946406380504, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753946406380648, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753946406380727, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406380806, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406381400, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406381859, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406382489, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406383726, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406384606, "dur": 773, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\PositionMaterialSlot.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753946406384501, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406385914, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406386939, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406387803, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406388561, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406389433, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406390369, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406390949, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406391681, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406392144, "dur": 1128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406393273, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406393617, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406393667, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406393957, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406394078, "dur": 1803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406395919, "dur": 65419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753946406461341, "dur": 4676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753946406466077, "dur": 5342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753946406471488, "dur": 2140936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406355337, "dur": 22920, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406378314, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406378555, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4F63EBA0273774B5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753946406378698, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3AA4F4B70AA81DBB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753946406378858, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406378981, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_17FD707CCC20433D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753946406379313, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753946406379418, "dur": 1132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753946406380605, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753946406380770, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406381470, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406382391, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406383048, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406384511, "dur": 794, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Procedural\\Shape\\EllipseNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753946406383671, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406385779, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406386447, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406387999, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406388678, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406389543, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406390214, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406390854, "dur": 790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406391644, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406392123, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753946406392422, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753946406393766, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406393893, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406394036, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753946406394160, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753946406394797, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753946406394898, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753946406395473, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406395536, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406395620, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406395896, "dur": 65414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406461316, "dur": 6649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753946406468040, "dur": 2698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406470787, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753946406471423, "dur": 2140986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406355086, "dur": 23049, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406378152, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_768496900A2B4AB2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406378268, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_66FDAEB71878BAE6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406378456, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406378549, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_22FED6107B880710.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406378877, "dur": 692, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CB3F943E5EEBF8C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406379589, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406379808, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406379938, "dur": 9900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753946406389839, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406389970, "dur": 995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406391014, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753946406391697, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406391774, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753946406392133, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406392427, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406392484, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753946406393502, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406393565, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406393679, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406393886, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753946406394800, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406395081, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753946406395239, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753946406395883, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1753946406396448, "dur": 63, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406396968, "dur": 60966, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1753946406461356, "dur": 4856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753946406466266, "dur": 4241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753946406470509, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406470612, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753946406471285, "dur": 2141202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406355891, "dur": 22556, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406378511, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6C5D1F615BBD2592.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753946406378725, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406378862, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_492CDE3577A08341.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753946406378980, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_AF431D755E5610CB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753946406379132, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753946406379471, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753946406379805, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753946406379910, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753946406380270, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753946406380619, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753946406380809, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406381556, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406382069, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406382865, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406383599, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406384431, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406385272, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406385797, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406386300, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406386938, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406387826, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406388976, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406389665, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406390731, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406390964, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406391655, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406392121, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753946406392582, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753946406393502, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406393665, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406393967, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406394071, "dur": 1812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406395884, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406396767, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753946406396896, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753946406397263, "dur": 64232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406461497, "dur": 3940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753946406465439, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406465565, "dur": 5151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753946406470717, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406470818, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753946406471524, "dur": 2140866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406355204, "dur": 22998, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406378217, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_585EB113211825F1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753946406378279, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406378456, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D58863CB052B2BAB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753946406378673, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_41F9674DB235B210.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753946406378749, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_448441F242111B54.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753946406378989, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753946406379148, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753946406379329, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753946406379430, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753946406379685, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753946406379821, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753946406380132, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753946406380254, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753946406380539, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753946406380650, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753946406380818, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406380887, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406381770, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406382527, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406383187, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406384382, "dur": 824, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\CodeFunctionNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753946406383975, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406385329, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406386257, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406387228, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406388331, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406389426, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406390165, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406390474, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406390876, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406391644, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406392105, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753946406392503, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753946406393267, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406393659, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406393873, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406393940, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406394093, "dur": 1810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406395904, "dur": 65399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406461305, "dur": 4944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753946406466294, "dur": 4383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753946406470747, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753946406471331, "dur": 2141132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406355114, "dur": 23042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406378318, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753946406378453, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D69C1FCC0966AB3C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753946406378586, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_96EF797E350252A7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753946406378707, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_43DF2AF909B52315.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753946406378799, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_8D5FF0065E53D73E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753946406378959, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753946406379036, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406379233, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753946406379430, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753946406379620, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753946406379820, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753946406380061, "dur": 634, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753946406380697, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753946406380823, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406380877, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753946406380933, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406382643, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406383647, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406384516, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406385621, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406386409, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406387417, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406388342, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406389098, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406389972, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406390393, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\DropdownOptionListDrawer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753946406390062, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406391041, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406391680, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406392100, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753946406392229, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406392290, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753946406393097, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406393275, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406393655, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406393875, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406394045, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406395087, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753946406395217, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753946406395675, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406395913, "dur": 65408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406461358, "dur": 6650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753946406468080, "dur": 2756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946406470836, "dur": 970588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946407441454, "dur": 10343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753946407441431, "dur": 11703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753946407453984, "dur": 141, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753946407454160, "dur": 65492, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753946407525094, "dur": 18237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753946407525088, "dur": 18245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753946407543351, "dur": 804, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753946407544157, "dur": 1068219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406355187, "dur": 22990, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406378195, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_A8FE29C950FB2CD8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753946406378316, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406378414, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D3BC0CCBE305A751.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753946406378548, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406378695, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753946406378865, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_345967DEBA052E01.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753946406379096, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406379211, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406379413, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753946406379626, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753946406379752, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406379992, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406380187, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406380259, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406380449, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406380526, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3961525668064847622.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406380598, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753946406380715, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406380778, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406381793, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406382322, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406383448, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406384570, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\BitangentMaterialSlot.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753946406384525, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406385675, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406386544, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406387209, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406388406, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406389347, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406390043, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406390763, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406390985, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406391667, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406392141, "dur": 1138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406393279, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406393680, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406393912, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406394119, "dur": 1823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406395942, "dur": 65425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406461371, "dur": 4673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753946406466085, "dur": 4654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753946406470740, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946406470868, "dur": 2119488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753946408590398, "dur": 20963, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753946408590362, "dur": 21001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753946408611387, "dur": 865, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753946406355266, "dur": 22964, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406378320, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406378428, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_5D931719A082E416.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753946406378539, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_43A3C34EEA8A405D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753946406378667, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406378876, "dur": 485, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_192890A86BC12540.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753946406379415, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753946406379587, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753946406379855, "dur": 8594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753946406388450, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406388560, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753946406388701, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406389637, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406390570, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406390979, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406391668, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406392124, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753946406392567, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406392666, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753946406393310, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406393662, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406393978, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406394053, "dur": 1832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406395885, "dur": 1792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406397679, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753946406397851, "dur": 63466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753946406461322, "dur": 4974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753946406466337, "dur": 4909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753946406471312, "dur": 2141140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406355309, "dur": 22931, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406378321, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1FEDA6F975B339C9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753946406378421, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406378575, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406378716, "dur": 350, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E419BB505CB124D6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753946406379126, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753946406379268, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753946406379481, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753946406379819, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753946406380079, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753946406380257, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753946406380671, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753946406380858, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406382248, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406382890, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406383509, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406384632, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\ReflectionProbeManager.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753946406384602, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406385917, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406386287, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406387093, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406388036, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406388841, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406389615, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406390383, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406390993, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406391662, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406392132, "dur": 1131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406393291, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406393678, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406393919, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406394112, "dur": 1816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406395928, "dur": 65419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406461383, "dur": 6252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753946406467636, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406467786, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406467918, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406468066, "dur": 2742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946406470847, "dur": 1054247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753946407525124, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1753946407525099, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1753946407525226, "dur": 1324, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1753946407526553, "dur": 1085808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406355370, "dur": 22903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406378325, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406378462, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_A5A0099F491A8369.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753946406378531, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753946406378681, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_DC1ACAFAC10F1EC5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753946406379027, "dur": 477, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1753946406379506, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753946406379699, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1753946406379905, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753946406380159, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753946406380324, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753946406380466, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1753946406380603, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753946406380761, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753946406380876, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753946406380958, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406381914, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406382250, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406383029, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406384655, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Utility\\Logic\\NandNode.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753946406383652, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406385286, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406385821, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406386843, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406387409, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406388357, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406389266, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406390120, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406390676, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406390758, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406390953, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406391653, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406392116, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753946406392337, "dur": 853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753946406393294, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406393684, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406393894, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406394038, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406394522, "dur": 1419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406395941, "dur": 65409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406461352, "dur": 5976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753946406467331, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406467428, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406467505, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406467706, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1753946406467824, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406468004, "dur": 2598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406470607, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406470684, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753946406471255, "dur": 2141329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406355415, "dur": 22882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406378310, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_2A46E7CF2C2E485A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753946406378491, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_3E69732DB14D8F64.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753946406378687, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406378884, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753946406379024, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1753946406379452, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1753946406379757, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753946406380140, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1753946406380247, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753946406380395, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753946406380484, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753946406380612, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753946406380683, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753946406380786, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753946406380907, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406381631, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406382319, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406382904, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406384607, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Math\\Vector\\ProjectionNode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753946406383721, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406385332, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406386432, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406387717, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406388972, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406389822, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406390379, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406390840, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406390928, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406391642, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406392103, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753946406392448, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406392507, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753946406393391, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406393464, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406393616, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406393667, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406393943, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406394086, "dur": 1791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406395920, "dur": 65485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406461409, "dur": 5360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753946406466771, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753946406466974, "dur": 4172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753946406471268, "dur": 2141386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406355467, "dur": 22842, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406378321, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_9AE2C3D45DD784F7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753946406378509, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CC2D34224542963B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753946406378782, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_E7B16A2A57D5AA9C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753946406378972, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753946406379221, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753946406379677, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1753946406379814, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753946406380068, "dur": 495, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753946406380564, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753946406380695, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406380759, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753946406380867, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406381633, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406382816, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406384640, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Colors\\NoColors.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753946406383531, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406385152, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406385918, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406386558, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406387134, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406388248, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406389087, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406390568, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406390645, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406390978, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406391661, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406392138, "dur": 1127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406393265, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406393652, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406393874, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406394038, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946406394516, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753946406394695, "dur": 1226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753946406395982, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753946406396081, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753946406396756, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753946406396860, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753946406397311, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753946406397399, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753946406397673, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753946406397807, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753946406398086, "dur": 1041811, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753946407441733, "dur": 4911, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753946407441420, "dur": 5299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753946407447059, "dur": 90, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946407447169, "dur": 1021329, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753946408469764, "dur": 18641, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753946408469757, "dur": 19967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753946408490538, "dur": 169, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753946408490723, "dur": 94791, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753946408590351, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753946408590346, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753946408590437, "dur": 642, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753946408591082, "dur": 21263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406355530, "dur": 22805, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406378351, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_9CC23DEF77915439.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753946406378494, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_42A7D495B479CB5B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753946406378551, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406378694, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406378870, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_95DEE68278F6B037.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753946406379135, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1753946406379192, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753946406379461, "dur": 508, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1753946406380030, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753946406380245, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753946406380329, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753946406380646, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753946406380823, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406381948, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406382696, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406383267, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406384055, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406384615, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\Passes\\DepthOnlyPass.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753946406384615, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406385764, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406386291, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406386849, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406387670, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406388840, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406389604, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406390177, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406390625, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406390832, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406390963, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406391675, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406392096, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753946406392481, "dur": 1448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753946406394040, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753946406394178, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753946406395027, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753946406395130, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753946406396586, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753946406396695, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753946406397380, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753946406397460, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753946406397763, "dur": 63650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406461416, "dur": 4742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753946406466159, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753946406466249, "dur": 4909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753946406471231, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753946406471282, "dur": 2141230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406355579, "dur": 22775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406378362, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753946406378437, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6BE2768E427A2B05.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753946406378575, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406378874, "dur": 352, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1753946406379366, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406379622, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406379676, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406379765, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406379903, "dur": 459, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406380363, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406380456, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406380534, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406380653, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406380735, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753946406380846, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406381978, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\Modes\\TimelineInactiveMode.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753946406381532, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406382493, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406383126, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406384660, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Matrix\\Matrix2Node.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753946406383863, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406385859, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406386302, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406387106, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406388666, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406389520, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406390309, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406391100, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406391697, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406392142, "dur": 1124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406393266, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406393664, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406393972, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406394062, "dur": 1838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406395901, "dur": 65399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406461303, "dur": 5094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753946406466398, "dur": 772, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406467295, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406467421, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406467682, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1753946406467850, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753946406467920, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406468155, "dur": 2952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753946406471155, "dur": 2141468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406355624, "dur": 22739, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406378374, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753946406378460, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6C1D6771D6465A3E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753946406378570, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406378715, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406378858, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753946406378958, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753946406379232, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1753946406379486, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Internal.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753946406379605, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753946406379807, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1753946406379913, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1753946406380216, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753946406380322, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753946406380430, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753946406380825, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406381736, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406382734, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406384664, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Input\\Scene\\ObjectNode.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753946406383801, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406385414, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406386339, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406387246, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406388195, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406388790, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406389737, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406390508, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406391022, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406391674, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406392134, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753946406392424, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406392487, "dur": 1690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753946406394178, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406394275, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753946406394427, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753946406394931, "dur": 1003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406395934, "dur": 65458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406461413, "dur": 4707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753946406466172, "dur": 4997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753946406471171, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753946406471273, "dur": 2141292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406355680, "dur": 22698, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406378452, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B4D1B567B4047FDB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753946406378553, "dur": 480, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_19C33F5BD50A5793.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753946406379035, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753946406379154, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753946406379227, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753946406379485, "dur": 468, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753946406379955, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753946406380125, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753946406380290, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753946406380424, "dur": 461, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753946406380886, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406381686, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406382249, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Editor\\UniversalRendererDataEditor.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753946406382249, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406384658, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Nodes\\Procedural\\Shape\\ZigZagNode.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753946406383663, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406385214, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406386027, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406386761, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406387638, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406388684, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406390069, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406390464, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406391033, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406391677, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406392118, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753946406392445, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406392511, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753946406393647, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753946406393821, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753946406394524, "dur": 1398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406395923, "dur": 65434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406461360, "dur": 4814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753946406466176, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406466272, "dur": 5053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753946406471326, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753946406471406, "dur": 2141033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406355731, "dur": 22662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406378403, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17CAD97A34817613.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753946406378498, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_17CAD97A34817613.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753946406378650, "dur": 479, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DDF3F06D7B4D04E8.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753946406379218, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753946406379326, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753946406379551, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753946406379681, "dur": 666, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753946406380441, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753946406380493, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753946406380719, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753946406380844, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406380904, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406381899, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406383484, "dur": 610, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\ViewModels\\ShaderInputViewModel.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753946406384575, "dur": 696, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Drawing\\Manipulators\\ElementResizer.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753946406383346, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406385304, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406386095, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406386857, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406387586, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406388774, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406389854, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406390367, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406390978, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406391657, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406392129, "dur": 1135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406393264, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406393650, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753946406393786, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406393894, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753946406394605, "dur": 1322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406395927, "dur": 65403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406461332, "dur": 6111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753946406467444, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406467702, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1753946406467922, "dur": 1778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406469749, "dur": 1472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753946406471269, "dur": 2141283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406355780, "dur": 22627, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406378536, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_98A5136B14FEE1EF.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753946406378658, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2F015ABCE956D166.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753946406379072, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1753946406379478, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753946406379584, "dur": 723, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753946406380308, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753946406380542, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753946406380706, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1753946406380799, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406381831, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406383231, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406384001, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406384615, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.1.0\\Editor\\Data\\Graphs\\Texture2DInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 21, "ts": 1753946406384498, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406386072, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406386778, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406387618, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406388773, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406390152, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406390291, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406391000, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406391671, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406392110, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753946406392233, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406392300, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753946406393297, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406393406, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753946406393680, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753946406394453, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406394774, "dur": 1158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406395932, "dur": 67721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406463657, "dur": 5884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753946406469545, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406469730, "dur": 1483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753946406471253, "dur": 2141356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406355828, "dur": 22601, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406378442, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_3A3E2B62EBD5E224.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753946406378530, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406378655, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1FF073C74B369FAA.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753946406378767, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753946406378920, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EA6B0B321175186.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753946406379489, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753946406379763, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753946406379912, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1753946406380287, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753946406380377, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1753946406380643, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753946406380803, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406381684, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406382223, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406382811, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406383430, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406384169, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406385264, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406386050, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406387674, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406388471, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406389406, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406390058, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406390654, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406390999, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406391682, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406392146, "dur": 1122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406393269, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406393652, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406393879, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406394037, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753946406394164, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753946406394746, "dur": 1185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406395931, "dur": 65468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406461403, "dur": 4478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753946406465882, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406466138, "dur": 4868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753946406471008, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753946406471117, "dur": 2141210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406355027, "dur": 23066, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406378301, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406378371, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_F5A419B9F1F8246D.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753946406378506, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406378565, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9AD432D09FAC516B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753946406378685, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753946406378868, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_DA78ADA0769E5832.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753946406379026, "dur": 805, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_92F25047DCB9DD87.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753946406379905, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753946406380061, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1753946406380253, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753946406380345, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753946406380433, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753946406380586, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753946406380746, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753946406380868, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406381933, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406382523, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406383069, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406384429, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406385706, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406386805, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406388037, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406389216, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406390163, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406390290, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406391114, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406391649, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406392092, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753946406392296, "dur": 1104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753946406393468, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753946406393721, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753946406394359, "dur": 1558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406395917, "dur": 69572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406465491, "dur": 5348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753946406470840, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753946406470970, "dur": 2141359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406355940, "dur": 22520, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406378598, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1983ABD9EB6C4B5C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753946406379071, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753946406379235, "dur": 428, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1753946406379786, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753946406380058, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1753946406380264, "dur": 408, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753946406380676, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1753946406380814, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406380955, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406381648, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406382367, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406383276, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406384592, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.1.0\\Runtime\\UniversalRenderPipelineGlobalSettings.ShaderVariantSettings.cs"}}, {"pid": 12345, "tid": 24, "ts": 1753946406384548, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406385927, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406386720, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406387616, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406388485, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406389326, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406390180, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406390563, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406390865, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406391647, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406392094, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753946406392328, "dur": 2079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753946406394408, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406394509, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753946406394641, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753946406395794, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753946406395930, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753946406396474, "dur": 64902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406461394, "dur": 4756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753946406466151, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753946406466215, "dur": 4677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753946406471010, "dur": 2141627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753946408618078, "dur": 1595, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 26516, "tid": 1053, "ts": 1753946408621413, "dur": 1393, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 26516, "tid": 1053, "ts": 1753946408622850, "dur": 5343, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 26516, "tid": 1053, "ts": 1753946408620097, "dur": 8132, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}