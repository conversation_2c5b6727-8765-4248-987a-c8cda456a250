{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "b3854e38d9ab48cbb965e85474839a36",
    "m_Properties": [
        {
            "m_Id": "4345b023a03b4df88df32b299229f803"
        },
        {
            "m_Id": "fb775396eaac48cb9a6ad131a6ef99b2"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [
        {
            "m_Id": "e7466b6254594c4ebbab4664c382dbdc"
        }
    ],
    "m_CategoryData": [
        {
            "m_Id": "6097bb4bb3734026843d70825c4855a0"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "a9f1655c8d2c44a3b5add35d592aab34"
        },
        {
            "m_Id": "c7c016255ad440b9814558d66aa288b5"
        },
        {
            "m_Id": "16155c0c493f4df295db19f55dbc3e3d"
        },
        {
            "m_Id": "ecf5b1cd23ed49508ae27c5a6fb739ef"
        },
        {
            "m_Id": "32ae109b7aab4358bfdff907157aa031"
        },
        {
            "m_Id": "47dd3928bb7142de911c3fb7fde8d672"
        },
        {
            "m_Id": "3c61ddc4a3e64b9eb9c50ccad0c4e6cd"
        },
        {
            "m_Id": "6a2506713a284c03ad09779864c61e6e"
        },
        {
            "m_Id": "f77ee4f5c1574703afff716ef6913716"
        },
        {
            "m_Id": "c3fa19c135b3486f92a2011351e09595"
        },
        {
            "m_Id": "2dedacae3e934d5daea7724449858e28"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "fbfcaaabf35a4ca9a58849eb99baa457"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "7560fb230db442bc9f529ee13b1a09e5"
        },
        {
            "m_Id": "60e97c4c00a54156afad61b4663f2f1d"
        },
        {
            "m_Id": "912671776ae045ef9f030d4376802305"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "16155c0c493f4df295db19f55dbc3e3d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "32ae109b7aab4358bfdff907157aa031"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "16155c0c493f4df295db19f55dbc3e3d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "6a2506713a284c03ad09779864c61e6e"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "16155c0c493f4df295db19f55dbc3e3d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f77ee4f5c1574703afff716ef6913716"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2dedacae3e934d5daea7724449858e28"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3c61ddc4a3e64b9eb9c50ccad0c4e6cd"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "32ae109b7aab4358bfdff907157aa031"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3c61ddc4a3e64b9eb9c50ccad0c4e6cd"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3c61ddc4a3e64b9eb9c50ccad0c4e6cd"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a9f1655c8d2c44a3b5add35d592aab34"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "47dd3928bb7142de911c3fb7fde8d672"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "32ae109b7aab4358bfdff907157aa031"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "47dd3928bb7142de911c3fb7fde8d672"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "6a2506713a284c03ad09779864c61e6e"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6a2506713a284c03ad09779864c61e6e"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c3fa19c135b3486f92a2011351e09595"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c3fa19c135b3486f92a2011351e09595"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2dedacae3e934d5daea7724449858e28"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c7c016255ad440b9814558d66aa288b5"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "47dd3928bb7142de911c3fb7fde8d672"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ecf5b1cd23ed49508ae27c5a6fb739ef"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "32ae109b7aab4358bfdff907157aa031"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ecf5b1cd23ed49508ae27c5a6fb739ef"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f77ee4f5c1574703afff716ef6913716"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f77ee4f5c1574703afff716ef6913716"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c3fa19c135b3486f92a2011351e09595"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Artistic/Mask",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "a9f1655c8d2c44a3b5add35d592aab34"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "0543e4e5c4d84bedb2d8e999a3cf30cc",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "16155c0c493f4df295db19f55dbc3e3d",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1212.0001220703125,
            "y": 170.5,
            "width": 122.5,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "46ea88ecb1f245f4b34b879348f5c4c8"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "4345b023a03b4df88df32b299229f803"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "265a4944577e454da50a178b29de57dc",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "2dedacae3e934d5daea7724449858e28",
    "m_Group": {
        "m_Id": "fbfcaaabf35a4ca9a58849eb99baa457"
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -680.0000610351563,
            "y": 15.000005722045899,
            "width": 127.99993896484375,
            "height": 93.9999771118164
        }
    },
    "m_Slots": [
        {
            "m_Id": "4d62a0b8e1654f668b4f51a7c5e69fbe"
        },
        {
            "m_Id": "265a4944577e454da50a178b29de57dc"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SmoothstepNode",
    "m_ObjectId": "32ae109b7aab4358bfdff907157aa031",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Smoothstep",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -832.0000610351563,
            "y": 290.0,
            "width": 152.0,
            "height": 142.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "3bf2df4fb4374f9ca7a5056b7abc4f9a"
        },
        {
            "m_Id": "c840c462052c463fbf1012153a44502b"
        },
        {
            "m_Id": "c432141888b04c97a03d32366fd6a024"
        },
        {
            "m_Id": "47ebfceb2d14429e9833b220a04c2bb5"
        }
    ],
    "synonyms": [
        "curve"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "3bf2df4fb4374f9ca7a5056b7abc4f9a",
    "m_Id": 0,
    "m_DisplayName": "Edge1",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Edge1",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DropdownNode",
    "m_ObjectId": "3c61ddc4a3e64b9eb9c50ccad0c4e6cd",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Falloff Type",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -526.0,
            "y": 160.00003051757813,
            "width": 183.3333740234375,
            "height": 120.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "484d576d955f414eb3df4e32dbb00bf4"
        },
        {
            "m_Id": "e76d879b0f5145429bdf61f6855a4c9c"
        },
        {
            "m_Id": "cb18bb0c489e49f6876e5d0cd0af8561"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Dropdown": {
        "m_Id": "e7466b6254594c4ebbab4664c382dbdc"
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "4345b023a03b4df88df32b299229f803",
    "m_Guid": {
        "m_GuidSerialized": "ba5b0a47-02c6-4548-965d-cd08775a1901"
    },
    "m_Name": "Minimum",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Minimum",
    "m_DefaultReferenceName": "_Minimum",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": -0.5,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "466a684d351e446aa07bc6bd041ffa93",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "46ea88ecb1f245f4b34b879348f5c4c8",
    "m_Id": 0,
    "m_DisplayName": "Minimum",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.SwizzleNode",
    "m_ObjectId": "47dd3928bb7142de911c3fb7fde8d672",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Swizzle",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1154.0,
            "y": 337.5,
            "width": 129.5,
            "height": 121.50003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "5b77b32e78254407aa827b6e06320a08"
        },
        {
            "m_Id": "8fc97903f0bb431fab8e1c2dd222d6f6"
        }
    ],
    "synonyms": [
        "swap",
        "reorder",
        "component mask"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "_maskInput": "y",
    "convertedMask": "y"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "47ebfceb2d14429e9833b220a04c2bb5",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "484d576d955f414eb3df4e32dbb00bf4",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4d62a0b8e1654f668b4f51a7c5e69fbe",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5054bf0e49774853b6a1137366a44438",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5b77b32e78254407aa827b6e06320a08",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "6097bb4bb3734026843d70825c4855a0",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "4345b023a03b4df88df32b299229f803"
        },
        {
            "m_Id": "fb775396eaac48cb9a6ad131a6ef99b2"
        },
        {
            "m_Id": "e7466b6254594c4ebbab4664c382dbdc"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "60e97c4c00a54156afad61b4663f2f1d",
    "m_Title": "Altitude Mask",
    "m_Content": "The Altitude Mask is black below the minimum altitude, transitions from black to white between the minimum and maximum altitudes, and then stays white above the maximum altitude.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1468.0,
        "y": -60.0,
        "width": 200.0,
        "height": 160.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubtractNode",
    "m_ObjectId": "6a2506713a284c03ad09779864c61e6e",
    "m_Group": {
        "m_Id": "fbfcaaabf35a4ca9a58849eb99baa457"
    },
    "m_Name": "Subtract",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1000.0001220703125,
            "y": -14.000015258789063,
            "width": 126.0,
            "height": 118.0000228881836
        }
    },
    "m_Slots": [
        {
            "m_Id": "5054bf0e49774853b6a1137366a44438"
        },
        {
            "m_Id": "f1a3796bfd654d04a2199aca7109198a"
        },
        {
            "m_Id": "d54b906118ef43e3ac18206791a6185b"
        }
    ],
    "synonyms": [
        "subtraction",
        "remove",
        "minus",
        "take away"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "7560fb230db442bc9f529ee13b1a09e5",
    "m_Title": "Linear",
    "m_Content": "(Pos.y - Min)/(Max - Min)",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -831.0,
        "y": 151.0,
        "width": 263.0,
        "height": 52.0
    },
    "m_Group": {
        "m_Id": "fbfcaaabf35a4ca9a58849eb99baa457"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "8fc97903f0bb431fab8e1c2dd222d6f6",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "912671776ae045ef9f030d4376802305",
    "m_Title": "Falloff options",
    "m_Content": " Linear will make the mask a direct line from minimum to maximum, while Smoothstep will create smooth transitions using a more S shaped curve.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -524.0000610351563,
        "y": 289.0000305175781,
        "width": 200.0,
        "height": 115.76373291015625
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "a9f1655c8d2c44a3b5add35d592aab34",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -342.5,
            "y": 162.00001525878907,
            "width": 85.5,
            "height": 77.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "bb892b340d4e4d49848bdae488415637"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ab37cf37d0904142be1a525bdee47de9",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "bb892b340d4e4d49848bdae488415637",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "bef9787278924b80ab6baeb4278ee1a3",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "befb91d0dd2047f9ab32bf424de8663b",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "c3fa19c135b3486f92a2011351e09595",
    "m_Group": {
        "m_Id": "fbfcaaabf35a4ca9a58849eb99baa457"
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -832.0000610351563,
            "y": 26.0000057220459,
            "width": 125.99993896484375,
            "height": 117.9999771118164
        }
    },
    "m_Slots": [
        {
            "m_Id": "0543e4e5c4d84bedb2d8e999a3cf30cc"
        },
        {
            "m_Id": "e303d97ca9424fccb11483158ac2547a"
        },
        {
            "m_Id": "befb91d0dd2047f9ab32bf424de8663b"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c432141888b04c97a03d32366fd6a024",
    "m_Id": 2,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.PositionNode",
    "m_ObjectId": "c7c016255ad440b9814558d66aa288b5",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1361.5,
            "y": 337.5,
            "width": 206.0,
            "height": 130.50003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "e6255b50aa4743f3b3c076908da972b4"
        }
    ],
    "synonyms": [],
    "m_Precision": 1,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 4,
    "m_PositionSource": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c840c462052c463fbf1012153a44502b",
    "m_Id": 1,
    "m_DisplayName": "Edge2",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Edge2",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c968a96eb4d548d2bbd735ff5810e8cc",
    "m_Id": 0,
    "m_DisplayName": "Maximum",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "cb18bb0c489e49f6876e5d0cd0af8561",
    "m_Id": 3,
    "m_DisplayName": "Smoothstep",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smoothstep",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d54b906118ef43e3ac18206791a6185b",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e303d97ca9424fccb11483158ac2547a",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 2.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e6255b50aa4743f3b3c076908da972b4",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ShaderDropdown",
    "m_ObjectId": "e7466b6254594c4ebbab4664c382dbdc",
    "m_Guid": {
        "m_GuidSerialized": "1d4f9513-a220-4684-ae92-f0134e787040"
    },
    "m_Name": "Falloff Type",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Falloff Type",
    "m_DefaultReferenceName": "_Falloff_Type",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Entries": [
        {
            "id": 1,
            "displayName": "Linear"
        },
        {
            "id": 3,
            "displayName": "Smoothstep"
        }
    ],
    "m_Value": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e76d879b0f5145429bdf61f6855a4c9c",
    "m_Id": 1,
    "m_DisplayName": "Linear",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Linear",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "ecf5b1cd23ed49508ae27c5a6fb739ef",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1215.0,
            "y": 224.0,
            "width": 125.5,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c968a96eb4d548d2bbd735ff5810e8cc"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "fb775396eaac48cb9a6ad131a6ef99b2"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f1a3796bfd654d04a2199aca7109198a",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubtractNode",
    "m_ObjectId": "f77ee4f5c1574703afff716ef6913716",
    "m_Group": {
        "m_Id": "fbfcaaabf35a4ca9a58849eb99baa457"
    },
    "m_Name": "Subtract",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1000.0001220703125,
            "y": 106.00000762939453,
            "width": 126.0,
            "height": 117.99999237060547
        }
    },
    "m_Slots": [
        {
            "m_Id": "466a684d351e446aa07bc6bd041ffa93"
        },
        {
            "m_Id": "ab37cf37d0904142be1a525bdee47de9"
        },
        {
            "m_Id": "bef9787278924b80ab6baeb4278ee1a3"
        }
    ],
    "synonyms": [
        "subtraction",
        "remove",
        "minus",
        "take away"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "fb775396eaac48cb9a6ad131a6ef99b2",
    "m_Guid": {
        "m_GuidSerialized": "7aad66b6-5f07-464b-a1d3-725b1e3b3d28"
    },
    "m_Name": "Maximum",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Maximum",
    "m_DefaultReferenceName": "_Maximum",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 1.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "fbfcaaabf35a4ca9a58849eb99baa457",
    "m_Title": "Linear",
    "m_Position": {
        "x": -1025.0001220703125,
        "y": -73.00003051757813
    }
}

