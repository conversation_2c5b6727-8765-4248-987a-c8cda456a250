# 第一人称控制器设置指南

## 快速设置步骤

### 1. 创建测试场景（可选）
1. 在Hierarchy中创建一个空的GameObject，命名为"GroundGenerator"
2. 添加SimpleGroundGenerator脚本
3. 运行游戏，会自动生成地面和墙壁用于测试

### 2. 创建第一人称角色
1. 在Hierarchy中创建一个空的GameObject，命名为"FirstPersonPlayer"
2. 设置位置为 (0, 1, 0)
3. 添加以下组件：
   - Character Controller
   - FirstPersonController脚本
   - CursorManager脚本

### 3. 设置摄像机
1. 在FirstPersonPlayer下创建一个子对象，命名为"PlayerCamera"
2. 将Main Camera拖拽到FirstPersonPlayer下作为子对象，或者删除Main Camera并将PlayerCamera添加Camera组件
3. 设置PlayerCamera的位置为 (0, 1.8, 0) - 模拟眼睛高度

### 4. 配置Character Controller
- Center: (0, 1, 0)
- Radius: 0.5
- Height: 2

### 5. 配置FirstPersonController脚本参数
- Walk Speed: 5 (正常行走速度)
- Run Speed: 10 (按住Shift时的跑步速度)
- Jump Height: 2 (跳跃高度)
- Mouse Sensitivity: 2 (鼠标灵敏度)
- Max Look Angle: 80 (上下视角限制)

### 6. 地面检测设置
脚本会自动创建GroundCheck对象，但你也可以手动设置：
- Ground Distance: 0.4
- Ground Mask: 选择地面图层 (通常是Default或Ground)

### 7. 最终检查
1. 确保FirstPersonPlayer的位置在地面上方
2. 确保PlayerCamera是FirstPersonPlayer的子对象
3. 运行游戏测试所有功能

## 控制说明
- **WASD**: 移动
- **鼠标**: 视角控制
- **空格**: 跳跃
- **左Shift**: 跑步
- **ESC**: 解锁鼠标光标 (需要额外代码)

## 注意事项
1. 确保场景中有地面对象，并且设置了正确的碰撞体
2. 如果需要解锁鼠标，可以按ESC键（需要在脚本中添加相应代码）
3. 可以根据需要调整各种参数来获得最佳的游戏体验

## 可选增强功能
- 添加脚步声音效果
- 添加头部摆动效果
- 添加冲刺功能
- 添加蹲下功能
