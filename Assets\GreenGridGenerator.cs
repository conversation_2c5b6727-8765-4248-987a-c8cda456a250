using UnityEngine;

public class GreenGridGenerator : MonoBehaviour
{
    [Header("Grid Settings")]
    public float gridSize = 200f;
    public int gridResolution = 100;
    public Color grassColor = new Color(0.2f, 0.8f, 0.2f);
    public Color gridLineColor = new Color(0.1f, 0.6f, 0.1f);
    
    [Header("Grid Options")]
    public bool showGridLines = true;
    public float gridLineWidth = 0.02f;
    
    void Start()
    {
        CreateGreenGrid();
    }
    
    void CreateGreenGrid()
    {
        // Create main ground plane
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "GreenGrid";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(gridSize / 10f, 1f, gridSize / 10f);
        
        // Create and apply grid material
        Material gridMaterial = CreateGridMaterial();
        ground.GetComponent<Renderer>().material = gridMaterial;
        
        // Ensure it has proper physics
        if (ground.GetComponent<MeshCollider>() == null)
        {
            ground.AddComponent<MeshCollider>();
        }
    }
    
    Material CreateGridMaterial()
    {
        Material material = new Material(Shader.Find("Standard"));
        
        // Create a simple green texture with grid pattern
        Texture2D gridTexture = CreateGridTexture();
        material.mainTexture = gridTexture;
        
        // Set material properties for grass-like appearance
        material.color = grassColor;
        material.SetFloat("_Metallic", 0f);
        material.SetFloat("_Glossiness", 0.1f);
        
        // Set texture tiling
        material.mainTextureScale = new Vector2(gridResolution, gridResolution);
        
        return material;
    }
    
    Texture2D CreateGridTexture()
    {
        int textureSize = 64;
        Texture2D texture = new Texture2D(textureSize, textureSize);
        
        for (int x = 0; x < textureSize; x++)
        {
            for (int y = 0; y < textureSize; y++)
            {
                Color pixelColor = grassColor;
                
                if (showGridLines)
                {
                    // Create grid lines
                    float lineThickness = gridLineWidth * textureSize;
                    if (x < lineThickness || x > textureSize - lineThickness ||
                        y < lineThickness || y > textureSize - lineThickness)
                    {
                        pixelColor = gridLineColor;
                    }
                }
                
                texture.SetPixel(x, y, pixelColor);
            }
        }
        
        texture.Apply();
        texture.filterMode = FilterMode.Point; // Sharp pixel edges for grid effect
        return texture;
    }
    
    // Optional: Create a simple skybox for better visual effect
    void CreateSimpleSkybox()
    {
        // Set a simple gradient skybox color
        Camera.main.backgroundColor = new Color(0.5f, 0.8f, 1f); // Light blue sky
        Camera.main.clearFlags = CameraClearFlags.SolidColor;
    }
    
    void OnValidate()
    {
        // Update in real-time when values change in inspector
        if (Application.isPlaying)
        {
            // Destroy existing ground and recreate
            GameObject existingGround = GameObject.Find("GreenGrid");
            if (existingGround != null)
            {
                DestroyImmediate(existingGround);
                CreateGreenGrid();
            }
        }
    }
}
