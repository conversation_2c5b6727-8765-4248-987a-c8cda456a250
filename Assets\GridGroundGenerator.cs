using UnityEngine;

public class GridGroundGenerator : MonoBehaviour
{
    [Header("网格地面设置")]
    public float groundSize = 200f;
    public int gridResolution = 20; // 网格密度
    public Color gridColor1 = new Color(0.3f, 0.8f, 0.3f); // 绿色
    public Color gridColor2 = new Color(0.6f, 0.6f, 0.6f); // 灰色
    public float gridLineWidth = 0.05f; // 网格线宽度
    
    void Start()
    {
        CreateGridGround();
    }
    
    void CreateGridGround()
    {
        // 创建地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "GridGround";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(groundSize / 10f, 1f, groundSize / 10f);
        
        // 创建网格材质
        Material gridMaterial = CreateGridMaterial();
        ground.GetComponent<Renderer>().material = gridMaterial;
        
        // 添加碰撞体
        if (ground.GetComponent<MeshCollider>() == null)
        {
            ground.AddComponent<MeshCollider>();
        }
        
        // 设置环境
        SetupEnvironment();
        
        Debug.Log("绿灰相间网格地面创建完成！");
    }
    
    Material CreateGridMaterial()
    {
        // 创建材质
        Material material = new Material(Shader.Find("Standard"));
        if (material.shader == null)
        {
            // 如果Standard着色器不可用，尝试其他着色器
            material = new Material(Shader.Find("Universal Render Pipeline/Lit"));
            if (material.shader == null)
            {
                material = new Material(Shader.Find("Mobile/Diffuse"));
            }
        }
        
        // 创建网格纹理
        Texture2D gridTexture = CreateGridTexture();
        material.mainTexture = gridTexture;
        
        // 设置材质属性
        material.SetFloat("_Metallic", 0f);
        if (material.HasProperty("_Glossiness"))
            material.SetFloat("_Glossiness", 0.1f);
        if (material.HasProperty("_Smoothness"))
            material.SetFloat("_Smoothness", 0.1f);
        
        // 设置纹理平铺
        material.mainTextureScale = new Vector2(gridResolution, gridResolution);
        
        return material;
    }
    
    Texture2D CreateGridTexture()
    {
        int textureSize = 128; // 纹理分辨率
        Texture2D texture = new Texture2D(textureSize, textureSize);
        
        // 计算网格线粗细（像素）
        int lineThickness = Mathf.Max(1, Mathf.RoundToInt(gridLineWidth * textureSize));
        
        for (int x = 0; x < textureSize; x++)
        {
            for (int y = 0; y < textureSize; y++)
            {
                Color pixelColor;
                
                // 判断是否在网格线上
                bool isOnGridLine = (x < lineThickness || x >= textureSize - lineThickness ||
                                   y < lineThickness || y >= textureSize - lineThickness);
                
                if (isOnGridLine)
                {
                    // 网格线使用深色
                    pixelColor = Color.Lerp(gridColor1, Color.black, 0.3f);
                }
                else
                {
                    // 棋盘格效果：根据位置决定颜色
                    int gridX = (x - lineThickness) / ((textureSize - 2 * lineThickness) / 2);
                    int gridY = (y - lineThickness) / ((textureSize - 2 * lineThickness) / 2);
                    
                    bool useColor1 = (gridX + gridY) % 2 == 0;
                    pixelColor = useColor1 ? gridColor1 : gridColor2;
                }
                
                texture.SetPixel(x, y, pixelColor);
            }
        }
        
        texture.Apply();
        texture.filterMode = FilterMode.Point; // 保持像素清晰
        texture.wrapMode = TextureWrapMode.Repeat;
        
        return texture;
    }
    
    void SetupEnvironment()
    {
        // 设置天空颜色
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            mainCamera.backgroundColor = new Color(0.5f, 0.8f, 1f);
            mainCamera.clearFlags = CameraClearFlags.SolidColor;
        }
        
        // 设置光照
        Light directionalLight = FindObjectOfType<Light>();
        if (directionalLight != null)
        {
            directionalLight.color = Color.white;
            directionalLight.intensity = 1f;
        }
    }
    
    // 在Inspector中实时更新
    void OnValidate()
    {
        if (Application.isPlaying)
        {
            // 查找现有地面并更新
            GameObject existingGround = GameObject.Find("GridGround");
            if (existingGround != null)
            {
                Material newMaterial = CreateGridMaterial();
                existingGround.GetComponent<Renderer>().material = newMaterial;
            }
        }
    }
    
    // 手动重新创建
    [ContextMenu("重新创建网格地面")]
    void RecreateGrid()
    {
        GameObject existingGround = GameObject.Find("GridGround");
        if (existingGround != null)
        {
            DestroyImmediate(existingGround);
        }
        CreateGridGround();
    }
}
