%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8110264068976334655
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aa486462e6be1764e89c788ba30e61f7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_DiffusionProfileReferences:
  - {fileID: 11400000, guid: 8ae28b9a19c36784280380e4799243cb, type: 2}
  m_MaterialReferences: []
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Fern
  m_Shader: {fileID: -6465566751694194690, guid: dfa3b96612ed4df4a97b7fd06217ba23,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _DISABLE_DECALS
  - _DISABLE_SSR
  - _DISABLE_SSR_TRANSPARENT
  - _DOUBLESIDED_ON
  m_InvalidKeywords: []
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: 2450
  stringTagMap:
    MotionVector: User
    RenderType: TransparentCutout
  disabledShaderPasses:
  - MOTIONVECTORS
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  - ForwardEmissiveForDeferred
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - Terrain_Splatmap_0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_8713F080:
        m_Texture: {fileID: 2800000, guid: 55e94088b00bd354ba9333979ca1f8fa, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_9DCAAA49:
        m_Texture: {fileID: 2800000, guid: 6e96a13009e427342b5fb18df6af5f43, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_A5E0646:
        m_Texture: {fileID: 2800000, guid: aac60b0547f380d4db22446a7ad21c64, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_E1B0D043:
        m_Texture: {fileID: 2800000, guid: 7a5ed5fcf4e7c184ba836a7a11012f69, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2DLOD_4ce76fe7821749c5a93282810d6e42f2_Texture_1:
        m_Texture: {fileID: 2800000, guid: 02fd33ef9ed967d4aa71fb7beaf2e4df, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_cae4e27b30a24f34ab186f65b7b78038_Texture_1:
        m_Texture: {fileID: 2800000, guid: c6d8a0b57dd44104bb1595fe41471942, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Animation_Cutoff: 30
    - Distance_Fade_End: 140
    - Distance_Fade_Start: 40
    - Dither_Scale: 2.108
    - Enable_Blend_Terrain_Color: 0
    - Terrain_Blend_A: 0
    - Terrain_Blend_B: 0
    - Terrain_Blend_G: 0
    - Terrain_Blend_R: 0
    - Vector1_593c5cea6c4a42e993ed03ced4685732: 1
    - Vector1_8651797e3e304e108dbd25f9d5a426ba: 0.575
    - Vector1_a5b8b09028ce49a39f4d090894c89e22: 0.75
    - Vector1_a6983181c8dc4691ba6a28a34c4223a6: 2
    - Wind_Blast: 0.05
    - Wind_Intensity: 0.1
    - Wind_Ripples: 0.05
    - Wind_Speed: 3
    - Wind_Turbulence: 0
    - Wind_Wavelength: 10
    - Wind_Yaw: 180
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 1
    - _AlphaCutoffEnable: 1
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 1
    - _AlphaToMaskInspectorValue: 0
    - _Blend: 0
    - _BlendMode: 0
    - _BlendModePreserveSpecular: 0
    - _CastShadows: 1
    - _ConservativeDepthOffsetEnable: 0
    - _Cull: 0
    - _CullMode: 0
    - _CullModeForward: 0
    - _DepthOffsetEnable: 0
    - _DiffusionProfileHash: 3.128872
    - _DoubleSidedEnable: 1
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 1
    - _DstBlend: 0
    - _DstBlend2: 0
    - _ENVIRONMENTREFLECTIONS_OFF: 1
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _ExcludeFromTUAndAA: 0
    - _FadeBias: 0.5
    - _ForceForwardEmissive: 0
    - _GrassNormal: 0
    - _GroundFalloff: 0.2
    - _MaterialID: 1
    - _MaterialTypeMask: 2
    - _OpaqueCullMode: 2
    - _PerPixelSorting: 0
    - _QueueControl: 1
    - _QueueOffset: 0
    - _RayTracing: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 0
    - _ReceivesSSRTransparent: 0
    - _RefractionModel: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _SSS_Effect: 4
    - _SSS_Shadows: 0.75
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 32
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 9
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 15
    - _StencilWriteMaskMV: 41
    - _SupportDecals: 0
    - _Surface: 0
    - _SurfaceType: 0
    - _TransmissionEnable: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 0
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 3
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - _ZWriteControl: 0
    m_Colors:
    - Fade_Color: {r: 0, g: 0, b: 0, a: 0}
    - SplatA: {r: 0, g: 0, b: 0, a: 0}
    - SplatB: {r: 0, g: 0, b: 0, a: 0}
    - SplatG: {r: 0, g: 0, b: 0, a: 0}
    - SplatR: {r: 0, g: 0, b: 0, a: 0}
    - Terrain_Origin: {r: 0, g: 0, b: 0, a: 0}
    - Terrain_Scale: {r: 0, g: 0, b: 0, a: 0}
    - _AORemap: {r: 0.1, g: 1, b: 0, a: 0}
    - _BlendScale: {r: 2, g: 2, b: 0, a: 0}
    - _DiffusionProfileAsset: {r: -5.7855046e-23, g: -2.7243504e-36, b: -1.8891285e+22,
        a: -12817017}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _SSSColor: {r: 2, g: 2, b: 2, a: 1}
    - _Thickness_Remap: {r: 0.1, g: 1, b: 0, a: 0}
    - _blendOffset: {r: 1024, g: 1024, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
