{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "008e464d1407483787dffaab01b09453",
    "m_Properties": [
        {
            "m_Id": "3b97c5182780489686cf16f9de4a9ade"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "6a40bcea14de41c78d0077550410d558"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "de0f753e699d42e6bb14cb34f2f8d8ff"
        },
        {
            "m_Id": "58c9e1bc07d746628c0fd021c51e3ed0"
        },
        {
            "m_Id": "1a8e3945f7574132b4177e03dd4e812f"
        },
        {
            "m_Id": "fdb5ad442b4a491d9104f4eed39a04d6"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "98e71e6de21a442abbe2c02c9aaa33c2"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1a8e3945f7574132b4177e03dd4e812f"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "de0f753e699d42e6bb14cb34f2f8d8ff"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "58c9e1bc07d746628c0fd021c51e3ed0"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8e3945f7574132b4177e03dd4e812f"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "fdb5ad442b4a491d9104f4eed39a04d6"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a8e3945f7574132b4177e03dd4e812f"
                },
                "m_SlotId": 2
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 0,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "de0f753e699d42e6bb14cb34f2f8d8ff"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "0b12c17487c84f56ad6cef2c896fd699",
    "m_Id": 0,
    "m_DisplayName": "in_position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "in_position",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "1a8e3945f7574132b4177e03dd4e812f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SimpleHash (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -536.0,
            "y": -39.0000114440918,
            "width": 215.00001525878907,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0b12c17487c84f56ad6cef2c896fd699"
        },
        {
            "m_Id": "b0d73cf5a0f542218df14a2711519004"
        },
        {
            "m_Id": "d64b4c4761a8465e9ac332bdb321c2f7"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "SimpleHash",
    "m_FunctionSource": "",
    "m_FunctionBody": "uint X = asuint(in_position.x);\r\nuint Y = asuint(in_position.y);\nuint Z = asuint(in_position.z);\r\nuint H = X ^ 2747636419u;\nH *= 2654435769u;\r\nH >> 16;\r\nH *= 2654435769u;\r\nH ^= H >> 16;\r\nH *= 2654435769u;\nH ^= Y;\nH ^= Z;\r\nout_hash = asfloat(H / 4294967295.0);"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "3b97c5182780489686cf16f9de4a9ade",
    "m_Guid": {
        "m_GuidSerialized": "50baa2b4-4dc2-4d85-a5f4-17d889c4e867"
    },
    "m_Name": "seed",
    "m_DefaultRefNameVersion": 0,
    "m_RefNameGeneratedByDisplayName": "",
    "m_DefaultReferenceName": "Vector1_3b97c5182780489686cf16f9de4a9ade",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 274769.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4470d3ff9218494c89d32c220cea4794",
    "m_Id": 3,
    "m_DisplayName": "out_frac",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "out_frac",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ObjectNode",
    "m_ObjectId": "58c9e1bc07d746628c0fd021c51e3ed0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Object",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -650.2501220703125,
            "y": -39.000003814697269,
            "width": 94.50006103515625,
            "height": 75.75
        }
    },
    "m_Slots": [
        {
            "m_Id": "a4a7ca1268b14a57996ae89be29530f8"
        },
        {
            "m_Id": "7967521f173b49e2a7e807d77dcaeb2e"
        },
        {
            "m_Id": "c89ba0b93d6c43a0836d6247414e9e2c"
        },
        {
            "m_Id": "74db4887f5644f4da4a70803610e9e56"
        },
        {
            "m_Id": "c234f258f57c46959d3d62c804cea228"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "6a40bcea14de41c78d0077550410d558",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "3b97c5182780489686cf16f9de4a9ade"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "74db4887f5644f4da4a70803610e9e56",
    "m_Id": 3,
    "m_DisplayName": "World Bounds Max",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "World Bounds Max",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "7967521f173b49e2a7e807d77dcaeb2e",
    "m_Id": 1,
    "m_DisplayName": "Scale",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Scale",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "98e71e6de21a442abbe2c02c9aaa33c2",
    "m_Title": "RandomFromPosition",
    "m_Content": "Generate a simple hash based on the object space position. Returns a value beteen 0 and 1\n\n",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -532.0,
        "y": -148.0,
        "width": 223.57177734375,
        "height": 106.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "a4a7ca1268b14a57996ae89be29530f8",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b0d73cf5a0f542218df14a2711519004",
    "m_Id": 2,
    "m_DisplayName": "seed",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "seed",
    "m_StageCapability": 3,
    "m_Value": 1234567.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "bbed536826294f43b92ffb1f18c47807",
    "m_Id": 0,
    "m_DisplayName": "seed",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "c234f258f57c46959d3d62c804cea228",
    "m_Id": 4,
    "m_DisplayName": "Bounds Size",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Bounds Size",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "c89ba0b93d6c43a0836d6247414e9e2c",
    "m_Id": 2,
    "m_DisplayName": "World Bounds Min",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "World Bounds Min",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d64b4c4761a8465e9ac332bdb321c2f7",
    "m_Id": 1,
    "m_DisplayName": "out_hash",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "out_hash",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "de0f753e699d42e6bb14cb34f2f8d8ff",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -220.0,
            "y": -39.0,
            "width": 96.0,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "4470d3ff9218494c89d32c220cea4794"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "fdb5ad442b4a491d9104f4eed39a04d6",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -654.7501220703125,
            "y": 44.249996185302737,
            "width": 99.00006103515625,
            "height": 33.0000114440918
        }
    },
    "m_Slots": [
        {
            "m_Id": "bbed536826294f43b92ffb1f18c47807"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "3b97c5182780489686cf16f9de4a9ade"
    }
}

