using UnityEngine;

public class SuperFlatGreen : MonoBehaviour
{
    [Header("超平坦绿色地面设置")]
    public float worldSize = 500f;
    public Color grassColor = new Color(0.3f, 0.8f, 0.3f);
    public bool createInfiniteAppearance = true;
    
    void Start()
    {
        CreateSuperFlatWorld();
        SetupSkybox();
    }
    
    void CreateSuperFlatWorld()
    {
        // 创建主地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "SuperFlatGround";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(worldSize / 10f, 1f, worldSize / 10f);
        
        // 创建草地材质
        Material grassMaterial = new Material(Shader.Find("Standard"));
        grassMaterial.color = grassColor;
        grassMaterial.SetFloat("_Metallic", 0f);
        grassMaterial.SetFloat("_Glossiness", 0.1f);
        
        // 如果需要无限外观，设置纹理平铺
        if (createInfiniteAppearance)
        {
            grassMaterial.mainTextureScale = new Vector2(worldSize / 2f, worldSize / 2f);
        }
        
        ground.GetComponent<Renderer>().material = grassMaterial;
        
        // 确保有碰撞体
        MeshCollider collider = ground.GetComponent<MeshCollider>();
        if (collider == null)
        {
            collider = ground.AddComponent<MeshCollider>();
        }
        
        // 创建额外的地面块以获得真正的"无限"感觉
        if (createInfiniteAppearance)
        {
            CreateAdditionalGroundTiles(grassMaterial);
        }
    }
    
    void CreateAdditionalGroundTiles(Material grassMaterial)
    {
        float tileSize = worldSize;
        
        // 创建3x3网格的地面块
        for (int x = -1; x <= 1; x++)
        {
            for (int z = -1; z <= 1; z++)
            {
                if (x == 0 && z == 0) continue; // 跳过中心块（已经创建）
                
                GameObject tile = GameObject.CreatePrimitive(PrimitiveType.Plane);
                tile.name = $"GroundTile_{x}_{z}";
                tile.transform.position = new Vector3(x * tileSize, 0, z * tileSize);
                tile.transform.localScale = new Vector3(worldSize / 10f, 1f, worldSize / 10f);
                tile.GetComponent<Renderer>().material = grassMaterial;
                
                // 添加碰撞体
                MeshCollider tileCollider = tile.GetComponent<MeshCollider>();
                if (tileCollider == null)
                {
                    tile.AddComponent<MeshCollider>();
                }
            }
        }
    }
    
    void SetupSkybox()
    {
        // 设置简单的天空颜色
        if (Camera.main != null)
        {
            Camera.main.backgroundColor = new Color(0.5f, 0.8f, 1f); // 浅蓝色天空
            Camera.main.clearFlags = CameraClearFlags.SolidColor;
        }
        
        // 设置雾效果以增强无限感
        RenderSettings.fog = true;
        RenderSettings.fogColor = new Color(0.7f, 0.9f, 1f);
        RenderSettings.fogMode = FogMode.Linear;
        RenderSettings.fogStartDistance = worldSize * 0.7f;
        RenderSettings.fogEndDistance = worldSize * 1.2f;
    }
    
    // 在编辑器中实时更新
    void OnValidate()
    {
        if (Application.isPlaying)
        {
            // 清理现有地面
            GameObject[] existingGround = GameObject.FindGameObjectsWithTag("Untagged");
            foreach (GameObject obj in existingGround)
            {
                if (obj.name.Contains("Ground") || obj.name.Contains("Tile"))
                {
                    DestroyImmediate(obj);
                }
            }
            
            // 重新创建
            CreateSuperFlatWorld();
        }
    }
}
