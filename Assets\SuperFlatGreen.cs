using UnityEngine;

public class SuperFlatGreen : MonoBehaviour
{
    [Header("超平坦绿色地面设置")]
    public float worldSize = 500f;
    public Color grassColor = new Color(0.3f, 0.8f, 0.3f);
    public bool createInfiniteAppearance = true;
    
    void Start()
    {
        // 先清理可能存在的旧地面
        CleanupOldGround();
        CreateSuperFlatWorld();
        SetupSkybox();
    }

    void CleanupOldGround()
    {
        // 删除之前创建的地面对象
        GameObject[] oldGrounds = GameObject.FindGameObjectsWithTag("Untagged");
        foreach (GameObject obj in oldGrounds)
        {
            if (obj.name.Contains("SuperFlatGround") ||
                obj.name.Contains("GroundTile") ||
                obj.name.Contains("Ground"))
            {
                DestroyImmediate(obj);
            }
        }
    }
    
    void CreateSuperFlatWorld()
    {
        // 创建主地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "SuperFlatGround";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(worldSize / 10f, 1f, worldSize / 10f);
        
        // 创建草地材质 - 尝试多个着色器以确保兼容性
        Material grassMaterial = CreateCompatibleMaterial();
        grassMaterial.color = grassColor;
        
        // 如果需要无限外观，设置纹理平铺
        if (createInfiniteAppearance)
        {
            grassMaterial.mainTextureScale = new Vector2(worldSize / 2f, worldSize / 2f);
        }
        
        ground.GetComponent<Renderer>().material = grassMaterial;
        
        // 确保有碰撞体
        MeshCollider collider = ground.GetComponent<MeshCollider>();
        if (collider == null)
        {
            collider = ground.AddComponent<MeshCollider>();
        }
        
        // 创建额外的地面块以获得真正的"无限"感觉
        if (createInfiniteAppearance)
        {
            CreateAdditionalGroundTiles(grassMaterial);
        }
    }

    Material CreateCompatibleMaterial()
    {
        Material material = null;

        // 尝试不同的着色器，按优先级排序
        string[] shaderNames = {
            "Universal Render Pipeline/Lit",  // URP
            "Universal Render Pipeline/Simple Lit", // URP简化版
            "Standard", // 内置渲染管线
            "Mobile/Diffuse", // 移动端
            "Unlit/Color" // 最基础的着色器
        };

        foreach (string shaderName in shaderNames)
        {
            Shader shader = Shader.Find(shaderName);
            if (shader != null)
            {
                material = new Material(shader);
                Debug.Log($"使用着色器: {shaderName}");
                break;
            }
        }

        // 如果所有着色器都找不到，创建一个基础材质
        if (material == null)
        {
            material = new Material(Shader.Find("Sprites/Default"));
            Debug.LogWarning("使用默认着色器，可能显示不正确");
        }

        // 设置材质属性（如果支持的话）
        if (material.HasProperty("_Metallic"))
            material.SetFloat("_Metallic", 0f);
        if (material.HasProperty("_Glossiness"))
            material.SetFloat("_Glossiness", 0.1f);
        if (material.HasProperty("_Smoothness"))
            material.SetFloat("_Smoothness", 0.1f);

        return material;
    }

    void CreateAdditionalGroundTiles(Material grassMaterial)
    {
        float tileSize = worldSize;
        
        // 创建3x3网格的地面块
        for (int x = -1; x <= 1; x++)
        {
            for (int z = -1; z <= 1; z++)
            {
                if (x == 0 && z == 0) continue; // 跳过中心块（已经创建）
                
                GameObject tile = GameObject.CreatePrimitive(PrimitiveType.Plane);
                tile.name = $"GroundTile_{x}_{z}";
                tile.transform.position = new Vector3(x * tileSize, 0, z * tileSize);
                tile.transform.localScale = new Vector3(worldSize / 10f, 1f, worldSize / 10f);
                tile.GetComponent<Renderer>().material = grassMaterial;
                
                // 添加碰撞体
                MeshCollider tileCollider = tile.GetComponent<MeshCollider>();
                if (tileCollider == null)
                {
                    tile.AddComponent<MeshCollider>();
                }
            }
        }
    }
    
    void SetupSkybox()
    {
        // 设置简单的天空颜色
        if (Camera.main != null)
        {
            Camera.main.backgroundColor = new Color(0.5f, 0.8f, 1f); // 浅蓝色天空
            Camera.main.clearFlags = CameraClearFlags.SolidColor;
        }
        
        // 设置雾效果以增强无限感
        RenderSettings.fog = true;
        RenderSettings.fogColor = new Color(0.7f, 0.9f, 1f);
        RenderSettings.fogMode = FogMode.Linear;
        RenderSettings.fogStartDistance = worldSize * 0.7f;
        RenderSettings.fogEndDistance = worldSize * 1.2f;
    }
    
    // 在编辑器中实时更新
    void OnValidate()
    {
        if (Application.isPlaying)
        {
            // 清理现有地面
            GameObject[] existingGround = GameObject.FindGameObjectsWithTag("Untagged");
            foreach (GameObject obj in existingGround)
            {
                if (obj.name.Contains("Ground") || obj.name.Contains("Tile"))
                {
                    DestroyImmediate(obj);
                }
            }
            
            // 重新创建
            CreateSuperFlatWorld();
        }
    }
}
