Using pre-set license
Built from '1.6.1_update' branch; Version is '2022.3.61t2 (5a7d31f62760) revision 5930289'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 31968 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\UNRTIY\Tuan Hub\2022.3.61t2\Editor\Tuanjie.exe
-adb2
-batchMode
-noUpm
-disableFMOD
-name
AssetImportWorker1
-projectPath
D:/UNRTIY/One/A
-logFile
Logs/AssetImportWorker1.log
-srvPort
62075
Successfully changed project path to: D:/UNRTIY/One/A
D:/UNRTIY/One/A
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [37720]  Target information:

Player connection [37720]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1449812191 [EditorId] 1449812191 [Version] 1048832 [Id] WindowsEditor(7,lsy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [37720] Host joined multi-casting on [***********:54997]...
Player connection [37720] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 11.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61t2 (5a7d31f62760)
[Subsystems] Discovering subsystems at path D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/UNRTIY/One/A/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) 610M (ID=0x164e)
    Vendor:   ATI
    VRAM:     15984 MB
    Driver:   32.0.13034.2002
Initialize mono
Mono path[0] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Managed'
Mono path[1] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56752
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004248 seconds.
- Loaded All Assemblies, in  0.348 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.201 seconds
Domain Reload Profiling: 549ms
	BeginReloadAssembly (96ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (153ms)
		LoadAssemblies (96ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (149ms)
				TypeCache.ScanAssembly (133ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (202ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (164ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (110ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.692 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.555 seconds
Domain Reload Profiling: 1248ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (463ms)
		LoadAssemblies (341ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (205ms)
			TypeCache.Refresh (176ms)
				TypeCache.ScanAssembly (154ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (556ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (436ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 3.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5131 Unused Serialized files (Serialized files now loaded: 0)
Unloading 39 unused Assets / (0.8 MB). Loaded Objects now: 5599.
Memory consumption went from 213.5 MB to 212.8 MB.
Total: 7.968600 ms (FindLiveObjects: 0.989000 ms CreateObjectMapping: 0.975300 ms MarkObjects: 5.295500 ms  DeleteObjects: 0.708000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 23932.043748 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Both).prefab
  artifactKey: Guid(f4965e4a9c6c198479ee8ffc02cb0da6) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Both).prefab using Guid(f4965e4a9c6c198479ee8ffc02cb0da6) Importer(PreviewImporter) Launched and connected shader compiler TuanjieShaderCompiler.exe after 0.06 seconds
 -> (artifact id: '5439b0eeb1f467deef85bb1930c34f8b') in 0.378600 seconds
Number of asset objects unloaded after import = 27
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Forward).mat
  artifactKey: Guid(31a80cd389e57174a8be138b3a8f1529) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Forward).mat using Guid(31a80cd389e57174a8be138b3a8f1529) Importer(PreviewImporter)  -> (artifact id: 'ddb916100c90abbd674ad250a98a2da3') in 0.053010 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 702.982056 seconds.
  path: Assets/FirstPersonController.cs
  artifactKey: Guid(df5819929e2f53444b65f01152277897) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/FirstPersonController.cs using Guid(df5819929e2f53444b65f01152277897) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '384b6031264a9b179bf5ae592e6a522d') in 0.124431 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Settings/URP-Performant-Renderer.asset
  artifactKey: Guid(707360a9c581a4bd7aa53bfeb1429f71) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Settings/URP-Performant-Renderer.asset using Guid(707360a9c581a4bd7aa53bfeb1429f71) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '16d86f81e12fe65cc51a85ef919998f4') in 0.059924 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/fu-Wings/textures/spa_h.png
  artifactKey: Guid(8358faed764156a4fb8fb742802a12ea) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/spa_h.png using Guid(8358faed764156a4fb8fb742802a12ea) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'e6f48d4aa2373007f1504d7bd35b747f') in 0.045882 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Sample/ThetaRealtimeSkybox + 360CameraSettings.lighting
  artifactKey: Guid(ee928662a6a037245a74850c77515848) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/ThetaRealtimeSkybox + 360CameraSettings.lighting using Guid(ee928662a6a037245a74850c77515848) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '794fbd7917181de970cadb88648dec1a') in 0.015344 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Sample/ThetaRealtimeSkyboxSettings.lighting
  artifactKey: Guid(********************************) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/ThetaRealtimeSkyboxSettings.lighting using Guid(********************************) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '6cb080976d57fd4e54b9bb672b440819') in 0.009101 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Scenes/SampleScene.scene
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Scenes/SampleScene.scene using Guid(99c9720ab356a0642a771bea13969a05) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '404cc39239edd5e0ec6e91714354fba6') in 0.009923 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/fu-Wings/textures/hair.bmp
  artifactKey: Guid(ae35ca16db16a694295ec50cf8b10f5a) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/hair.bmp using Guid(ae35ca16db16a694295ec50cf8b10f5a) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '5099c76d3188a6d07b87c634c7965f7d') in 0.012170 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/SimpleGroundGenerator.cs
  artifactKey: Guid(151bbe13467c3c64aa7dbcd8c9ddb8e6) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/SimpleGroundGenerator.cs using Guid(151bbe13467c3c64aa7dbcd8c9ddb8e6) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '089c55092d2b9dd0ef34859313ebe7e4') in 0.007043 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Settings/SampleSceneProfile.asset
  artifactKey: Guid(a6560a915ef98420e9faacc1c7438823) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Settings/SampleSceneProfile.asset using Guid(a6560a915ef98420e9faacc1c7438823) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'af4b06cce18c68f8a546d6fc05a0dbe3') in 0.009794 seconds
Number of asset objects unloaded after import = 4
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/TutorialInfo/Layout.wlt
  artifactKey: Guid(eabc9546105bf4accac1fd62a63e88e6) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/TutorialInfo/Layout.wlt using Guid(eabc9546105bf4accac1fd62a63e88e6) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '26a93545f31f3e0d1ffa63dfb76b98f6') in 0.009141 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Sample/ThetaRealtimeSkybox + 360Camera.unity
  artifactKey: Guid(********************************) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/ThetaRealtimeSkybox + 360Camera.unity using Guid(********************************) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'd2bd372b2f98b90e0eeaa4618ab277cf') in 0.007847 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/360Camera/RenderTextures/Down.renderTexture
  artifactKey: Guid(7208864ddd0b5b14d80f7696e1024a91) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/RenderTextures/Down.renderTexture using Guid(7208864ddd0b5b14d80f7696e1024a91) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '293cb47a2f3314ce5cdd2ca2b4d8b63b') in 0.024370 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/fu-Wings/fu-White Wings.pmx
  artifactKey: Guid(9d1dc0bf6f0e329419513e62c1ee07f5) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/fu-White Wings.pmx using Guid(9d1dc0bf6f0e329419513e62c1ee07f5) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '6cc073191b3270b2f0c6ceb5df69cbc9') in 0.009598 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/fu-Wings/textures/cloth.png
  artifactKey: Guid(35211adce7b8f9b478bbacdd24550c5e) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/cloth.png using Guid(35211adce7b8f9b478bbacdd24550c5e) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'd203b57d6c5eb212e3ac4c8d9cb789de') in 0.014560 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Sample/360Camera.unity
  artifactKey: Guid(6fe363b6c20c53c4c9293a938bd41ab6) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/360Camera.unity using Guid(6fe363b6c20c53c4c9293a938bd41ab6) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '9303c610f7e1a8b694e0aea9b1015510') in 0.009146 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Sample/ThetaRealtimeEquirectanguler + 360Camera.unity
  artifactKey: Guid(a22b2318af0db9042992bc54adb0f051) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/ThetaRealtimeEquirectanguler + 360Camera.unity using Guid(a22b2318af0db9042992bc54adb0f051) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '79a003f597b3e951e65257cfea011bd7') in 0.007966 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Backward).mat
  artifactKey: Guid(143f383f14af1874cad10e528a0f97a7) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Backward).mat using Guid(143f383f14af1874cad10e528a0f97a7) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '9920c2590c1848b8cd74a70ad8ef24e7') in 0.014721 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Forward).mat
  artifactKey: Guid(31a80cd389e57174a8be138b3a8f1529) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Forward).mat using Guid(31a80cd389e57174a8be138b3a8f1529) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '745371a836c406351e88e3d1b6065ddb') in 0.010710 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/fu-Wings/textures/JP_SOX_48_Albedo.png
  artifactKey: Guid(b828a64648251b2448617edd95af63cf) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/JP_SOX_48_Albedo.png using Guid(b828a64648251b2448617edd95af63cf) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '485f6c93124dbb11720a530b5cfd03cd') in 0.014417 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/fu-Wings/textures/JP_TOP_292_Albedo.png
  artifactKey: Guid(ea6acc38578fe4945ac1e3a76ab1a243) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/JP_TOP_292_Albedo.png using Guid(ea6acc38578fe4945ac1e3a76ab1a243) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'f5f29293ac01e7aab4e8650a408d1347') in 0.025780 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/360Camera/360Camera.prefab
  artifactKey: Guid(1f9288d774b29a041b00a54fd07c8937) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/360Camera.prefab using Guid(1f9288d774b29a041b00a54fd07c8937) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '527fba5aa22c74d0be8e5d8385a3e05c') in 0.017602 seconds
Number of asset objects unloaded after import = 28
========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Settings/URP-HighFidelity.asset
  artifactKey: Guid(7b7fd9122c28c4d15b667c7040e3b3fd) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Settings/URP-HighFidelity.asset using Guid(7b7fd9122c28c4d15b667c7040e3b3fd) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '20376a1906abac8b4e3245eb0996eaf1') in 0.045494 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/ThetaRealtimeSkybox/ThetaRealtimeSkybox.shader
  artifactKey: Guid(********************************) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeSkybox/ThetaRealtimeSkybox.shader using Guid(********************************) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '61143006f1d3aba7f0073a6800b51d97') in 0.017477 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/fu-Wings/textures/toon_defo.bmp
  artifactKey: Guid(51c8bd2ba5c6ab3439f70bf33d80616c) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/toon_defo.bmp using Guid(51c8bd2ba5c6ab3439f70bf33d80616c) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '8b2eb47f2a79db5e1725747083e46c8f') in 0.015748 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 610.391970 seconds.
  path: Assets/textures/cloth.png
  artifactKey: Guid(d08aebc8064657a499f43857a18bddfe) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/cloth.png using Guid(d08aebc8064657a499f43857a18bddfe) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '012bef9b53ce29f9ed415e075a02c899') in 0.023755 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/textures/hair_s.bmp
  artifactKey: Guid(6c6d67044cddf174f92d25b59cb7f331) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/hair_s.bmp using Guid(6c6d67044cddf174f92d25b59cb7f331) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '2524da80376dfbacfa87a4d7458b49fd') in 0.024180 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/textures/skin.bmp
  artifactKey: Guid(764c09315340dd546ae62e7ba9ed7d30) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/skin.bmp using Guid(764c09315340dd546ae62e7ba9ed7d30) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '96ce008b521c3d774648f8cb771e558b') in 0.023453 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/textures/L.bmp
  artifactKey: Guid(f81a6af23f5288b4a97da7fb8c3cfcdc) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/L.bmp using Guid(f81a6af23f5288b4a97da7fb8c3cfcdc) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'b03e15a88ba84d52a51ab88d0eba56f2') in 0.046360 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/textures/肌.png
  artifactKey: Guid(f64e1551f05c97a489aa270bbce4be7f) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/肌.png using Guid(f64e1551f05c97a489aa270bbce4be7f) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'f3bf54ca07ae1ffb3cd0d5da10b81cdf') in 0.025503 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/textures/JP_TOP_292_Albedo.png
  artifactKey: Guid(bce00622cd6c9b849ac29c092a524404) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/JP_TOP_292_Albedo.png using Guid(bce00622cd6c9b849ac29c092a524404) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'f663a7125d0eb20038658327158c659a') in 0.022861 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 88.450348 seconds.
  path: Assets/textures/face_MikuAp_a.tga
  artifactKey: Guid(1bf6868c291dfbe4290f9808a65ba029) Importer(PreviewImporter)
Start importing Assets/textures/face_MikuAp_a.tga using Guid(1bf6868c291dfbe4290f9808a65ba029) Importer(PreviewImporter)  -> (artifact id: '1d9511579407901bfd3e82339e950ff0') in 0.020407 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/textures/JP_WING_4_Albedo.png
  artifactKey: Guid(2b40439c422ee9e4d8703d1562dd9697) Importer(PreviewImporter)
Start importing Assets/textures/JP_WING_4_Albedo.png using Guid(2b40439c422ee9e4d8703d1562dd9697) Importer(PreviewImporter)  -> (artifact id: '96a15c80f4bfad2b7d4cabc60ed2c940') in 0.037178 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/textures/体.png
  artifactKey: Guid(e947abcb854fe324aba26c222d8f8a1c) Importer(PreviewImporter)
Start importing Assets/textures/体.png using Guid(e947abcb854fe324aba26c222d8f8a1c) Importer(PreviewImporter)  -> (artifact id: '4dae60f367c58ec96b59b99f36147b02') in 0.023779 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000146 seconds.
  path: Assets/textures/颜.png
  artifactKey: Guid(c573fd14c3f572042ba6514197714829) Importer(PreviewImporter)
Start importing Assets/textures/颜.png using Guid(c573fd14c3f572042ba6514197714829) Importer(PreviewImporter)  -> (artifact id: 'b6a984d80b472090a70bd42cb412b0d9') in 0.026656 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000206 seconds.
  path: Assets/textures/JP_SH_237_Albedo.png
  artifactKey: Guid(add179d6cdfc3ba4987a44640caa287f) Importer(PreviewImporter)
Start importing Assets/textures/JP_SH_237_Albedo.png using Guid(add179d6cdfc3ba4987a44640caa287f) Importer(PreviewImporter)  -> (artifact id: 'f144ffba081c2ad1a9772f82adc1bd6e') in 0.048455 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 204.923835 seconds.
  path: Assets/fu-Wings/textures/face_MikuAp_a.tga
  artifactKey: Guid(961813eacc826174e85fa6920a7a76d4) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/face_MikuAp_a.tga using Guid(961813eacc826174e85fa6920a7a76d4) Importer(PreviewImporter)  -> (artifact id: 'c1d921e0c2957866132bea2e69e3793c') in 0.023250 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/fu-Wings/textures/颜.png
  artifactKey: Guid(6788d44680e2e0348935b4a4a63fbbd7) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/颜.png using Guid(6788d44680e2e0348935b4a4a63fbbd7) Importer(PreviewImporter)  -> (artifact id: '06c351639f1865fd26e7d6e223bc461e') in 0.020268 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.428 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.756 seconds
Domain Reload Profiling: 1184ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (184ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (757ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (375ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (42ms)
			ProcessInitializeOnLoadAttributes (276ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4985 Unused Serialized files (Serialized files now loaded: 0)
Unloading 34 unused Assets / (0.7 MB). Loaded Objects now: 5685.
Memory consumption went from 192.1 MB to 191.4 MB.
Total: 7.907700 ms (FindLiveObjects: 1.325200 ms CreateObjectMapping: 0.304400 ms MarkObjects: 5.958800 ms  DeleteObjects: 0.317500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
