{
	"introduction":
	"""The Shader Graph Production Ready Shaders sample is a collection of Shader Graph shader assets that are ready to be used out of the box or modified to suit your needs.  You can take them apart and learn from them, or just drop them directly into your project and use them as they are.
	""",


	"samples": [
		{
			"title": "URP Lit",
			"prefabName": "PlatformLitURP",
			"description":
			"""<ignore><link="URPLit.shadergraph">Open the example shader</link></ignore>
			
			Both URP and HDRP come with code-based shaders. The most commonly used shader for each of the SRPs is called Lit. For projects that use it, it’s often applied to just about every mesh in the game. Both the HDRP and URP versions of the Lit shader are very full-featured.  However, sometimes users want to add additional features to get just the look they’re trying to achieve, or remove unused features to optimize performance. For users who aren’t familiar with shader code, this can be very difficult.

			For that reason, we’ve included Shader Graph versions of the Lit shader for both URP and HDRP in this sample pack. Users can make a copy of the appropriate Shader Graph Lit shader, and then change any material that’s currently referencing the code version of the Lit shader with the Shader Graph version. All of the material settings will correctly be applied and continue to work.  They’ll then be able to make changes to the Shader Graph version as needed. Previously, this process required first building the Shader Graph version from scratch.

			Please note that most but not all of the features of the code-based shaders are duplicated in the Shader Graph versions. Some lesser-used features may be missing from the Shader Graph versions due to the differences in creating shader with Shader Graph vs creating them with code.

			Also note - If you’re going to use the Lit shader as is, we recommend sticking with the code version.  Only swap out the shader for the Shader Graph version if you’re making changes.  We also recommend removing unused features from the Shader Graph version for better performance.  For example, if you’re not using Emissive or Detail Maps, you can remove those parts of the shader (both graph nodes and Blackboard parameters) for faster build times and better performance. The real power of Shader Graph is its flexibility and how easy it is to change, update, and improve shaders. 
			"""
		},
		{
			"title": "HDRP Lit",
			"prefabName": "PlatformLitHDRP",
			"description":
			"""<ignore><link="HDRPLit.shadergraph">Open the example shader</link></ignore>
			
			Both URP and HDRP come with code-based shaders. The most commonly used shader for each of the SRPs is called Lit. For projects that use it, it’s often applied to just about every mesh in the game. Both the HDRP and URP versions of the Lit shader are very full-featured.  However, sometimes users want to add additional features to get just the look they’re trying to achieve, or remove unused features to optimize performance. For users who aren’t familiar with shader code, this can be very difficult.

			For that reason, we’ve included Shader Graph versions of the Lit shader for both URP and HDRP in this sample pack. Users can make a copy of the appropriate Shader Graph Lit shader, and then change any material that’s currently referencing the code version of the Lit shader with the Shader Graph version. All of the material settings will correctly be applied and continue to work.  They’ll then be able to make changes to the Shader Graph version as needed. Previously, this process required first building the Shader Graph version from scratch.

			Please note that most but not all of the features of the code-based shaders are duplicated in the Shader Graph versions. Some lesser-used features may be missing from the Shader Graph versions due to the differences in creating shader with Shader Graph vs creating them with code.

			Also note - If you’re going to use the Lit shader as is, we recommend sticking with the code version.  Only swap out the shader for the Shader Graph version if you’re making changes.  We also recommend removing unused features from the Shader Graph version for better performance.  For example, if you’re not using Emissive or Detail Maps, you can remove those parts of the shader (both graph nodes and Blackboard parameters) for faster build times and better performance. The real power of Shader Graph is its flexibility and how easy it is to change, update, and improve shaders. 
			"""
		},		
		{
			"title": "Material Projection Decal",
			"prefabName": "MatProj",
			"description":
			"""<ignore><link="DecalMaterialProjection.shadergraph">Open the example shader</link></ignore>
			
			This decal uses triplanar projection to project a material in 3D space. It projects materials correctly onto any mesh that intersects the decal volume.  It can be used to apply terrain materials on to other objects like rocks so that they blend in better with the terrain. 
			"""
		},
		{
			"title": "Water Caustics Decal",
			"prefabName": "WaterCaustics",
			"description":
			"""<ignore><link="DecalWaterCaustics.shadergraph">Open the example shader</link></ignore>
			
			When light shines through rippling water, the water warps and focuses the light, casting really interesting rippling patterns on surfaces under the water.  This shader creates these rippling caustic patterns. If you place decals using this shader under your water planes, you’ll get projected caustics that imitate the behavior of light shining through the water. 
			"""
		},
		{
			"title": "Water Wetness Decal",
			"prefabName": "WaterWetness",
			"description":
			"""<ignore><link="DecalWaterWetness.shadergraph">Open the example shader</link></ignore>
			
			The wetness decal makes surfaces look wet by darkening color and increasing smoothness. It uses very simple math and no texture samples so it is very performance efficient. It can be used along the banks of bodies of water to better integrate the water with the environment. 
			"""
		},
		{
			"title": "Running Water Decal",
			"prefabName": "RunningWater",
			"description":
			"""<ignore><link="DecalWaterRunning.shadergraph">Open the example shader</link></ignore>
			
			This decal creates the appearance of flowing water across whatever surfaces are inside the decal. It can be used on the banks of streams and around waterfalls to support the appearance of water flowing. With material parameters, you can control the speed of the water flow, the opacity of both the wetness and the water, and the strength of the flowing water normals. 
			"""
		},
		{
			"title": "Clover Terrain Details",
			"prefabName": "Clover",
			"description":
			"""<ignore><link="clover.shadergraph">Open the example shader</link></ignore>
			
			The shader for the clover uses just one single channel texture to reduce cost and save texture memory. Color is generated by lerping between a bright color and a dark color using the greyscale value from the texture. Each instance of clover uses a slightly different color variation.  The Instance ID node gives a unique value to each mesh instance and that’s used to give each instance a color variation.
			The Distance Mask is calculated in the vertex shader to save performance and then passed to the pixel shader where it’s combined with the texture and some screen space noise. Together, these elements are passed into the Fade Transition node which makes the mesh dissolve between the Clip Offset and Clip Distance values.
			"""
		},
		{
			"title": "Fern Details",
			"prefabName": "Ferns",
			"description":
			"""<ignore><link="Fern.shadergraph">Open the example shader</link></ignore>
			
			The fern shader uses a color, normal, and mask texture to define the fern material.  It animates the ferns based on wind settings. It also creates a subsurface scattering effect so that the fern fronds are illuminated on the reverse side from the sunlight. For ambient occlusion, we darken the AO close to the ground. As with the other detail shaders, we also dissolve the fern as we move away to prevent it from popping out.
			"""
		},
		{
			"title": "Nettle Details",
			"prefabName": "Nettle",
			"description":
			"""<ignore><link="TerrainNettle.shadergraph">Open the example shader</link></ignore>
			
			The nettle shader is for simple, broad-leaf undergrowth.  It’s a variation of the fern shader - so it has similar features.  The main difference is that it has been adapted to only use one texture sample to reduce both texture memory usage and shader cost. The texture has the normal X and Y in the red and green channels. The blue channel is a combination of the opacity and a grayscale mask that is used to modulate smoothness, AO, and color.
			"""
		},
		{
			"title": "Grass Details",
			"prefabName": "Grass",
			"description":
			"""<ignore><link="GrassWindTerrainDetails.shadergraph">Open the example shader</link></ignore>
			
			Usually, grass is created with billboards using a grass texture. This shader is different. We use mesh for each individual blade of grass. To keep the meshes as cheap as possible, our grass blade meshes have only 12 vertices and 10 triangles. They don’t have UV coordinates, normals, or vertex colors - so the only data stored in the mesh is position. The meshes are as simple as they can possibly be. We also do as much work as possible in the vertex shader for lower cost. Wind, color, translucency, and distance fade are all calculated in the vertex shader.
			The shader generates wind forces and then uses them to bend the blades of grass. The wind forces vary in direction and gust strength so the movement of the blades feels natural.
			"""
		},
		{
			"title": "Pebbles Details",
			"prefabName": "Pebbles",
			"description":
			"""<ignore><link="pebbles.shadergraph">Open the example shader</link></ignore>
			
			As with the rest of the detail shaders, the pebble shader is designed to be as cheap as possible. It only uses one small noise texture. It creates color variation using the noise texture and the instance IDs so that each pebble cluster has its own unique color. And it fades the pebbles out at a distance to prevent popping.
			"""
		},
		{
			"title": "Rock Shader",
			"prefabName": "PlatformRock",
			"description":
			"""<ignore><link="Rock.shadergraph">Open the example shader</link></ignore>
			
			This is a full-featured, modular rock shader that can be used for everything from small pebbles to boulders up to large cliff faces. It has features that can be turned on and off in the material depending on the application. Each of the features is encapsulated in a subgraph so it’s easy to remove features that you don’t need.  You can also add new features in the chain of modules if you need something else. Each module takes in color and smoothness in one input port and normal and ambient occlusion in a second input. Inside the subgraph, it alters these, and then it outputs the result again in the same format - color and smoothness in the first output port, and normal and AO in the second. Using this input/output port format keeps all of the modules organized and in a nice, neat line.

			To help with performance, the shader has an LOD0 boolean parameter exposed to the material. For materials applied to LOD0 of your rocks, this should be true.  For materials applied to the other LODs, this boolean should be false. This feature turns off extra features that are only visible when the rock is close so that non-LOD0 versions of your rocks render faster.

			Additionally, this shader branches using the Material Quality built-in enum keyword. This means that the shader is already set up to create a low quality, medium quality, and high quality version of itself depending on project settings. Features will be turned on and off, or different variations of features will be used depending on the project’s Material Quality setting.
			"""
		},
		{
			"title": "Water Lake",
			"prefabName": "Demo WaterLake",
			"description":
			"""<ignore><link="WaterLake.shadergraph">Open the example shader</link></ignore>
			
			This is the simplest water shader of the group.  Because it’s meant to be applied to larger bodies of water, it has two different sets of scrolling normals for the surface ripples - one large, one small - to break up the repetition of the ripples. It also fades the ripples out at a distance both to hide the tiling patterns and to give the lake a mirror finish at a distance.
			"""
		},
		{
			"title": "Water Animated",
			"prefabName": "Demo WaterSimpleFoamMask",
			"description":
			"""<ignore><link="WaterSimple_FoamMask.shadergraph">Open the example shader</link></ignore>
			
			This shader is intended to be used on ponds or other small bodies of non-flowing water. It uses 3 Gerstner waves subgraphs to animated the vertices in a chaotic wave pattern. It also adds foam around the edges of the water where it intersects with other objects. The unique thing about the foam implementation in this shader is that it allows you to additionally paint a texture mask that determines where foam can be placed manually. This manual placed foam could be used for a spot where a waterfall is hitting the water - for example.
			"""
		},
		{
			"title": "Water Stream Falls",
			"prefabName": "Demo WaterStreamFalls",
			"description":
			"""<ignore><link="WaterStreamFalls.shadergraph">Open the example shader</link></ignore>
			
			This is the same shader as the WaterStream, but it’s intended to be used on a waterfall mesh. It fades out at the start and the end of the mesh so that it can be blended in with the stream meshes at the top and bottom of the falls, and it adds foam where the falls are vertical.
			"""
		},
		{
			"title": "Water Stream",
			"prefabName": "Demo WaterStream",
			"description":
			"""<ignore><link="WaterStream.shadergraph">Open the example shader</link></ignore>
			
			The water stream shader is intended to be used on small, flowing bodies of water. Instead of standard scrolling normal maps for ripples, it uses flow mapping to make the water flow slowly along the edges of the stream and faster in the middle. It also uses the same animated foam technique as the WaterSimple shader - but without the mask.

			This shader uses the puddle_norm texture.  Notice that we save a little bit of shader performance by NOT storing this texture as a normal map. After the two samples are combined, then we expand the data to the -1 to 1 range, so we don’t have to do it twice.
			"""
		},
		{
			"title": "Rain",
			"prefabName": "DemoRain",
			"description":
			"""<ignore><link="RainExample.shadergraph">Open the example shader</link></ignore>
			
			The Rain subgraph combines all of the rain effect - drops, drips, puddles, wetness - to create a really nice rain weather effect - but it’s the most expensive on performance. Puddles are a bit expensive to generate as are drips, so this version should only be used on objects that will have both flat horizontal surfaces as well as vertical surfaces.
			"""
		},
		{
			"title": "Rain Floor",
			"prefabName": "DemoRainFloor",
			"description":
			"""<ignore><link="RainFloorExample.shadergraph">Open the example shader</link></ignore>
			
			The Rain Floor subgraph creates puddle and drop effects, but it does not have the drip effects that would run down vertical surfaces. This subgraph is best used for flat, horizontal surfaces.
			"""
		},
		{
			"title": "Rain Rocks",
			"prefabName": "DemoRainRocks",
			"description":
			"""<ignore><link="RainRocksExample.shadergraph">Open the example shader</link></ignore>
			
			The Rain Rocks subgraph has been specifically tuned for use on rocks. It includes drips and drops, but not puddles. It also includes the LOD0 parameter that is meant to turn off close-up features on LODs other than the first one.
			"""
		},
		{
			"title": "Rain Props",
			"prefabName": "DemoRainProps",
			"description":
			"""<ignore><link="RainPropsExample.shadergraph">Open the example shader</link></ignore>
			
			The Rain Props subgraph has the drop and drip effects but does not include the puddles.  It’s best for small prop objects.
			"""
		},
		{
			"title": "Snow",
			"prefabName": "DemoSnow",
			"description":
			"""<ignore><link="SnowExample.shadergraph">Open the example shader</link></ignore>
			
			The Snow subgraph creates a snow effect and applies it to the tops of objects. The snow material includes color, smoothness, normal, metallic, and emissive - where the emissive is used to apply sparkles to the snow.
			"""
		},
		{
			"title": "Blockout Grid",
			"prefabName": "DemoBlockout",
			"description":
			"""<ignore><link="BlockoutGrid.shadergraph">Open the example shader</link></ignore>
			
			Apply this simple shader to a 1 meter cube.  You can then scale and stretch the cube to block out your level.  The grid projected on the cube doesn't stretch but maintains its world-space projection so it's easy to see distances and heights.  It's a great way to block out traversable paths, obstacles, and level layouts. Turning on the “EnableSlopeWarning” parameter will shade meshes red where they’re too steep to traverse.
			"""
		},
		{
			"title": "Ice",
			"prefabName": "DemoIce",
			"description":
			"""<ignore><link="IceShader.shadergraph">Open the example shader</link></ignore>
			
			This ice shader uses up to three layers of parallax mapping to create the illusion that the cracks and bubbles are embedded in the volume of the ice below the surface though there is no transparency or actual volume. It also uses a Fresnel effect to brightness the edges and create a frosted look.
			"""
		},
		{
			"title": "1 - Sculpt Terrain",
			"prefabName": "TutorialStep1SculptTerrain",
			"description":
			"""
			We start by blocking out the main shapes of the terrain. We use the Set Height brush to create a sloping terrain by creating a series of terraces and then using the Smooth brush to smooth out the hard edges between the terraces.

			Then we cut in our stream channel with the Set Height brush in several different tiers heading down the slope. After cutting in the stream, we smooth out the hard edges using the Smooth brush.

			We finalize the terrain shape by adding polish using the Raise/Lower Height brush and the Smooth brush to add touch-ups and variety.  In this process, we start out with large brushes and end up using small ones.

			Once this step is done, we do revisit the terrain shape occasionally to add additional touch ups, especially after adding in the water meshes in steps 3 and 4, to ensure that the water meshes and terrain shape work together.
			"""
		},
		{
			"title": "2 - Paint Terrain Materials",
			"prefabName": "TutorialStep2TerrainMaterials",
			"description":
			"""
			Next, it’s time to add materials to our terrain.  We have four material layers - cobblestone rocks for our stream bed, dry dirt, rocky moss, and mossy grass. To apply the materials, we begin by establishing guidelines.  The stones material goes in the stream bed.  The dirt material goes along the banks of the stream. As a transition between the first and the grass, we use the rocky moss material. And finally, we use the grass material for the background.

			We first block in the materials according to our guidelines with large, hard-edged brushes. Then we go back and blend the materials together using smaller brushes. We paint one material over the other using brushes with a low opacity value to blend the two materials together.

			Even though our terrain materials exhibit tiling artifacts by themselves, we’re able to hide the tiling by giving each material a different tiling frequency. When the materials are blended, they break up each others tiling artifacts.  We also cover the terrain with detail meshes (step 7) which further hides the tiling.
			"""
		},
		{
			"title": "3 - Add Water Planes",
			"prefabName": "TutorialStep3WaterPlanes",
			"description":
			"""<ignore><link="WaterStream.shadergraph">Open the example shader</link></ignore>
			
			The stream itself is constructed from simple planes that are added to the scene by right clicking in the Hierarchy panel and selecting 3D Object->Plane. Then we apply the WaterStream material.  The planes are placed in the stream channel that’s cut into the terrain, and then scaled along the Z axis to stretch them along the length of the stream. Water flows in the local -Z direction of the planes. Planes are scaled as long as they need to be in order to reach from one stream height drop to the next.

			Notice that the edges of the stream mesh are transparent at the start and at the end.  This is to allow the stream meshes to blend together correctly with the waterfall meshes that link the stream planes together.
			"""
		},
		{
			"title": "4 - Add Waterfall Meshes",
			"prefabName": "TutorialStep4WaterfallMeshes",
			"description":
			"""<ignore><link="WaterStreamFalls.shadergraph">Open the example shader</link></ignore>
			
			The waterfall meshes are designed to connect one level of stream plane to the next lower level. They are placed at the end of a stream plane and slope down to connect to the next stream plane. We rotate the waterfall meshes around the Y axis to align the waterfall mesh between the two stream planes.

			The pivot point of the waterfall is lined up vertically with the top portion of the waterfall, so you can place the waterfall mesh at the exact same height as the top stream plane, and then scale the waterfall mesh so that the bottom portion of the waterfall mesh aligns with the lower stream plane.

			Notice that the Sorting Priority parameter in the Advanced Options of the material has been set to -1.  This makes the waterfall meshes draw behind the stream meshes so there isn’t a draw order conflict.
			"""
		},
		{
			"title": "5 - Add Rocks",
			"prefabName": "TutorialStep5AddRocks",
			"description":
			"""<ignore><link="Rock.shadergraph">Open the example shader</link></ignore>
			
			Streams are often filled with rocks that have been pushed by the current. To save memory and reduce draw calls, we’re just using two different rock meshes that both use the same texture set.  The rocks are rotated and scaled to give a variety of appearances. 
			
			Notice that we’ve created visual variety by creating two different sizes of rocks - large boulders, and smaller rocks. Overall, the rocks break up the shape of the stream and change the pattern of the foam on the water surface.
			"""
		},
		{
			"title": "6 - Add Water Decals",
			"prefabName": "TutorialStep6WaterDecals",
			"description":
			"""<ignore><link="DecalWaterCaustics.shadergraph">Open the example shader</link></ignore>
			
			We use the Water Wetness and Water Caustics decal to more tightly integrate the stream water with the terrain and rocks. The Wetness decal makes the terrain and other meshes around the stream look like they’re wet, and the Caustics decal imitates the appearance of lighting getting refracted by the surface of the water and getting focused in animated patterns on the bottom of the stream.

			For the Wetness decal, it should be created and scaled so that the top of the decal extends around half a meter above the surface of the water. The top of the Caustics decal should be just under the water.

			For both decals, the decal volumes should be kept as small as possible in all three dimensions - just large enough to cover their intended use and no larger. You can also save some performance by lowering the Draw Distance parameter on each decal so they are not drawn at a distance.
			"""
		},
		{
			"title": "7 - Add Reflection Probes",
			"prefabName": "TutorialStep7ReflectionProbes",
			"description":
			"""
			Reflections are a critical component of realistic-looking water. To improve the appearance of the water reflections, we create a Reflection Probe for each of the stream segments and place it at about head height and in the middle of the stream. If were are objects like rocks and trees nearby, they will be captured in the Reflection Probes and then reflected more accurately in the water.

			Especially notice how water to the right of this point is correctly reflecting the high bank behind the signs while water to the left is only reflecting the sky. This additional realism is contributed by the Reflection Probes.
			"""
		},
		{
			"title": "8 - Add Terrain Detail Meshes",
			"prefabName": "TutorialStep8TerrainDetailMeshes",
			"description":
			"""<ignore><link="GrassWindTerrainDetails.shadergraph">Open the example shader</link></ignore>
			
			Our last step is to add detail meshes to the terrain. We have pebble meshes that are added everywhere, including under the water. We have broad-leaf nettle plants that are added round the edges of the water in the dirt areas. We have ferns (3 variations) that are added just above the nettle in the transition between dirt and grass, and we have clover that is added in between the ferns and the grass. For the grass, we have three different meshes that each fade out at a different distance from the camera to soften the fade-out so that it doesn’t happen all at once. The most dense grass is only visible at 10 meters from the camera to improve performance. The three different grass layers are painted somewhat randomly with all three layers being applied where the terrain grass material is most dense and the most sparse grass being painted around the edges. Only one of the three grass meshes has shadows turned on - which gives the impression of grass shadows without paying the full performance cost.

			To save on performance, our terrain is set to fade out the detail meshes at 30 meters. This allows us to achieve a nice density of meshes up close and then get rid of them further away where they’re not as visible.  We hide the transition by dither fading the meshes in the shader before the 30 meter point so there’s not popping.

			"""
		}							
	]
}
