Using pre-set license
Built from '1.6.1_update' branch; Version is '2022.3.61t2 (5a7d31f62760) revision 5930289'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 31968 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\UNRTIY\Tuan Hub\2022.3.61t2\Editor\Tuanjie.exe
-adb2
-batchMode
-noUpm
-disableFMOD
-name
AssetImportWorker4
-projectPath
D:/UNRTIY/One/A
-logFile
Logs/AssetImportWorker4.log
-srvPort
62075
Successfully changed project path to: D:/UNRTIY/One/A
D:/UNRTIY/One/A
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [14316]  Target information:

Player connection [14316]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3699399316 [EditorId] 3699399316 [Version] 1048832 [Id] WindowsEditor(7,lsy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [14316] Host joined multi-casting on [***********:54997]...
Player connection [14316] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 5.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61t2 (5a7d31f62760)
[Subsystems] Discovering subsystems at path D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/UNRTIY/One/A/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) 610M (ID=0x164e)
    Vendor:   ATI
    VRAM:     15984 MB
    Driver:   32.0.13034.2002
Initialize mono
Mono path[0] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Managed'
Mono path[1] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56116
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003551 seconds.
- Loaded All Assemblies, in  0.232 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.161 seconds
Domain Reload Profiling: 393ms
	BeginReloadAssembly (61ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (107ms)
		LoadAssemblies (61ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (104ms)
				TypeCache.ScanAssembly (93ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (161ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (129ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (89ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.446 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.525 seconds
Domain Reload Profiling: 971ms
	BeginReloadAssembly (93ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (291ms)
		LoadAssemblies (198ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (142ms)
			TypeCache.Refresh (119ms)
				TypeCache.ScanAssembly (106ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (525ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (422ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (47ms)
			ProcessInitializeOnLoadAttributes (318ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 1.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5131 Unused Serialized files (Serialized files now loaded: 0)
Unloading 39 unused Assets / (0.8 MB). Loaded Objects now: 5599.
Memory consumption went from 215.5 MB to 214.7 MB.
Total: 3.693600 ms (FindLiveObjects: 0.397100 ms CreateObjectMapping: 0.095900 ms MarkObjects: 2.968200 ms  DeleteObjects: 0.232200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 25246.412015 seconds.
  path: Assets/textures
  artifactKey: Guid(7a86145f6b878474cb31a9891d25b69c) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures using Guid(7a86145f6b878474cb31a9891d25b69c) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'e03c8d05a66eff4628d54eb40d2b3719') in 0.170427 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 4.615129 seconds.
  path: Assets/fu-White Wings.pmx
  artifactKey: Guid(e585a7ba52b1ee145b13d92cdc9c1c8d) Importer(PreviewImporter)
Start importing Assets/fu-White Wings.pmx using Guid(e585a7ba52b1ee145b13d92cdc9c1c8d) Importer(PreviewImporter)  -> (artifact id: '1ec65188be4034a80142d652c003f002') in 0.020554 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 20.738860 seconds.
  path: Assets/360Camera
  artifactKey: Guid(1b08d6f94ab2ee1439b328e3e3e25e69) Importer(PreviewImporter)
Start importing Assets/360Camera using Guid(1b08d6f94ab2ee1439b328e3e3e25e69) Importer(PreviewImporter)  -> (artifact id: 'c5496555b238fc5ae8fb19d12a24d5bd') in 0.000476 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.537035 seconds.
  path: Assets/Sample
  artifactKey: Guid(e4084721aa340c2419fa1b3f1917a209) Importer(PreviewImporter)
Start importing Assets/Sample using Guid(e4084721aa340c2419fa1b3f1917a209) Importer(PreviewImporter)  -> (artifact id: '2f7726664ab949d6ea7e5e515de6d07c') in 0.000542 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.268388 seconds.
  path: Assets/Settings
  artifactKey: Guid(709f11a7f3c4041caa4ef136ea32d874) Importer(PreviewImporter)
Start importing Assets/Settings using Guid(709f11a7f3c4041caa4ef136ea32d874) Importer(PreviewImporter)  -> (artifact id: 'fc23d63ff66f9a2d95089cc26ce2dcc0') in 0.000493 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.517091 seconds.
  path: Assets/textures
  artifactKey: Guid(7a86145f6b878474cb31a9891d25b69c) Importer(PreviewImporter)
Start importing Assets/textures using Guid(7a86145f6b878474cb31a9891d25b69c) Importer(PreviewImporter)  -> (artifact id: 'e5f089d7eeadf95a30c9b1ed7e430ceb') in 0.000518 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.634025 seconds.
  path: Assets/360Camera/360Camera.prefab
  artifactKey: Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)
Start importing Assets/360Camera/360Camera.prefab using Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)  -> (artifact id: '98839f228647e7ee8c6b01d94f474c4e') in 0.021722 seconds
Number of asset objects unloaded after import = 28
========================================================================
Received Import Request.
  Time since last request: 34.984072 seconds.
  path: Assets/Scenes/SampleScene.scene
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(PreviewImporter)
Start importing Assets/Scenes/SampleScene.scene using Guid(99c9720ab356a0642a771bea13969a05) Importer(PreviewImporter)  -> (artifact id: 'e536f05db6543daaa01058203bf9ec60') in 0.000947 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.104093 seconds.
  path: Assets/Settings/URP-Balanced.asset
  artifactKey: Guid(e1260c1148f6143b28bae5ace5e9c5d1) Importer(PreviewImporter)
Start importing Assets/Settings/URP-Balanced.asset using Guid(e1260c1148f6143b28bae5ace5e9c5d1) Importer(PreviewImporter)  -> (artifact id: 'cbb95cdeac2548207c271f1a1980c9cf') in 0.003574 seconds
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.001632 seconds.
  path: Assets/Settings/URP-HighFidelity.asset
  artifactKey: Guid(7b7fd9122c28c4d15b667c7040e3b3fd) Importer(PreviewImporter)
Start importing Assets/Settings/URP-HighFidelity.asset using Guid(7b7fd9122c28c4d15b667c7040e3b3fd) Importer(PreviewImporter)  -> (artifact id: 'bdf0b5fcb05030461264a4323af07bc5') in 0.000396 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000432 seconds.
  path: Assets/Settings/URP-HighFidelity-Renderer.asset
  artifactKey: Guid(c40be3174f62c4acf8c1216858c64956) Importer(PreviewImporter)
Start importing Assets/Settings/URP-HighFidelity-Renderer.asset using Guid(c40be3174f62c4acf8c1216858c64956) Importer(PreviewImporter)  -> (artifact id: '48af593a1059813db34005d9b81baaf6') in 0.000566 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000614 seconds.
  path: Assets/Settings/URP-Performant.asset
  artifactKey: Guid(d0e2fc18fe036412f8223b3b3d9ad574) Importer(PreviewImporter)
Start importing Assets/Settings/URP-Performant.asset using Guid(d0e2fc18fe036412f8223b3b3d9ad574) Importer(PreviewImporter)  -> (artifact id: 'b884d680a2e93bcc0c91c3ff42a432f1') in 0.001711 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 2.769101 seconds.
  path: Assets/Scripts/FirstPersonSetupGuide.md
  artifactKey: Guid(db7b99b1bded00a40b51cd53dbabb5f9) Importer(PreviewImporter)
Start importing Assets/Scripts/FirstPersonSetupGuide.md using Guid(db7b99b1bded00a40b51cd53dbabb5f9) Importer(PreviewImporter)  -> (artifact id: 'f943ca9ed7113514e5258916ca977270') in 0.000960 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 6.474946 seconds.
  path: Assets/ThetaRealtimeSkybox/ThetaRealtimeSkybox.shader
  artifactKey: Guid(********************************) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeSkybox/ThetaRealtimeSkybox.shader using Guid(********************************) Importer(PreviewImporter)  -> (artifact id: '59535ab6ba657b604fba09323a5b6349') in 0.000737 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 7.113961 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectangular.shader
  artifactKey: Guid(91b8677abe2e60d48a8941053cf517ff) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectangular.shader using Guid(91b8677abe2e60d48a8941053cf517ff) Importer(PreviewImporter)  -> (artifact id: '5aa5cc961d8b42d83d82b2309e21798c') in 0.000818 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.648615 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/Editor
  artifactKey: Guid(b8a1d27ba928d914b946544820f03828) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler/Editor using Guid(b8a1d27ba928d914b946544820f03828) Importer(PreviewImporter)  -> (artifact id: 'f448ee3632fdc95fa6e62e5284caef30') in 0.000505 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.862375 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/Editor/ThetaRealtimeEquirectangulerMaker.cs
  artifactKey: Guid(9da74c6d48699c640b523a8bb842f9cf) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler/Editor/ThetaRealtimeEquirectangulerMaker.cs using Guid(9da74c6d48699c640b523a8bb842f9cf) Importer(PreviewImporter)  -> (artifact id: 'c436fad654c19d43894a6f1ede75f7fa') in 0.000435 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 3.111925 seconds.
  path: Assets/textures/5.png
  artifactKey: Guid(cc97e2c6463ed944fb0f0a80afc61e5f) Importer(PreviewImporter)
Start importing Assets/textures/5.png using Guid(cc97e2c6463ed944fb0f0a80afc61e5f) Importer(PreviewImporter) Launched and connected shader compiler TuanjieShaderCompiler.exe after 0.06 seconds
 -> (artifact id: 'd6a58be672ab01b662a4e9cf4a17a5f9') in 0.139144 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/textures/toon_defo.bmp
  artifactKey: Guid(a33ff4a521669e447ba7070120641143) Importer(PreviewImporter)
Start importing Assets/textures/toon_defo.bmp using Guid(a33ff4a521669e447ba7070120641143) Importer(PreviewImporter)  -> (artifact id: 'ba3ff9b4e97877a57096e109ff74df4b') in 0.027028 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 71.553346 seconds.
  path: Assets/fu-Wings
  artifactKey: Guid(8c8a8bea83b8ef849af7d45b221faaae) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings using Guid(8c8a8bea83b8ef849af7d45b221faaae) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '618a793e621d44282a28f477317bcf13') in 0.013214 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 3.423791 seconds.
  path: Assets/fu-Wings/RM.txt
  artifactKey: Guid(1bec28eafcc78444b8ce0c59faed019c) Importer(PreviewImporter)
Start importing Assets/fu-Wings/RM.txt using Guid(1bec28eafcc78444b8ce0c59faed019c) Importer(PreviewImporter)  -> (artifact id: '0c8ccdf4ab96b871a2ce9b801c0eb109') in 0.001118 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 129.385138 seconds.
  path: Assets/fu-Wings/textures
  artifactKey: Guid(6193d821ffaa069499882ec80f8e3906) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures using Guid(6193d821ffaa069499882ec80f8e3906) Importer(PreviewImporter)  -> (artifact id: '18e15b3d0dd5c03225d05f6ba659063d') in 0.000478 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.541299 seconds.
  path: Assets/fu-Wings/textures/5.png
  artifactKey: Guid(24fe00e32b22f4549a73dfe75b0479a3) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/5.png using Guid(24fe00e32b22f4549a73dfe75b0479a3) Importer(PreviewImporter)  -> (artifact id: 'd69335726542b13ea4cab141fe941234') in 0.009707 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/fu-Wings/textures/JP_SOX_48_Albedo.png
  artifactKey: Guid(b828a64648251b2448617edd95af63cf) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/JP_SOX_48_Albedo.png using Guid(b828a64648251b2448617edd95af63cf) Importer(PreviewImporter)  -> (artifact id: '630af7317ae3bc5097442c5bbedb072d') in 0.017860 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/fu-Wings/textures/髮.png
  artifactKey: Guid(babc8c206373298478af8fbf0c5b4c05) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/髮.png using Guid(babc8c206373298478af8fbf0c5b4c05) Importer(PreviewImporter)  -> (artifact id: '34a693f768d2fe22200ed6816b034ae0') in 0.020161 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 254.250450 seconds.
  path: Assets/CheckerboardGround.cs
  artifactKey: Guid(8e791aaeddeb6c447950df33d93d100b) Importer(PreviewImporter)
Start importing Assets/CheckerboardGround.cs using Guid(8e791aaeddeb6c447950df33d93d100b) Importer(PreviewImporter)  -> (artifact id: '45e5e746ed05bed0502b9256951e6cb4') in 0.000643 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.421125 seconds.
  path: Assets/ThetaRealtimeSkybox
  artifactKey: Guid(********************************) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeSkybox using Guid(********************************) Importer(PreviewImporter)  -> (artifact id: 'a712507e549135c7841007daeece3f6c') in 0.000571 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.727269 seconds.
  path: Assets/ThetaRealtimeEquirectanguler
  artifactKey: Guid(d608d93f785a8ba45b0986198bb5b097) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler using Guid(d608d93f785a8ba45b0986198bb5b097) Importer(PreviewImporter)  -> (artifact id: 'afd50a527ee4bd8f3d8f362330343ea6') in 0.000456 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.713562 seconds.
  path: Assets/Scripts
  artifactKey: Guid(7f5f470c1c7d4a64b9f8d2e3a1b0c9d8) Importer(PreviewImporter)
Start importing Assets/Scripts using Guid(7f5f470c1c7d4a64b9f8d2e3a1b0c9d8) Importer(PreviewImporter)  -> (artifact id: '7e5201b00518d7a3a5b3f5607910fda7') in 0.000512 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 21.133903 seconds.
  path: Assets/360Camera/360Camera.prefab
  artifactKey: Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)
Start importing Assets/360Camera/360Camera.prefab using Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)  -> (artifact id: '21316bf330075abd69e1ca22bb6652ac') in 0.005409 seconds
Number of asset objects unloaded after import = 29
========================================================================
Received Import Request.
  Time since last request: 0.977993 seconds.
  path: Assets/360Camera/360Camera.prefab
  artifactKey: Guid(1f9288d774b29a041b00a54fd07c8937) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/360Camera.prefab using Guid(1f9288d774b29a041b00a54fd07c8937) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '2daa3516c62d7f41262ed1df7563c6c9') in 0.032777 seconds
Number of asset objects unloaded after import = 29
========================================================================
Received Import Request.
  Time since last request: 0.118312 seconds.
  path: Assets/360Camera/360Camera.prefab
  artifactKey: Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)
Start importing Assets/360Camera/360Camera.prefab using Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)  -> (artifact id: '7ad05e8e05b203e0065d5d7e42280072') in 0.003437 seconds
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 0.404070 seconds.
  path: Assets/360Camera/360Camera.prefab
  artifactKey: Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)
Start importing Assets/360Camera/360Camera.prefab using Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)  -> (artifact id: '2e72017fc01249d6be49b412f4acc94b') in 0.003694 seconds
Number of asset objects unloaded after import = 31
========================================================================
Received Import Request.
  Time since last request: 0.922898 seconds.
  path: Assets/360Camera/360Camera.prefab
  artifactKey: Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)
Start importing Assets/360Camera/360Camera.prefab using Guid(1f9288d774b29a041b00a54fd07c8937) Importer(PreviewImporter)  -> (artifact id: 'c3a1ddbac6f3927e93a771648c1c7e6e') in 0.004110 seconds
Number of asset objects unloaded after import = 33
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.397 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.778 seconds
Domain Reload Profiling: 1176ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (186ms)
		LoadAssemblies (232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (778ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (377ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (49ms)
			ProcessInitializeOnLoadAttributes (270ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4985 Unused Serialized files (Serialized files now loaded: 0)
Unloading 34 unused Assets / (0.7 MB). Loaded Objects now: 5608.
Memory consumption went from 189.1 MB to 188.4 MB.
Total: 4.407900 ms (FindLiveObjects: 0.514900 ms CreateObjectMapping: 0.105600 ms MarkObjects: 3.455700 ms  DeleteObjects: 0.330600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 16.850666 seconds.
  path: Assets/360Camera/360CameraRealtimeEquirectanguler.shader
  artifactKey: Guid(09ece92e3778e07409066de7a6e4ca1d) Importer(PreviewImporter)
Start importing Assets/360Camera/360CameraRealtimeEquirectanguler.shader using Guid(09ece92e3778e07409066de7a6e4ca1d) Importer(PreviewImporter)  -> (artifact id: '556a30a5d4c028c59e8ef17952d1ba4d') in 0.010741 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 279.907152 seconds.
  path: Assets/Sample/360Camera.unity
  artifactKey: Guid(6fe363b6c20c53c4c9293a938bd41ab6) Importer(PreviewImporter)
Start importing Assets/Sample/360Camera.unity using Guid(6fe363b6c20c53c4c9293a938bd41ab6) Importer(PreviewImporter)  -> (artifact id: '969c7d3de57580492d63445bf0ace22d') in 0.000682 seconds
Number of asset objects unloaded after import = 0
