Using pre-set license
Built from '1.6.1_update' branch; Version is '2022.3.61t2 (5a7d31f62760) revision 5930289'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 31968 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\UNRTIY\Tuan Hub\2022.3.61t2\Editor\Tuanjie.exe
-adb2
-batchMode
-noUpm
-disableFMOD
-name
AssetImportWorker0
-projectPath
D:/UNRTIY/One/A
-logFile
Logs/AssetImportWorker0.log
-srvPort
62075
Successfully changed project path to: D:/UNRTIY/One/A
D:/UNRTIY/One/A
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [41920]  Target information:

Player connection [41920]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 530088251 [EditorId] 530088251 [Version] 1048832 [Id] WindowsEditor(7,lsy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [41920] Host joined multi-casting on [***********:54997]...
Player connection [41920] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 11.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61t2 (5a7d31f62760)
[Subsystems] Discovering subsystems at path D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/UNRTIY/One/A/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) 610M (ID=0x164e)
    Vendor:   ATI
    VRAM:     15984 MB
    Driver:   32.0.13034.2002
Initialize mono
Mono path[0] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Managed'
Mono path[1] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56904
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004580 seconds.
- Loaded All Assemblies, in  0.365 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.192 seconds
Domain Reload Profiling: 558ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (168ms)
		LoadAssemblies (105ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (162ms)
				TypeCache.ScanAssembly (141ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (193ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (153ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (103ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.689 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.558 seconds
Domain Reload Profiling: 1247ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (468ms)
		LoadAssemblies (350ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (201ms)
			TypeCache.Refresh (171ms)
				TypeCache.ScanAssembly (149ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (558ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (442ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 2.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5131 Unused Serialized files (Serialized files now loaded: 0)
Unloading 39 unused Assets / (0.8 MB). Loaded Objects now: 5599.
Memory consumption went from 213.5 MB to 212.8 MB.
Total: 7.298100 ms (FindLiveObjects: 1.077400 ms CreateObjectMapping: 0.397700 ms MarkObjects: 5.208000 ms  DeleteObjects: 0.613500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 23932.056674 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Forward and Backward).prefab
  artifactKey: Guid(b56bab3a929d05d469b2b9eec16c7298) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Forward and Backward).prefab using Guid(b56bab3a929d05d469b2b9eec16c7298) Importer(PreviewImporter) Launched and connected shader compiler TuanjieShaderCompiler.exe after 0.05 seconds
 -> (artifact id: 'a2df9d4440924fbb7ee6578ca4c6f032') in 0.419970 seconds
Number of asset objects unloaded after import = 34
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/ThetaRealtimeSkybox/ThetaRealtimeSkybox.mat
  artifactKey: Guid(********************************) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeSkybox/ThetaRealtimeSkybox.mat using Guid(********************************) Importer(PreviewImporter)  -> (artifact id: 'ea91fc31752e7b61150e0ca20c17962b') in 0.043085 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 702.940819 seconds.
  path: Assets/CursorManager.cs
  artifactKey: Guid(99a72555c7ed3ac488d82a861e1fae3c) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/CursorManager.cs using Guid(99a72555c7ed3ac488d82a861e1fae3c) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'b236e064a339412b567a9dedf9e81260') in 0.121101 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Sample/360CameraSettings.lighting
  artifactKey: Guid(402b198b5a6785347b2ea232e0b23c42) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/360CameraSettings.lighting using Guid(402b198b5a6785347b2ea232e0b23c42) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '61f54a86e02ed8c3e7a6c2ba0e8ed29e') in 0.027831 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Sample/ThetaRealtimeEquirectangulerSettings.lighting
  artifactKey: Guid(4c1361d0c26157d4a96f55c02eb3f95b) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/ThetaRealtimeEquirectangulerSettings.lighting using Guid(4c1361d0c26157d4a96f55c02eb3f95b) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '43637bd01bda591d27ffdca1f13de6d5') in 0.017406 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/fu-Wings/textures/hair_s.bmp
  artifactKey: Guid(6129b6b6b9eb46d4eb1de883940ce3f3) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/hair_s.bmp using Guid(6129b6b6b9eb46d4eb1de883940ce3f3) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'c2cd3ed179471adcc0eab296584b9a47') in 0.050002 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Forward and Backward).prefab
  artifactKey: Guid(b56bab3a929d05d469b2b9eec16c7298) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Forward and Backward).prefab using Guid(b56bab3a929d05d469b2b9eec16c7298) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '5df31a3d56a8ed033b6ac5d695fa70cd') in 0.016987 seconds
Number of asset objects unloaded after import = 12
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/360Camera/RenderTextures/Up.renderTexture
  artifactKey: Guid(cbefd9749a67292408601d62efdd595f) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/RenderTextures/Up.renderTexture using Guid(cbefd9749a67292408601d62efdd595f) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '619462867d53581c8be7436203878424') in 0.011462 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/360Camera/Editor/_360CameraMaker.cs
  artifactKey: Guid(d622b9e1398867d41b02012e78eb72bc) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/Editor/_360CameraMaker.cs using Guid(d622b9e1398867d41b02012e78eb72bc) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'ecc5077ce45fcbbd089048043ae262a2') in 0.009668 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/ThetaRealtimeSkybox/ThetaRealtimeSkybox.mat
  artifactKey: Guid(********************************) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeSkybox/ThetaRealtimeSkybox.mat using Guid(********************************) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '4439c02b700767c74fdf456208cf89ca') in 0.013288 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectangular.shader
  artifactKey: Guid(91b8677abe2e60d48a8941053cf517ff) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectangular.shader using Guid(91b8677abe2e60d48a8941053cf517ff) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'e0d07f34c05f88b4b4821120130ad579') in 0.009279 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Both).prefab
  artifactKey: Guid(f4965e4a9c6c198479ee8ffc02cb0da6) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Both).prefab using Guid(f4965e4a9c6c198479ee8ffc02cb0da6) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '9b6fb429f97216db5fa85a066b3ffd50') in 0.015735 seconds
Number of asset objects unloaded after import = 6
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Readme.asset
  artifactKey: Guid(8105016687592461f977c054a80ce2f2) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Readme.asset using Guid(8105016687592461f977c054a80ce2f2) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '7bb398efaf027b10bd5671407a08d773') in 0.017261 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/fu-Wings/textures/JP_WING_4_Albedo.png
  artifactKey: Guid(5eb602cbd5cfd364a8a93d0c6daf0593) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/JP_WING_4_Albedo.png using Guid(5eb602cbd5cfd364a8a93d0c6daf0593) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'e3963864eb92a5b39a6015e6df66740c') in 0.025493 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Settings/URP-HighFidelity-Renderer.asset
  artifactKey: Guid(c40be3174f62c4acf8c1216858c64956) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Settings/URP-HighFidelity-Renderer.asset using Guid(c40be3174f62c4acf8c1216858c64956) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'bfef39e618f4f4c646128c5a72b2d810') in 0.022638 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Both).mat
  artifactKey: Guid(bd5c643f4c19e0348b08b0d0624b7be1) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Both).mat using Guid(bd5c643f4c19e0348b08b0d0624b7be1) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '83939c0fd33773c89335f25c7f416efc') in 0.009965 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/TutorialInfo/Scripts/Readme.cs
  artifactKey: Guid(fcf7219bab7fe46a1ad266029b2fee19) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/TutorialInfo/Scripts/Readme.cs using Guid(fcf7219bab7fe46a1ad266029b2fee19) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '31f2550340fe5232ac5cc24f4151801a') in 0.010674 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/fu-Wings/textures/髮2.png
  artifactKey: Guid(96d520ed32858f84395a542c36cc2c6c) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/髮2.png using Guid(96d520ed32858f84395a542c36cc2c6c) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '762601b81ecb2eb49630b0508d126d36') in 0.016342 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/360Camera/RenderTextures/Front.renderTexture
  artifactKey: Guid(971d696d97b5528459f1c249eda58ee7) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/RenderTextures/Front.renderTexture using Guid(971d696d97b5528459f1c249eda58ee7) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '5e16d1d4af9aa27a8a090f80b11aaa16') in 0.009684 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/360Camera/RenderTextures/Back.renderTexture
  artifactKey: Guid(ed419a1e14ccc894386911e8810e7c3e) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/RenderTextures/Back.renderTexture using Guid(ed419a1e14ccc894386911e8810e7c3e) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '2972cb87b97f8d9ecf4f2faca1af9bfa') in 0.009074 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/fu-Wings/textures/颜.png
  artifactKey: Guid(6788d44680e2e0348935b4a4a63fbbd7) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/颜.png using Guid(6788d44680e2e0348935b4a4a63fbbd7) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '014e7e6dfda8138a16f20693048bf7fe') in 0.027970 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/Editor/ThetaRealtimeEquirectangulerMaker.cs
  artifactKey: Guid(9da74c6d48699c640b523a8bb842f9cf) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/ThetaRealtimeEquirectanguler/Editor/ThetaRealtimeEquirectangulerMaker.cs using Guid(9da74c6d48699c640b523a8bb842f9cf) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '217376f28e989fccbbe3cc2bc064031a') in 0.010008 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/fu-Wings/textures/5.png
  artifactKey: Guid(24fe00e32b22f4549a73dfe75b0479a3) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/5.png using Guid(24fe00e32b22f4549a73dfe75b0479a3) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '303704a46ae518c5c738d852b0b13fa5') in 0.014362 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '5148d3d8724dd220af8c44e65205fda8') in 0.014324 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/fu-Wings/textures/body00_MikuAp_b.tga
  artifactKey: Guid(4becb40fdf0db12418fbb37e5b4552e6) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/body00_MikuAp_b.tga using Guid(4becb40fdf0db12418fbb37e5b4552e6) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '079b7b1f44d61e4fbeb009e220bf789f') in 0.019878 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/360Camera/RenderTextures/Right.renderTexture
  artifactKey: Guid(a5024e9213597b544a4dfd3185d7990f) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/RenderTextures/Right.renderTexture using Guid(a5024e9213597b544a4dfd3185d7990f) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '4ba63763caa295661da358739115abd1') in 0.010758 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/360Camera/360CameraRealtimeEquirectanguler.prefab
  artifactKey: Guid(ac4d3be66d3d98047b290690ab56b254) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/360CameraRealtimeEquirectanguler.prefab using Guid(ac4d3be66d3d98047b290690ab56b254) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '4b2909b24af301ee43af425035c94c4d') in 0.013987 seconds
Number of asset objects unloaded after import = 6
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/fu-Wings/textures/肌.png
  artifactKey: Guid(1f45f228c5cb3974294a087b4a6e1eaa) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/肌.png using Guid(1f45f228c5cb3974294a087b4a6e1eaa) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '1aaf6cae83f273fcc1d6bb4b1b76bd92') in 0.017672 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 610.386093 seconds.
  path: Assets/textures/JP_SH_237_Albedo.png
  artifactKey: Guid(add179d6cdfc3ba4987a44640caa287f) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/JP_SH_237_Albedo.png using Guid(add179d6cdfc3ba4987a44640caa287f) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'dfbea9b2c133b12e4e5c0d01235f72cb') in 0.023839 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/textures/髮.png
  artifactKey: Guid(db784562b971436469568627455df02c) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/髮.png using Guid(db784562b971436469568627455df02c) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '1f36505331cc3f02717d9744fc3c1dd5') in 0.049659 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/fu-White Wings.pmx
  artifactKey: Guid(e585a7ba52b1ee145b13d92cdc9c1c8d) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-White Wings.pmx using Guid(e585a7ba52b1ee145b13d92cdc9c1c8d) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '68bcb446ac7bf8180155ca25be6d9f6c') in 0.016379 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/textures/5.png
  artifactKey: Guid(cc97e2c6463ed944fb0f0a80afc61e5f) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/5.png using Guid(cc97e2c6463ed944fb0f0a80afc61e5f) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '777c7e2ea1eff28fd6317c74042c18df') in 0.025391 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/textures/JP_WING_4_Albedo.png
  artifactKey: Guid(2b40439c422ee9e4d8703d1562dd9697) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/JP_WING_4_Albedo.png using Guid(2b40439c422ee9e4d8703d1562dd9697) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '474a98a61f737a1c71d247204be353b5') in 0.025324 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/textures/toon_defo.bmp
  artifactKey: Guid(a33ff4a521669e447ba7070120641143) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/toon_defo.bmp using Guid(a33ff4a521669e447ba7070120641143) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '3b102b31fa72e9c788936f5f534c0d8e') in 0.023344 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 30.340889 seconds.
  path: Assets/360Camera/360CameraRealtimeEquirectanguler.prefab
  artifactKey: Guid(ac4d3be66d3d98047b290690ab56b254) Importer(PreviewImporter)
Start importing Assets/360Camera/360CameraRealtimeEquirectanguler.prefab using Guid(ac4d3be66d3d98047b290690ab56b254) Importer(PreviewImporter)  -> (artifact id: 'd9d9bd3c99a0c6060957af17c6044ef7') in 0.030443 seconds
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 58.079541 seconds.
  path: Assets/textures/cloth.png
  artifactKey: Guid(d08aebc8064657a499f43857a18bddfe) Importer(PreviewImporter)
Start importing Assets/textures/cloth.png using Guid(d08aebc8064657a499f43857a18bddfe) Importer(PreviewImporter)  -> (artifact id: 'a666de44a626f0b76a84041ab963db29') in 0.021691 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/textures/L.bmp
  artifactKey: Guid(f81a6af23f5288b4a97da7fb8c3cfcdc) Importer(PreviewImporter)
Start importing Assets/textures/L.bmp using Guid(f81a6af23f5288b4a97da7fb8c3cfcdc) Importer(PreviewImporter)  -> (artifact id: 'cc2b7772f4d0a223db61dffb501b44f8') in 0.036414 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/textures/spa_h.png
  artifactKey: Guid(0daa777e7b1396e499732decae5c4230) Importer(PreviewImporter)
Start importing Assets/textures/spa_h.png using Guid(0daa777e7b1396e499732decae5c4230) Importer(PreviewImporter)  -> (artifact id: '833e7d5c993a36b92896e5576f98e6c2') in 0.025348 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000103 seconds.
  path: Assets/textures/髮2.png
  artifactKey: Guid(93095bf6aa5e4f647b224386e32f6ab2) Importer(PreviewImporter)
Start importing Assets/textures/髮2.png using Guid(93095bf6aa5e4f647b224386e32f6ab2) Importer(PreviewImporter)  -> (artifact id: '6dceaeaada061bb55fafc279ce29d850') in 0.025474 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000102 seconds.
  path: Assets/textures/肌.png
  artifactKey: Guid(f64e1551f05c97a489aa270bbce4be7f) Importer(PreviewImporter)
Start importing Assets/textures/肌.png using Guid(f64e1551f05c97a489aa270bbce4be7f) Importer(PreviewImporter)  -> (artifact id: '1f79af25af4b5909bc3aa3ae765efb59') in 0.048423 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 204.922475 seconds.
  path: Assets/fu-Wings/textures/cloth.png
  artifactKey: Guid(35211adce7b8f9b478bbacdd24550c5e) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/cloth.png using Guid(35211adce7b8f9b478bbacdd24550c5e) Importer(PreviewImporter)  -> (artifact id: 'cf4b424e9ab9d5fcc1e7ffa74d246348') in 0.009222 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/fu-Wings/textures/L.bmp
  artifactKey: Guid(d7a41d0a49fb2ae46995187786957fa3) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/L.bmp using Guid(d7a41d0a49fb2ae46995187786957fa3) Importer(PreviewImporter)  -> (artifact id: '8c2ca5023b7f6af028ab80a291727e26') in 0.014000 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/fu-Wings/textures/髮2.png
  artifactKey: Guid(96d520ed32858f84395a542c36cc2c6c) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/髮2.png using Guid(96d520ed32858f84395a542c36cc2c6c) Importer(PreviewImporter)  -> (artifact id: '885843ad8b72d40340601cb93fb91263') in 0.020005 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.424 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.766 seconds
Domain Reload Profiling: 1190ms
	BeginReloadAssembly (173ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (203ms)
		LoadAssemblies (251ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (23ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (766ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (384ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (50ms)
			ProcessInitializeOnLoadAttributes (282ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4985 Unused Serialized files (Serialized files now loaded: 0)
Unloading 34 unused Assets / (0.7 MB). Loaded Objects now: 5685.
Memory consumption went from 192.0 MB to 191.3 MB.
Total: 7.670600 ms (FindLiveObjects: 0.905900 ms CreateObjectMapping: 0.411600 ms MarkObjects: 5.907900 ms  DeleteObjects: 0.444000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
