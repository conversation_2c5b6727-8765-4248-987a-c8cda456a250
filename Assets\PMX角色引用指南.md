# PMX角色直接引用指南

## 🎯 现在FirstPersonController支持直接引用PMX角色！

### 新增功能：
- ✅ **直接引用角色模型**：无需转换格式
- ✅ **第一人称/第三人称切换**：按V键切换视角
- ✅ **智能显示控制**：第一人称时隐藏角色，第三人称时显示
- ✅ **自动设置**：自动调整角色位置和摄像机

## 🚀 设置步骤：

### 第一步：准备PMX角色
1. 即使PMX文件无法直接导入Unity
2. 你仍然可以通过其他方式获得角色GameObject：
   - 使用转换后的FBX文件
   - 使用其他格式的角色模型
   - 甚至使用Unity内置的角色

### 第二步：创建第一人称控制器
1. **层级视图**中右键 → **创建空对象**
2. 重命名为"PMXPlayer"
3. **添加组件**：
   - **Character Controller**
   - **FirstPersonController**脚本
   - **CursorManager**脚本

### 第三步：引用角色模型
1. 将你的角色模型（PMX转换后的或其他格式）拖拽到场景中
2. 在FirstPersonController的**检视面板**中：
   - 将角色模型拖拽到**Character Model**字段
   - 设置**Hide Model In First Person**（第一人称时隐藏模型）
   - 设置**Is Third Person**（是否第三人称视角）

### 第四步：配置参数
**角色控制器设置**：
- **中心**（Center）：(0, 1, 0)
- **半径**（Radius）：0.5
- **高度**（Height）：2

**摄像机设置**：
- **Third Person Offset**：(0, 2, -3) - 第三人称摄像机位置
- **Toggle View Key**：V - 切换视角的按键

### 第五步：设置摄像机
1. 将**主摄像机**拖拽到PMXPlayer下作为子对象
2. 摄像机位置会自动调整

## 🎮 控制说明：

### 基础控制：
- **WASD**：移动
- **鼠标**：视角控制
- **空格**：跳跃
- **左Shift**：跑步
- **ESC**：切换鼠标锁定

### 新增控制：
- **V键**：切换第一人称/第三人称视角

## 🎯 视角模式：

### 第一人称模式：
- 摄像机位置：角色眼睛位置
- 角色模型：隐藏（避免遮挡视线）
- 适合：射击、探索类游戏

### 第三人称模式：
- 摄像机位置：角色后方
- 角色模型：显示
- 适合：动作、冒险类游戏

## 💡 使用技巧：

### 1. 角色大小调整：
- 如果角色太大/太小，调整Character Controller的参数
- 或者调整角色模型的Scale

### 2. 摄像机位置微调：
- 修改**Third Person Offset**调整第三人称视角
- 第一人称视角固定在(0, 1.8, 0)

### 3. 兼容性：
- 支持任何格式的角色模型
- 不限于PMX，FBX、OBJ等都可以

## 🔧 故障排除：

### 问题1：角色模型不显示
- 检查Character Model字段是否正确引用
- 确保角色模型在场景中

### 问题2：摄像机位置不对
- 检查摄像机是否为PMXPlayer的子对象
- 调整Third Person Offset参数

### 问题3：角色控制不正常
- 检查Character Controller的Center和Height设置
- 确保角色模型的碰撞体设置正确

## 🎉 完成！
现在你可以使用任何角色模型作为第一人称控制器的角色，包括PMX角色！
