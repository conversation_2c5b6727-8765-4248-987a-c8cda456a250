%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1671820138640363827
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 26607311602891990}
  m_Layer: 0
  m_Name: ViewPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &26607311602891990
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1671820138640363827}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000938557, y: -0.98524684, z: 0.17105107, w: -0.005405843}
  m_LocalPosition: {x: -1.13, y: 2.4900017, z: 3.69}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3608749374209294103}
  m_LocalEulerAnglesHint: {x: 19.698, y: -180.629, z: 0}
--- !u!1 &6038242143246611039
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3608749374209294103}
  m_Layer: 0
  m_Name: TutorialStep4WaterfallMeshes
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3608749374209294103
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6038242143246611039}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -33, y: 0, z: -40}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 26607311602891990}
  - {fileID: 8238880412239631815}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &3680042658855434867
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 3608749374209294103}
    m_Modifications:
    - target: {fileID: 2102425495700333303, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_Name
      value: InfoPanel
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5035190720112997874, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_text
      value: 4 - Add Waterfall Meshes
      objectReference: {fileID: 0}
    - target: {fileID: 7809346879080935076, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
      propertyPath: m_text
      value: "The waterfall meshes are designed to connect one level of stream plane
        to the next lower level. They are placed at the end of a stream plane and
        slope down to connect to the next stream plane. We rotate the waterfall meshes
        around the Y axis to align the waterfall mesh between the two stream planes.\r\n\r\nThe
        pivot point of the waterfall is lined up vertically with the top portion
        of the waterfall, so you can place the waterfall mesh at the exact same height
        as the top stream plane, and then scale the waterfall mesh so that the bottom
        portion of the waterfall mesh aligns with the lower stream plane.\r\n \n\r\nNotice
        that the Sorting Priority parameter in the Advanced Options of the material
        has been set to -1.  This makes the waterfall meshes draw behind the stream
        meshes so there isn\u2019t a draw order conflict.\r\n"
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
--- !u!4 &8238880412239631815 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4702955764361932724, guid: c4058e53767c32445a7b7cb7c5bc3dad, type: 3}
  m_PrefabInstance: {fileID: 3680042658855434867}
  m_PrefabAsset: {fileID: 0}
