Using pre-set license
Built from '1.6.1_update' branch; Version is '2022.3.61t2 (5a7d31f62760) revision 5930289'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 31968 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\UNRTIY\Tuan Hub\2022.3.61t2\Editor\Tuanjie.exe
-adb2
-batchMode
-noUpm
-disableFMOD
-name
AssetImportWorker2
-projectPath
D:/UNRTIY/One/A
-logFile
Logs/AssetImportWorker2.log
-srvPort
62075
Successfully changed project path to: D:/UNRTIY/One/A
D:/UNRTIY/One/A
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [37992]  Target information:

Player connection [37992]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 976347833 [EditorId] 976347833 [Version] 1048832 [Id] WindowsEditor(7,lsy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [37992] Host joined multi-casting on [***********:54997]...
Player connection [37992] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 6.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61t2 (5a7d31f62760)
[Subsystems] Discovering subsystems at path D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/UNRTIY/One/A/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) 610M (ID=0x164e)
    Vendor:   ATI
    VRAM:     15984 MB
    Driver:   32.0.13034.2002
Initialize mono
Mono path[0] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/Managed'
Mono path[1] = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56304
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: D:/UNRTIY/Tuan Hub/2022.3.61t2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004128 seconds.
- Loaded All Assemblies, in  0.350 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.206 seconds
Domain Reload Profiling: 556ms
	BeginReloadAssembly (100ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (157ms)
		LoadAssemblies (100ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (152ms)
				TypeCache.ScanAssembly (137ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (206ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (170ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (116ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.684 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.569 seconds
Domain Reload Profiling: 1254ms
	BeginReloadAssembly (154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (467ms)
		LoadAssemblies (349ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (200ms)
			TypeCache.Refresh (171ms)
				TypeCache.ScanAssembly (149ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (570ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (452ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (50ms)
			ProcessInitializeOnLoadAttributes (337ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5131 Unused Serialized files (Serialized files now loaded: 0)
Unloading 39 unused Assets / (0.8 MB). Loaded Objects now: 5599.
Memory consumption went from 215.5 MB to 214.8 MB.
Total: 7.849400 ms (FindLiveObjects: 0.886900 ms CreateObjectMapping: 0.452300 ms MarkObjects: 5.838700 ms  DeleteObjects: 0.670600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 23932.085933 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Both).mat
  artifactKey: Guid(bd5c643f4c19e0348b08b0d0624b7be1) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Both).mat using Guid(bd5c643f4c19e0348b08b0d0624b7be1) Importer(PreviewImporter) Launched and connected shader compiler TuanjieShaderCompiler.exe after 0.05 seconds
 -> (artifact id: '5dfb1eeb6f3caa7cba24b828c248e6f7') in 0.390920 seconds
Number of asset objects unloaded after import = 21
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Backward).mat
  artifactKey: Guid(143f383f14af1874cad10e528a0f97a7) Importer(PreviewImporter)
Start importing Assets/ThetaRealtimeEquirectanguler/ThetaRealtimeEquirectanguler (Backward).mat using Guid(143f383f14af1874cad10e528a0f97a7) Importer(PreviewImporter)  -> (artifact id: '8763e30e51ed52b10fcdb8fb2ed67d18') in 0.035835 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 694.755775 seconds.
  path: Assets/fu-Wings
  artifactKey: Guid(8c8a8bea83b8ef849af7d45b221faaae) Importer(PreviewImporter)
Start importing Assets/fu-Wings using Guid(8c8a8bea83b8ef849af7d45b221faaae) Importer(PreviewImporter)  -> (artifact id: '78dd753f6fc3380474244ca1c641d18e') in 0.000463 seconds
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.941771 seconds.
  path: Assets/fu-Wings/fu-White Wings.pmx
  artifactKey: Guid(9d1dc0bf6f0e329419513e62c1ee07f5) Importer(PreviewImporter)
Start importing Assets/fu-Wings/fu-White Wings.pmx using Guid(9d1dc0bf6f0e329419513e62c1ee07f5) Importer(PreviewImporter)  -> (artifact id: '26037da073e6dfc1d5f68c8a8e2b76d5') in 0.001363 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 7.247865 seconds.
  path: Assets/CheckerboardGround.cs
  artifactKey: Guid(8e791aaeddeb6c447950df33d93d100b) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/CheckerboardGround.cs using Guid(8e791aaeddeb6c447950df33d93d100b) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'fbde7ae0293ba4a4a8e193d920529609') in 0.143285 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/fu-Wings/RM.txt
  artifactKey: Guid(1bec28eafcc78444b8ce0c59faed019c) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/RM.txt using Guid(1bec28eafcc78444b8ce0c59faed019c) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'd00f723a9671925f09fe2dc5d6bd947e') in 0.018839 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/360Camera/RenderTextures/Left.renderTexture
  artifactKey: Guid(9d8aa339dc32b234ca531968c91ecead) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/RenderTextures/Left.renderTexture using Guid(9d8aa339dc32b234ca531968c91ecead) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'b605e9a234a798afe39edaa34d214aeb') in 0.020788 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Settings/URP-Balanced.asset
  artifactKey: Guid(e1260c1148f6143b28bae5ace5e9c5d1) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Settings/URP-Balanced.asset using Guid(e1260c1148f6143b28bae5ace5e9c5d1) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '44cf538b96031e2b01e10c179cfdc3d6') in 0.054177 seconds
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/360Camera/360CameraRealtimeEquirectanguler.shader
  artifactKey: Guid(09ece92e3778e07409066de7a6e4ca1d) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/360CameraRealtimeEquirectanguler.shader using Guid(09ece92e3778e07409066de7a6e4ca1d) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'c03c1f06bcec676bf00130440e5493d2') in 0.009863 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Sample/ThetaRealtimeEquirectanguler.unity
  artifactKey: Guid(212555c2e515b034a8d4836fbf578832) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/ThetaRealtimeEquirectanguler.unity using Guid(212555c2e515b034a8d4836fbf578832) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '0fed4084b7f7734ec6daae4eaa87ceb2') in 0.008211 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Settings/URP-Balanced-Renderer.asset
  artifactKey: Guid(e634585d5c4544dd297acaee93dc2beb) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Settings/URP-Balanced-Renderer.asset using Guid(e634585d5c4544dd297acaee93dc2beb) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '2aab353a98c9f427515ced3e9a498f2f') in 0.021423 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/TutorialInfo/Icons/URP.png
  artifactKey: Guid(727a75301c3d24613a3ebcec4a24c2c8) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/TutorialInfo/Icons/URP.png using Guid(727a75301c3d24613a3ebcec4a24c2c8) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '126e21ff082f316de7d571364b37f4d0') in 0.042713 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/fu-Wings/textures/JP_SH_237_Albedo.png
  artifactKey: Guid(a60a28c4914209646a36ed074c38efe5) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/JP_SH_237_Albedo.png using Guid(a60a28c4914209646a36ed074c38efe5) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'cc16b5ba2dd3257df1153c074f08b2bd') in 0.015433 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Sample/ThetaRealtimeSkybox.unity
  artifactKey: Guid(********************************) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/ThetaRealtimeSkybox.unity using Guid(********************************) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '576147cbfc47bb36372140362113b80d') in 0.025729 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000099 seconds.
  path: Assets/fu-Wings/textures/face_MikuAp_a.tga
  artifactKey: Guid(961813eacc826174e85fa6920a7a76d4) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/face_MikuAp_a.tga using Guid(961813eacc826174e85fa6920a7a76d4) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '13ec9c8b69240fa5bd0aad248a642bf9') in 0.014667 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Sample/ThetaRealtimeEquirectanguler + 360CameraSettings.lighting
  artifactKey: Guid(1c62f7ae5f1dab8459e864822fbc305c) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Sample/ThetaRealtimeEquirectanguler + 360CameraSettings.lighting using Guid(1c62f7ae5f1dab8459e864822fbc305c) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'd7010af8bcba9161d4f77d13eeafd038') in 0.015931 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Scripts/FirstPersonSetupGuide.md
  artifactKey: Guid(db7b99b1bded00a40b51cd53dbabb5f9) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Scripts/FirstPersonSetupGuide.md using Guid(db7b99b1bded00a40b51cd53dbabb5f9) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'dc0c3da9c1d04347998d689b423417d7') in 0.008501 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/fu-Wings/textures/体.png
  artifactKey: Guid(a6ef07e22fb4a4c4abce2a42d39f27c1) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/体.png using Guid(a6ef07e22fb4a4c4abce2a42d39f27c1) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '7d37cc1c3e75a59abb3e38805598aead') in 0.014754 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/360Camera/360CameraRealtimeEquirectanguler.mat
  artifactKey: Guid(cec9216733561fe41b418d0910a4df62) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/360Camera/360CameraRealtimeEquirectanguler.mat using Guid(cec9216733561fe41b418d0910a4df62) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '09a94456dc827155b40b14de5f1a6846') in 0.014010 seconds
Number of asset objects unloaded after import = 7
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs
  artifactKey: Guid(476cc7d7cd9874016adc216baab94a0a) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/TutorialInfo/Scripts/Editor/ReadmeEditor.cs using Guid(476cc7d7cd9874016adc216baab94a0a) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'e1b3e7ecf0d8a6d0f8e8ad61df27c7ab') in 0.022039 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/fu-Wings/textures/L.bmp
  artifactKey: Guid(d7a41d0a49fb2ae46995187786957fa3) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/L.bmp using Guid(d7a41d0a49fb2ae46995187786957fa3) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '1fca817a1eb55ff474f551e3d12260e2') in 0.015625 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/fu-Wings/textures/skin.bmp
  artifactKey: Guid(c70a97ed78e1df2499b551f0781a7679) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/skin.bmp using Guid(c70a97ed78e1df2499b551f0781a7679) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '09cc4f4f86ab35291bf52370f37b47dd') in 0.018241 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Settings/URP-Performant.asset
  artifactKey: Guid(d0e2fc18fe036412f8223b3b3d9ad574) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/Settings/URP-Performant.asset using Guid(d0e2fc18fe036412f8223b3b3d9ad574) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '31f358767471df1c972f53a5dc713079') in 0.019664 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/fu-Wings/textures/髮.png
  artifactKey: Guid(babc8c206373298478af8fbf0c5b4c05) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures/髮.png using Guid(babc8c206373298478af8fbf0c5b4c05) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'e943893c1fe9d2d9ea257526103736f6') in 0.032632 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 610.396334 seconds.
  path: Assets/textures/hair.bmp
  artifactKey: Guid(ca99a94eb85e0384194ca504b96fa3eb) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/hair.bmp using Guid(ca99a94eb85e0384194ca504b96fa3eb) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '7825b5f8f15e6ec8b72299c7c2fedc01') in 0.023889 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/textures/体.png
  artifactKey: Guid(e947abcb854fe324aba26c222d8f8a1c) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/体.png using Guid(e947abcb854fe324aba26c222d8f8a1c) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '918e04d747eb9425cc5d9986f62ccc30') in 0.024604 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/textures/face_MikuAp_a.tga
  artifactKey: Guid(1bf6868c291dfbe4290f9808a65ba029) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/face_MikuAp_a.tga using Guid(1bf6868c291dfbe4290f9808a65ba029) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '9a31e9d1be46ef778aff981d44411b46') in 0.023193 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000104 seconds.
  path: Assets/textures/spa_h.png
  artifactKey: Guid(0daa777e7b1396e499732decae5c4230) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/spa_h.png using Guid(0daa777e7b1396e499732decae5c4230) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '2fcddfbe907f2765ac68e103d3637d1d') in 0.028494 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/textures/JP_SOX_48_Albedo.png
  artifactKey: Guid(3e49966b6797e3c4d8e05d2f540a0c59) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/JP_SOX_48_Albedo.png using Guid(3e49966b6797e3c4d8e05d2f540a0c59) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'a84ec49956c6f410523b01db2619c434') in 0.042653 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/textures/body00_MikuAp_b.tga
  artifactKey: Guid(afa62a6f97336a043a95674ab77f8549) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/textures/body00_MikuAp_b.tga using Guid(afa62a6f97336a043a95674ab77f8549) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: '608c0656c48e1d07f22deb8950d51780') in 0.022858 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 30.337302 seconds.
  path: Assets/360Camera/360CameraRealtimeEquirectanguler.mat
  artifactKey: Guid(cec9216733561fe41b418d0910a4df62) Importer(PreviewImporter)
Start importing Assets/360Camera/360CameraRealtimeEquirectanguler.mat using Guid(cec9216733561fe41b418d0910a4df62) Importer(PreviewImporter)  -> (artifact id: 'fa3957204079ab733c03f6b1fc383983') in 0.030617 seconds
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 58.078163 seconds.
  path: Assets/textures/body00_MikuAp_b.tga
  artifactKey: Guid(afa62a6f97336a043a95674ab77f8549) Importer(PreviewImporter)
Start importing Assets/textures/body00_MikuAp_b.tga using Guid(afa62a6f97336a043a95674ab77f8549) Importer(PreviewImporter)  -> (artifact id: '40157c05bf64b1f1641fa781d4fbb97f') in 0.023572 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/textures/JP_SOX_48_Albedo.png
  artifactKey: Guid(3e49966b6797e3c4d8e05d2f540a0c59) Importer(PreviewImporter)
Start importing Assets/textures/JP_SOX_48_Albedo.png using Guid(3e49966b6797e3c4d8e05d2f540a0c59) Importer(PreviewImporter)  -> (artifact id: '8ba833b8ad000195e056e864ccc45522') in 0.035935 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/textures/JP_TOP_292_Albedo.png
  artifactKey: Guid(bce00622cd6c9b849ac29c092a524404) Importer(PreviewImporter)
Start importing Assets/textures/JP_TOP_292_Albedo.png using Guid(bce00622cd6c9b849ac29c092a524404) Importer(PreviewImporter)  -> (artifact id: 'c3c8b6f8680722b20cb4dc773c75b205') in 0.024510 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000172 seconds.
  path: Assets/textures/skin.bmp
  artifactKey: Guid(764c09315340dd546ae62e7ba9ed7d30) Importer(PreviewImporter)
Start importing Assets/textures/skin.bmp using Guid(764c09315340dd546ae62e7ba9ed7d30) Importer(PreviewImporter)  -> (artifact id: 'eac0c70150c3af4382862aa277c069fb') in 0.026584 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/textures/髮.png
  artifactKey: Guid(db784562b971436469568627455df02c) Importer(PreviewImporter)
Start importing Assets/textures/髮.png using Guid(db784562b971436469568627455df02c) Importer(PreviewImporter)  -> (artifact id: '5e53ece0d6ae01bbec5bedef39694061') in 0.047882 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 71.553033 seconds.
  path: Assets/fu-Wings/textures
  artifactKey: Guid(6193d821ffaa069499882ec80f8e3906) Importer(ScriptedImporter:ASIEI03)
Start importing Assets/fu-Wings/textures using Guid(6193d821ffaa069499882ec80f8e3906) Importer(ScriptedImporter:ASIEI03)  -> (artifact id: 'eb4078b5ba9d7fa79acfe564fa22ff1c') in 0.013158 seconds
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 133.354119 seconds.
  path: Assets/fu-Wings/textures/body00_MikuAp_b.tga
  artifactKey: Guid(4becb40fdf0db12418fbb37e5b4552e6) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/body00_MikuAp_b.tga using Guid(4becb40fdf0db12418fbb37e5b4552e6) Importer(PreviewImporter)  -> (artifact id: '650b740829662570b9c42abf20daba4b') in 0.009288 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/fu-Wings/textures/JP_TOP_292_Albedo.png
  artifactKey: Guid(ea6acc38578fe4945ac1e3a76ab1a243) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/JP_TOP_292_Albedo.png using Guid(ea6acc38578fe4945ac1e3a76ab1a243) Importer(PreviewImporter)  -> (artifact id: '4d9624e55f82d36fd0fdc5904bb1111b') in 0.015935 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/fu-Wings/textures/toon_defo.bmp
  artifactKey: Guid(51c8bd2ba5c6ab3439f70bf33d80616c) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/toon_defo.bmp using Guid(51c8bd2ba5c6ab3439f70bf33d80616c) Importer(PreviewImporter)  -> (artifact id: 'e2a5e495e589e939fbb7abed0d61764f') in 0.008255 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/fu-Wings/textures/JP_SH_237_Albedo.png
  artifactKey: Guid(a60a28c4914209646a36ed074c38efe5) Importer(PreviewImporter)
Start importing Assets/fu-Wings/textures/JP_SH_237_Albedo.png using Guid(a60a28c4914209646a36ed074c38efe5) Importer(PreviewImporter)  -> (artifact id: '70b0fbf00eb2c78b0690a0ad772a4196') in 0.015172 seconds
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.423 seconds
Native extension for WindowsStandalone target not found
Native extension for WeixinMiniGame target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.758 seconds
Domain Reload Profiling: 1182ms
	BeginReloadAssembly (166ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (204ms)
		LoadAssemblies (253ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (23ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (759ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (377ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (42ms)
			ProcessInitializeOnLoadAttributes (278ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4985 Unused Serialized files (Serialized files now loaded: 0)
Unloading 34 unused Assets / (0.7 MB). Loaded Objects now: 5687.
Memory consumption went from 194.1 MB to 193.3 MB.
Total: 7.910600 ms (FindLiveObjects: 1.065600 ms CreateObjectMapping: 0.543200 ms MarkObjects: 5.839600 ms  DeleteObjects: 0.460900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: f1b99f4b41accd8c7519ffbcf00fecd9 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
