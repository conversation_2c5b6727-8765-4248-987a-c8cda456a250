{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "a805d50baeec43e688c68613cba85f5b",
    "m_Properties": [
        {
            "m_Id": "dd684fefff72468b9ad6923ed35df2e2"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "e6ef3e8653e54ab4b528b830c517ec15"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
        },
        {
            "m_Id": "20799078114c4b6db04ea1100b7eef61"
        },
        {
            "m_Id": "81597d504de34be494fa8f38fb30427c"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "905aaf5cdc4a4e16a258133cbf3c494a"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "20799078114c4b6db04ea1100b7eef61"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "81597d504de34be494fa8f38fb30427c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "20799078114c4b6db04ea1100b7eef61"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Procedural/Noise",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
    },
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "20799078114c4b6db04ea1100b7eef61",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Tchou23 (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1830.0001220703125,
            "y": -214.0,
            "width": 204.0,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "713775a318d94788abb76ac4fd0753c3"
        },
        {
            "m_Id": "4917cf32298946ed8ad49d83dbba2827"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "Tchou23",
    "m_FunctionSource": "",
    "m_FunctionBody": "//tchou23\r\nuint3 v;\r\n    v.xy = (uint2) (int2) round(p);\n    v.y ^= 1103515245U;\n    v.x += v.y;\n    v.x *= v.y;\n    v.x ^= v.x >> 5u;\n    v.x *= 0x27d4eb2du;\n    v.y ^= (v.x << 3u);\n    v.z = v.x ^ (v.y << 5u); \r\r\nOut = v * (1.0 / float(0xffffffff));"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "4917cf32298946ed8ad49d83dbba2827",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "713775a318d94788abb76ac4fd0753c3",
    "m_Id": 1,
    "m_DisplayName": "p",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "p",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "81597d504de34be494fa8f38fb30427c",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1916.0001220703125,
            "y": -171.0,
            "width": 86.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c58611acf8374c45b6562bc7094612c7"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "dd684fefff72468b9ad6923ed35df2e2"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "905aaf5cdc4a4e16a258133cbf3c494a",
    "m_Title": "",
    "m_Content": "Generates a random output value for every unique input value.\n\nThis one receives a Vec3 and outputs a Vec3.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1827.0001220703125,
        "y": -317.5000305175781,
        "width": 200.0,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "c58611acf8374c45b6562bc7094612c7",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "c5aca2b3b1dc453f815710fd4c3d676a",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "d47abf6eb8234e1c8deb90ae9dae0c62",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1624.0,
            "y": -214.00001525878907,
            "width": 85.333251953125,
            "height": 76.66667175292969
        }
    },
    "m_Slots": [
        {
            "m_Id": "c5aca2b3b1dc453f815710fd4c3d676a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector2ShaderProperty",
    "m_ObjectId": "dd684fefff72468b9ad6923ed35df2e2",
    "m_Guid": {
        "m_GuidSerialized": "b361e6ce-eb4f-4128-a0e1-e950fda3b6ca"
    },
    "m_Name": "In",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "In",
    "m_DefaultReferenceName": "_In",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "e6ef3e8653e54ab4b528b830c517ec15",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "dd684fefff72468b9ad6923ed35df2e2"
        }
    ]
}

